import re
from typing import Optional, List, Tuple
import torch
from torch.autograd import grad
from torch import nn, Tensor
from torch_scatter import scatter
from pytorch_lightning.utilities import rank_zero_warn
import warnings

from . import output_modules
from .wrappers import AtomFilter
from .. import priors


def create_model(args, prior_model=None, mean=None, std=None):
    shared_args = dict(
        hidden_channels=args['embedding_dimension'],
        num_layers=args['num_layers'],
        num_rbf=args['num_rbf'],
        rbf_type=args['rbf_type'],
        trainable_rbf=args['trainable_rbf'],
        activation=args['activation'],
        neighbor_embedding=args['neighbor_embedding'],
        cutoff_lower=args['cutoff_lower'],
        cutoff_upper=args['cutoff_upper'],
        max_z=args['max_z'],
        max_num_neighbors=args['max_num_neighbors'],
    )

    if args['model'] == 'QuinNet':
        from .quinnet import QuinNet_Module
        is_equivariant = True
        representation_model = QuinNet_Module(
            attn_activation=args['attn_activation'],
            num_heads=args['num_heads'],
            distance_influence=args['distance_influence'],
            **shared_args,
        )
    else:
        raise ValueError(f"Unknown architecture: {args['model']}")

    # atom filter
    if not args['derivative'] and args['atom_filter'] > -1:
        representation_model = AtomFilter(representation_model, args['atom_filter'])
    elif args['atom_filter'] > -1:
        raise ValueError("Derivative and atom filter can't be used together")

    # prior model
    if args['prior_model'] and prior_model is None:
        assert 'prior_args' in args, (f"Requested prior model {args['prior_model']} but the arguments are lacking the key 'prior_args'.")
        assert hasattr(priors, args['prior_model']), (f"Unknown prior model {args['prior_model']}. Available models are {', '.join(priors.__all__)}")
        # instantiate prior model if it was not passed to create_model (i.e. when loading a model)
        prior_model = getattr(priors, args['prior_model'])(**args['prior_args'])

    # create output network
    output_prefix = 'Equivariant' if is_equivariant else ''
    output_model = getattr(output_modules, output_prefix + args['output_model'])(args['embedding_dimension'], args['activation'])

    # combine representation and output network
    model = QuinNet(
        representation_model,
        output_model,
        prior_model=prior_model,
        reduce_op=args['reduce_op'],
        mean=mean,
        std=std,
        derivative=args['derivative'],
    )
    
    return model


def load_model(filepath, args=None, device='cpu', **kwargs):
    ckpt = torch.load(filepath, map_location='cpu')
    if args is None:
        args = ckpt['hyper_parameters']

    for key, value in kwargs.items():
        if not key in args:
            warnings.warn(f'Unknown hyperparameter: {key}={value}')

        args[key] = value

    model = create_model(args)

    state_dict = {re.sub(r'^model\.', '', k): v for k, v in ckpt['state_dict'].items()}
    model.load_state_dict(state_dict)
    return model.to(device)


class QuinNet(nn.Module):
    def __init__(
        self,
        representation_model,
        output_model,
        prior_model=None,
        reduce_op='add',
        mean=None,
        std=None,
        derivative=False,
    ):
        super(QuinNet, self).__init__()
        self.representation_model = representation_model
        self.output_model = output_model

        self.prior_model = prior_model
        if not output_model.allow_prior_model and prior_model is not None:
            self.prior_model = None
            rank_zero_warn(('Prior model was given but the output model does not allow prior models. Dropping the prior model.'))

        self.reduce_op = reduce_op
        self.derivative = derivative

        mean = torch.scalar_tensor(0) if mean is None else mean
        self.register_buffer('mean', mean)
        std = torch.scalar_tensor(1) if std is None else std
        self.register_buffer('std', std)

        self.reset_parameters()

    def reset_parameters(self):
        self.representation_model.reset_parameters()
        self.output_model.reset_parameters()
        if self.prior_model is not None:
            self.prior_model.reset_parameters()

    def forward(self,
                z: Tensor,
                pos: Tensor,
                batch: Optional[Tensor] = None,
                q: Optional[Tensor] = None,
                s: Optional[Tensor] = None,
                **kwargs) -> Tuple[Tensor, Optional[Tensor]]:
        assert z.dim() == 1 and z.dtype == torch.long
        batch = torch.zeros_like(z) if batch is None else batch

        if self.derivative:
            pos.requires_grad_(True)

        # run the potentially wrapped representation model
        x, v, z, pos, batch = self.representation_model(z, pos, batch, q=q, s=s)

        # apply the output network
        x = self.output_model.pre_reduce(x, v, z, pos, batch)

        # scale by data standard deviation
        if self.std is not None:
            x = x * self.std

        # apply prior model
        if self.prior_model is not None:
            x = self.prior_model(x, z, pos, batch)

        # aggregate atoms
        out = scatter(x, batch, dim=0, reduce=self.reduce_op)

        # shift by data mean
        if self.mean is not None:
            out = out + self.mean

        # apply output model after reduction
        out = self.output_model.post_reduce(out)

        # compute gradients with respect to coordinates
        if self.derivative:
            grad_outputs: List[Optional[torch.Tensor]] = [torch.ones_like(out)]
            dy = grad([out], [pos], grad_outputs=grad_outputs, create_graph=True, retain_graph=True)[0]
            if dy is None:
                raise RuntimeError('Autograd returned None for the force prediction.')
            
            return out, -dy
        
        # TODO: return only `out` once Union typing works with TorchScript (https://github.com/pytorch/pytorch/pull/53180)
        return out, None