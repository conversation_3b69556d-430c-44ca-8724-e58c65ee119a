from typing import Optional, <PERSON><PERSON>
import torch
from torch import Tensor, nn
from torch_geometric.nn import MessagePassing
from torch_scatter import scatter

from .utils import (
    NeighborEmbedding,
    EdgeEmbedding,
    CosineCutoff,
    Distance,
    Sphere,
    rbf_class_mapping,
    act_class_mapping,
)


class QuinNet_Module(nn.Module):
    r"""The TorchMD equivariant Transformer architecture.

    Args:
        hidden_channels (int, optional): Hidden embedding size.
            (default: :obj:`128`)
        num_layers (int, optional): The number of attention layers.
            (default: :obj:`6`)
        num_rbf (int, optional): The number of radial basis functions :math:`\mu`.
            (default: :obj:`50`)
        rbf_type (string, optional): The type of radial basis function to use.
            (default: :obj:`"expnorm"`)
        trainable_rbf (bool, optional): Whether to train RBF parameters with
            backpropagation. (default: :obj:`True`)
        activation (string, optional): The type of activation function to use.
            (default: :obj:`"silu"`)
        attn_activation (string, optional): The type of activation function to use
            inside the attention mechanism. (default: :obj:`"silu"`)
        neighbor_embedding (bool, optional): Whether to perform an initial neighbor
            embedding step. (default: :obj:`True`)
        num_heads (int, optional): Number of attention heads.
            (default: :obj:`8`)
        distance_influence (string, optional): Where distance information is used inside
            the attention mechanism. (default: :obj:`"both"`)
        cutoff_lower (float, optional): Lower cutoff distance for interatomic interactions.
            (default: :obj:`0.0`)
        cutoff_upper (float, optional): Upper cutoff distance for interatomic interactions.
            (default: :obj:`5.0`)
        max_z (int, optional): Maximum atomic number. Used for initializing embeddings.
            (default: :obj:`100`)
        max_num_neighbors (int, optional): Maximum number of neighbors to return for a
            given node/atom when constructing the molecular graph during forward passes.
            This attribute is passed to the torch_cluster radius_graph routine keyword
            max_num_neighbors, which normally defaults to 32. Users should set this to
            higher values if they are using higher upper distance cutoffs and expect more
            than 32 neighbors per node/atom.
            (default: :obj:`32`)
    """
    def __init__(
        self,
        hidden_channels=128,
        num_layers=6,
        num_rbf=50,
        rbf_type='expnorm',
        trainable_rbf=True,
        activation='silu',
        attn_activation='silu',
        neighbor_embedding=True,
        num_heads=8,
        distance_influence='both',
        cutoff_lower=0.0,
        cutoff_upper=5.0,
        max_z=100,
        max_num_neighbors=32,
    ):
        super(QuinNet_Module, self).__init__()

        assert distance_influence in ['keys', 'values', 'both', 'none']
        assert rbf_type in rbf_class_mapping, (f"Unknown RBF type '{rbf_type}'. Choose from {', '.join(rbf_class_mapping.keys())}.")
        assert activation in act_class_mapping, (f"Unknown activation function '{activation}'. Choose from {', '.join(act_class_mapping.keys())}.")
        assert attn_activation in act_class_mapping, (f"Unknown attention activation function '{attn_activation}'. Choose from {', '.join(act_class_mapping.keys())}.")

        self.hidden_channels = hidden_channels
        self.num_layers = num_layers
        self.num_rbf = num_rbf
        self.rbf_type = rbf_type
        self.trainable_rbf = trainable_rbf
        self.activation = activation
        self.attn_activation = attn_activation
        self.neighbor_embedding = neighbor_embedding
        self.num_heads = num_heads
        self.distance_influence = distance_influence
        self.cutoff_lower = cutoff_lower
        self.cutoff_upper = cutoff_upper
        self.max_z = max_z

        act_class = act_class_mapping[activation]

        self.embedding = nn.Embedding(self.max_z, hidden_channels)

        self.distance = Distance(cutoff_lower, cutoff_upper, max_num_neighbors=max_num_neighbors, return_vecs=True, loop=True)
        self.sphere = Sphere(l=2)
        self.distance_expansion = rbf_class_mapping[rbf_type](cutoff_lower, cutoff_upper, num_rbf, trainable_rbf)
        self.neighbor_embedding = NeighborEmbedding(hidden_channels, num_rbf, cutoff_lower, cutoff_upper, self.max_z).jittable() if neighbor_embedding else None
        
        self.edge_embedding = EdgeEmbedding(num_rbf, hidden_channels).jittable()

        self.attention_layers = nn.ModuleList()
        for _ in range(num_layers - 1):
            layer = EquivariantMultiHeadAttention(
                hidden_channels,
                distance_influence,
                num_heads,
                act_class,
                attn_activation,
                cutoff_lower,
                cutoff_upper,
                last_layer=False,
            ).jittable()
            self.attention_layers.append(layer)
        self.attention_layers.append(EquivariantMultiHeadAttention(
                hidden_channels,
                distance_influence,
                num_heads,
                act_class,
                attn_activation,
                cutoff_lower,
                cutoff_upper,
                last_layer=True,
            ).jittable())

        self.out_norm = nn.LayerNorm(hidden_channels)
        self.reset_parameters()

    def reset_parameters(self):
        self.embedding.reset_parameters()
        self.distance_expansion.reset_parameters()
        if self.neighbor_embedding is not None:
            self.neighbor_embedding.reset_parameters()

        self.edge_embedding.reset_parameters()
        for attn in self.attention_layers:
            attn.reset_parameters()

        self.out_norm.reset_parameters()

    def forward(self,
                z: Tensor,
                pos: Tensor,
                batch: Tensor,
                q: Optional[Tensor] = None,
                s: Optional[Tensor] = None,
                **kwargs
                ) -> Tuple[Tensor, Tensor, Tensor, Tensor, Tensor]:
        x = self.embedding(z)

        edge_index, edge_weight, edge_vec = self.distance(pos, batch)
        assert edge_vec is not None, 'Distance module did not return directional information'

        edge_attr = self.distance_expansion(edge_weight)
        mask = edge_index[0] != edge_index[1]
        edge_vec[mask] = edge_vec[mask] / torch.norm(edge_vec[mask], dim=1).unsqueeze(1)
        edge_vec = self.sphere(edge_vec)

        if self.neighbor_embedding is not None:
            x = self.neighbor_embedding(z, x, edge_index, edge_weight, edge_attr)

        vec = torch.zeros(x.size(0), 8, x.size(1), device=x.device)
        edge_attr = self.edge_embedding(edge_index, edge_attr, x)
        
        for attn in self.attention_layers[:-1]:
            dx, dvec, dedge_attr = attn(x, vec, edge_index, edge_weight, edge_attr, edge_vec)
            x = x + dx
            vec = vec + dvec
            edge_attr = edge_attr + dedge_attr

        dx, dvec, _ = self.attention_layers[-1](x, vec, edge_index, edge_weight, edge_attr, edge_vec)
        x = x + dx
        vec = vec + dvec
        
        x = self.out_norm(x)
        # vec = vec_layernorm(vec, max_min_norm)

        return x, vec, z, pos, batch

    def __repr__(self):
        return (
            f'{self.__class__.__name__}('
            f'hidden_channels={self.hidden_channels}, '
            f'num_layers={self.num_layers}, '
            f'num_rbf={self.num_rbf}, '
            f'rbf_type={self.rbf_type}, '
            f'trainable_rbf={self.trainable_rbf}, '
            f'activation={self.activation}, '
            f'attn_activation={self.attn_activation}, '
            f'neighbor_embedding={self.neighbor_embedding}, '
            f'num_heads={self.num_heads}, '
            f'distance_influence={self.distance_influence}, '
            f'cutoff_lower={self.cutoff_lower}, '
            f'cutoff_upper={self.cutoff_upper})'
        )


class EquivariantMultiHeadAttention(MessagePassing):
    def __init__(
        self,
        hidden_channels,
        distance_influence,
        num_heads,
        activation,
        attn_activation,
        cutoff_lower,
        cutoff_upper,
        last_layer=False,
    ):
        super(EquivariantMultiHeadAttention, self).__init__(aggr='add', node_dim=0)
        assert hidden_channels % num_heads == 0, f'The number of hidden channels ({hidden_channels}) must be evenly divisible by the number of attention heads ({num_heads})'

        self.distance_influence = distance_influence
        self.num_heads = num_heads
        self.hidden_channels = hidden_channels
        self.head_dim = hidden_channels // num_heads
        self.last_layer = last_layer

        self.layernorm = nn.LayerNorm(hidden_channels)
        self.act = activation()
        self.attn_activation = act_class_mapping[attn_activation]()
        self.cutoff = CosineCutoff(cutoff_lower, cutoff_upper)

        self.q_proj = nn.Linear(hidden_channels, hidden_channels)
        self.k_proj = nn.Linear(hidden_channels, hidden_channels)
        self.v_proj = nn.Linear(hidden_channels, hidden_channels)
        self.s_proj = nn.Linear(hidden_channels, hidden_channels * 2)

        self.f_proj1 = nn.Linear(hidden_channels, hidden_channels)
        self.f_proj2 = nn.Linear(hidden_channels, hidden_channels)
        self.f_proj3 = nn.Linear(hidden_channels, hidden_channels)

        self.src_proj = nn.Linear(hidden_channels, hidden_channels, bias=False)
        self.trg_proj = nn.Linear(hidden_channels, hidden_channels, bias=False)

        self.o_proj = nn.Linear(hidden_channels, hidden_channels * 3)

        self.vec_proj = nn.Linear(hidden_channels, hidden_channels * 3, bias=False)

        self.vec_proj_improper1 = nn.Linear(hidden_channels, hidden_channels, bias=False)
        self.vec_proj_improper2 = nn.Linear(hidden_channels, hidden_channels, bias=False)
        self.vec_proj_improper3 = nn.Linear(hidden_channels, hidden_channels, bias=False)
        self.vec_proj_improper4 = nn.Linear(hidden_channels, hidden_channels, bias=False)
        self.vec_proj_improper5 = nn.Linear(hidden_channels, hidden_channels, bias=False)

        self.src_proj1 = nn.Linear(hidden_channels, hidden_channels, bias=False)
        self.src_proj2 = nn.Linear(hidden_channels, hidden_channels, bias=False)
        self.dst_proj1 = nn.Linear(hidden_channels, hidden_channels, bias=False)

        self.ratio = nn.Parameter(torch.ones(6, ))

        self.dk_proj = None
        if distance_influence in ['keys', 'both']:
            self.dk_proj = nn.Linear(hidden_channels, hidden_channels)

        self.dv_proj = None
        if distance_influence in ['values', 'both']:
            self.dv_proj = nn.Linear(hidden_channels, hidden_channels)

        self.reset_parameters()
        
    def vector_rejection(self, vec, d_ij):
        vec_proj = (vec * d_ij).sum(dim=1, keepdim=True)
        return vec - vec_proj * d_ij

    def reset_parameters(self):
        self.layernorm.reset_parameters()
        nn.init.xavier_uniform_(self.q_proj.weight)
        self.q_proj.bias.data.fill_(0)
        nn.init.xavier_uniform_(self.k_proj.weight)
        self.k_proj.bias.data.fill_(0)
        nn.init.xavier_uniform_(self.v_proj.weight)
        self.v_proj.bias.data.fill_(0)
        nn.init.xavier_uniform_(self.o_proj.weight)
        self.o_proj.bias.data.fill_(0)
        nn.init.xavier_uniform_(self.s_proj.weight)
        self.s_proj.bias.data.fill_(0)
        
        nn.init.xavier_uniform_(self.f_proj1.weight)
        self.f_proj1.bias.data.fill_(0)
        nn.init.xavier_uniform_(self.f_proj2.weight)
        self.f_proj2.bias.data.fill_(0)
        nn.init.xavier_uniform_(self.f_proj3.weight)
        self.f_proj3.bias.data.fill_(0)
        nn.init.xavier_uniform_(self.src_proj.weight)
        nn.init.xavier_uniform_(self.trg_proj.weight)

        nn.init.xavier_uniform_(self.vec_proj.weight)
        if self.dk_proj:
            nn.init.xavier_uniform_(self.dk_proj.weight)
            self.dk_proj.bias.data.fill_(0)

        if self.dv_proj:
            nn.init.xavier_uniform_(self.dv_proj.weight)
            self.dv_proj.bias.data.fill_(0)

    def forward(self, x, vec, edge_index, r_ij, f_ij, d_ij):
        x = self.layernorm(x)
        q = self.q_proj(x).reshape(-1, self.num_heads, self.head_dim)
        k = self.k_proj(x).reshape(-1, self.num_heads, self.head_dim)
        v = self.v_proj(x).reshape(-1, self.num_heads, self.head_dim)

        vec1, vec2, vec3 = torch.split(self.vec_proj(vec), self.hidden_channels, dim=-1)
        vec_dot1 = (vec1 * vec2).sum(dim=1)

        normal_vec1 = self.vector_rejection(self.vec_proj_improper1(vec), self.vec_proj_improper2(vec))
        vec_dot2 = (normal_vec1 ** 2).sum(dim=1)

        normal_vec2 = self.vector_rejection(self.vec_proj_improper3(vec), self.vec_proj_improper4(vec))
        vec_dot6 = (normal_vec2 * self.vec_proj_improper5(vec)).sum(dim=1)

        dst, src = edge_index
        vec_dot3_ = self.act(self.f_proj2(f_ij)) * (self.dst_proj1(normal_vec1[dst]) * self.src_proj1(normal_vec1[src])).sum(dim=1)
        vec_dot3 = scatter(vec_dot3_, dst, dim=0, dim_size=vec_dot1.size(0))

        vec_dot4_ = self.act(self.f_proj3(f_ij)) * (self.src_proj2(normal_vec1[src]) ** 2).sum(dim=1)
        vec_dot4 = scatter(vec_dot4_, dst, dim=0, dim_size=vec_dot1.size(0))

        dk = (
            self.act(self.dk_proj(f_ij)).reshape(-1, self.num_heads, self.head_dim)
            if self.dk_proj is not None
            else None
        )
        dv = (
            self.act(self.dv_proj(f_ij)).reshape(-1, self.num_heads, self.head_dim)
            if self.dv_proj is not None
            else None
        )

        # propagate_type: (q: Tensor, k: Tensor, v: Tensor, vec: Tensor, dk: Tensor, dv: Tensor, r_ij: Tensor, d_ij: Tensor)
        x, vec_out = self.propagate(edge_index, q=q, k=k, v=v, vec=vec, dk=dk, dv=dv, r_ij=r_ij, d_ij=d_ij, size=None)
        vec_dot5_ = self.edge_updater(edge_index, vec=vec, d_ij=d_ij, f_ij=f_ij)
        vec_dot5 = scatter(vec_dot5_, dst, dim=0, dim_size=vec_dot1.size(0))

        # edge_updater_type: (vec: Tensor, d_ij: Tensor, f_ij: Tensor)
        o1, o2, o3 = torch.split(self.o_proj(x), self.hidden_channels, dim=1)
        dx = (torch.stack([vec_dot1, vec_dot2, vec_dot3, vec_dot4, vec_dot5, vec_dot6]) * self.ratio[:, None, None]).sum(dim=0) * o2 + o3
        dvec = vec3 * o1.unsqueeze(1) + vec_out
        if not self.last_layer:
            return dx, dvec, vec_dot5_ + vec_dot3_ + vec_dot4_
        else:
            return dx, dvec, None

    def message(self, q_i, k_j, v_j, vec_j, dk, dv, r_ij, d_ij):
        # attention mechanism
        if dk is None:
            attn = (q_i * k_j).sum(dim=-1)
        else:
            attn = (q_i * k_j * dk).sum(dim=-1)

        # attention activation function
        attn = self.attn_activation(attn) * self.cutoff(r_ij).unsqueeze(1)

        # value pathway
        if dv is not None:
            v_j = v_j * dv

        # update scalar features
        v_j = (v_j * attn.unsqueeze(2)).view(-1, self.hidden_channels)

        s1, s2 = torch.split(self.act(self.s_proj(v_j)), self.hidden_channels, dim=1)
        
        # update vector features
        vec = vec_j * s1.unsqueeze(1) + s2.unsqueeze(1) * d_ij.unsqueeze(2)
        
        return v_j, vec
    
    def edge_update(self, vec_i, vec_j, d_ij, f_ij):
        w1 = self.vector_rejection(self.trg_proj(vec_i), d_ij.unsqueeze(2))
        w2 = self.vector_rejection(self.src_proj(vec_j), -d_ij.unsqueeze(2))
        w_dot = (w1 * w2).sum(dim=1)
        return self.act(self.f_proj1(f_ij)) * w_dot

    def aggregate(
        self,
        features: Tuple[torch.Tensor, torch.Tensor],
        index: torch.Tensor,
        ptr: Optional[torch.Tensor],
        dim_size: Optional[int],
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        x, vec = features
        x = scatter(x, index, dim=self.node_dim, dim_size=dim_size)
        vec = scatter(vec, index, dim=self.node_dim, dim_size=dim_size)
        return x, vec