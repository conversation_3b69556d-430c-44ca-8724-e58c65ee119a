"""
Scaffold数据集加载器，支持预定义的训练/验证/测试划分
"""

import os
import numpy as np
import torch
from torch_geometric.data import Data, InMemoryDataset
from typing import Optional, Callable, List


class ScaffoldDataset(InMemoryDataset):
    """
    Scaffold数据集，使用预定义的训练/验证/测试划分
    支持从scaffold.npz文件加载数据
    """
    
    def __init__(self, 
                 root: str,
                 dataset_arg: str = 'chignolin_small',
                 split: str = 'train',
                 transform: Optional[Callable] = None,
                 pre_transform: Optional[Callable] = None,
                 pre_filter: Optional[Callable] = None):
        """
        Args:
            root: 数据根目录
            dataset_arg: 数据集名称
            split: 数据集划分 ('train', 'val', 'test')
            transform: 数据变换函数
            pre_transform: 预处理变换函数
            pre_filter: 预过滤函数
        """
        self.dataset_arg = dataset_arg
        self.split = split
        assert split in ['train', 'val', 'test'], f"split必须是'train', 'val', 'test'之一，得到: {split}"
        
        super().__init__(root, transform, pre_transform, pre_filter)
        self.data, self.slices = torch.load(self.processed_paths[0])

    @property
    def raw_file_names(self) -> List[str]:
        return ['scaffold.npz']

    @property
    def processed_file_names(self) -> List[str]:
        return [f'{self.dataset_arg}_scaffold_{self.split}.pt']

    def download(self):
        # scaffold.npz应该已经存在，不需要下载
        pass

    def process(self):
        """处理原始数据并保存为PyTorch Geometric格式"""
        
        # 加载scaffold.npz文件
        scaffold_path = os.path.join(self.raw_dir, 'scaffold.npz')
        if not os.path.exists(scaffold_path):
            raise FileNotFoundError(f"找不到scaffold.npz文件: {scaffold_path}")
        
        data = np.load(scaffold_path)
        
        # 获取对应split的数据
        structures = data[f'{self.split}_structures']
        forces = data[f'{self.split}_forces'] 
        energies = data[f'{self.split}_energies']
        atomic_numbers = data['atomic_numbers']
        
        print(f"处理{self.split}数据集: {len(structures)}个结构, {len(atomic_numbers)}个原子")
        
        # 转换为PyTorch Geometric Data对象列表
        data_list = []
        
        for i in range(len(structures)):
            # 坐标和力
            pos = torch.tensor(structures[i], dtype=torch.float32)
            force = torch.tensor(forces[i], dtype=torch.float32)
            
            # 能量
            energy = torch.tensor([energies[i]], dtype=torch.float32)
            
            # 原子序数
            z = torch.tensor(atomic_numbers, dtype=torch.long)
            
            # 创建Data对象
            data_obj = Data(
                z=z,           # 原子序数
                pos=pos,       # 原子坐标
                y=energy.unsqueeze(0),      # 能量，形状为(1, 1)
                dy=force       # 力
            )
            
            if self.pre_filter is not None and not self.pre_filter(data_obj):
                continue
                
            if self.pre_transform is not None:
                data_obj = self.pre_transform(data_obj)
                
            data_list.append(data_obj)
        
        # 保存处理后的数据
        data, slices = self.collate(data_list)
        torch.save((data, slices), self.processed_paths[0])
        
        print(f"✅ {self.split}数据集处理完成: {len(data_list)}个样本")

    def __repr__(self) -> str:
        return f'{self.__class__.__name__}({len(self)}, dataset={self.dataset_arg}, split={self.split})'


class ScaffoldDataModule:
    """
    Scaffold数据模块，管理训练/验证/测试数据集
    模拟标准数据集接口，用于与现有训练代码兼容
    """

    def __init__(self, root: str, dataset_arg: str = 'chignolin_small', pre_transform=None):
        self.root = root
        self.dataset_arg = dataset_arg
        self.pre_transform = pre_transform

        # 创建各个split的数据集
        self.train_dataset = ScaffoldDataset(root, dataset_arg, 'train', pre_transform=pre_transform)
        self.val_dataset = ScaffoldDataset(root, dataset_arg, 'val', pre_transform=pre_transform)
        self.test_dataset = ScaffoldDataset(root, dataset_arg, 'test', pre_transform=pre_transform)

        # 计算均值和标准差（基于训练集）
        self._compute_stats()

        # 为了兼容性，设置一些属性
        self.idx_train = list(range(len(self.train_dataset)))
        self.idx_val = list(range(len(self.val_dataset)))
        self.idx_test = list(range(len(self.test_dataset)))

    def _compute_stats(self):
        """计算训练集的能量均值和标准差"""
        energies = []
        for data in self.train_dataset:
            energies.append(data.y.squeeze().item())

        energies = torch.tensor(energies)
        self.mean = energies.mean()
        self.std = energies.std()

        print(f"训练集统计: 均值={self.mean:.6f}, 标准差={self.std:.6f}")

    def __len__(self):
        """返回总数据集大小"""
        return len(self.train_dataset) + len(self.val_dataset) + len(self.test_dataset)

    def get_atomref(self):
        """返回原子参考能量（如果有的话）"""
        return None

    def get_train_dataset(self):
        return self.train_dataset

    def get_val_dataset(self):
        return self.val_dataset

    def get_test_dataset(self):
        return self.test_dataset


def test_scaffold_dataset():
    """测试ScaffoldDataset"""
    
    # 测试各个split
    for split in ['train', 'val', 'test']:
        print(f"\n=== 测试{split}数据集 ===")
        
        dataset = ScaffoldDataset(
            root='./protein_ref_datasets/',
            dataset_arg='chignolin_small',
            split=split
        )
        
        print(f"数据集大小: {len(dataset)}")
        
        # 检查第一个样本
        if len(dataset) > 0:
            sample = dataset[0]
            print(f"第一个样本:")
            print(f"  z (原子序数): {sample.z.shape} {sample.z.dtype}")
            print(f"  pos (坐标): {sample.pos.shape} {sample.pos.dtype}")
            print(f"  y (能量): {sample.y.shape} {sample.y.dtype}")
            print(f"  dy (力): {sample.dy.shape} {sample.dy.dtype}")
            
            # 检查原子序数范围
            z_min, z_max = sample.z.min().item(), sample.z.max().item()
            print(f"  原子序数范围: {z_min} - {z_max}")
            
            # 检查数据一致性
            n_atoms_z = len(sample.z)
            n_atoms_pos = len(sample.pos)
            n_atoms_dy = len(sample.dy)
            
            if n_atoms_z == n_atoms_pos == n_atoms_dy:
                print(f"  ✅ 原子数一致: {n_atoms_z}")
            else:
                print(f"  ❌ 原子数不一致: z={n_atoms_z}, pos={n_atoms_pos}, dy={n_atoms_dy}")


if __name__ == "__main__":
    test_scaffold_dataset()
