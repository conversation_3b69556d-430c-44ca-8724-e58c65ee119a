--- Configuration v4.0812 ---
- Dataset: DeepPeppi
- Device: cuda
- batch_size: 1
- gradient_accumulation_steps: 4
- learning_rate: 0.0001
- num_epochs: 50
- max_atoms: 5000
- vqvae_dim: 64
- use_amp: True
-------------------------------------------------
Step 1: NPY files already exist, skipping conversion...

Step 2: Loading labels from PPI data...

Balancing dataset classes...
--- Class counts before balancing ---
Class 'Peptide only': 234 samples
Class 'Protein only': 551 samples
Class 'Both': 0 samples
------------------------------------
Balancing non-empty classes to 234 samples each.
Found 468 unique proteins with labels (after potential balancing).
Class 0 (Peptide only): 234
Class 1 (Protein only): 234
Class 2 (Both): 0

Step 3: Creating dataset and dataloaders...
Found 468 preprocessed protein files for this dataset.
Train samples: 374, Test samples: 94

Step 4: Initializing model...

Step 5: Starting training...
