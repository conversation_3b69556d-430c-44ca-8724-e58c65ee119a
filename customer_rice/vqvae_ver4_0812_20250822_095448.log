--- Configuration v4.0812 ---
- Dataset: DeepPeppi
- Device: cuda
- batch_size: 1
- gradient_accumulation_steps: 4
- learning_rate: 0.0001
- num_epochs: 50
- max_atoms: 5000
- vqvae_dim: 64
- use_amp: True
-------------------------------------------------
Step 1: Converting PDB files to NPY format...
Converting PDBs from /home/<USER>/code/MAPE-PPI/data/raw_data/all_pdbs to npy format in preprocessed_proteins_DeepPeppi_v4...

Step 2: Loading labels from PPI data...

Balancing dataset classes...
Balancing all 3 classes to 234 samples each.
