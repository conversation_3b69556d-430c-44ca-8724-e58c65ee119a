Traceback (most recent call last):
  File "/home/<USER>/code/customer_rice/vqvae_ver4_0822.py", line 20, in <module>
    import torch
  File "/home/<USER>/miniconda/lib/python3.12/site-packages/torch/__init__.py", line 368, in <module>
    from torch._C import *  # noqa: F403
    ^^^^^^^^^^^^^^^^^^^^^^
ImportError: /home/<USER>/miniconda/lib/python3.12/site-packages/torch/lib/../../nvidia/cusparse/lib/libcusparse.so.12: undefined symbol: __nvJitLinkComplete_12_4, version libnvJitLink.so.12
