# Gemini Project Configuration for dMaSIF

This file provides instructions for the Gemini agent to interact with the dMaSIF project.

## Build & Test Commands

- **Build:** `echo "No build command specified. Dependencies are installed via pip."`
- **Test:** `echo "No specific test command provided. Validation is part of the training script."`
- **Linter:** `echo "No linter specified."`

## Project Overview

- **Primary Language:** Python
- **Core Frameworks:** PyTorch, PyTorch Geometric, PyKeops
- **Purpose:** dMaSIF (differentiable Molecular Surface Interaction Fingerprinting) is a geometric deep learning framework for fast, end-to-end learning on protein surfaces. It takes raw atomic coordinates (e.g., from PDB files) as input to perform tasks like interaction site identification and protein-protein interaction (PPI) prediction.

- **Key Files:**
  - `main_training.py`: The main script for training models.
  - `main_inference.py`: The main script for running inference with pre-trained models.
  - `model.py`: Defines the neural network architecture.
  - `geometry_processing.py`: Contains the core logic for on-the-fly surface generation and geometric convolutions.
  - `Arguments.py`: Defines the command-line arguments for training and inference.
  - `dataloading.py`: Handles the loading and preprocessing of protein data.

## Instructions for Gemini

1.  **Primary Goal:** The main goal is typically to predict interaction sites on a protein surface or to predict whether two proteins will interact.
2.  **Input Data:** The fundamental input for this project is one or more protein structures in PDB format.
3.  **Training vs. Inference:**
    - To train a new model, use `python main_training.py` with the appropriate flags from `Arguments.py`.
    - To make a prediction on new data using an existing model, the primary script to use is `python main_inference.py`. You will need to specify the path to the PDB file(s) and the pre-trained model to use.
4.  **Core Logic:** The unique strength of dMaSIF is in `geometry_processing.py`. This file handles the conversion from a point cloud of atoms to a molecular surface and applies the custom convolutions on-the-fly.
5.  **Simplifying for a Demo:** When asked to create a demo, the goal is to extract the core prediction logic from `main_inference.py`. This involves:
    - Hardcoding the path to a pre-trained model.
    - Simplifying the data loading part to accept just a path to a PDB file.
    - Removing all argument parsing, training-related code, and performance benchmarking.
    - The output should be a clear prediction (e.g., interaction scores or site coordinates).

## Model Architectures (from `model.py`)

The `dMaSIF` project utilizes several neural network architectures for different tasks, primarily for site prediction and search. These models are built using PyTorch and leverage custom geometric convolution layers.

### 1. `MaSIF_site_model`

This model is designed for predicting interaction sites on protein surfaces. It incorporates geometric features and applies a series of MaSIF (Molecular Surface Interaction Fingerprinting) layers.

**Key Components:**
- **Input:** Features derived from protein surface points (e.g., coordinates, normals, charges, hydrophobicity).
- **`nn.Linear` layers:** Initial linear transformations of input features.
- **`MaSIF_conv_block`:** Custom convolutional blocks that perform geometric convolutions on the surface. These blocks typically consist of:
    - `MaSIF_conv_layer`: The core convolutional layer, likely using PyKeops for efficient operations.
    - `nn.LeakyReLU`: Activation function.
    - `nn.BatchNorm1d`: Batch normalization for stable training.
- **Output Layer:** A final `nn.Linear` layer to produce the site prediction scores (e.g., probability of being an interaction site).

**Architecture Flow:**
Input Features -> Linear Layer -> (MaSIF_conv_block -> MaSIF_conv_block -> ...) -> Output Linear Layer -> Prediction Scores

### 2. `MaSIF_search_model`

This model is used for searching or comparing protein surfaces, often in the context of protein-protein interaction (PPI) prediction or binding affinity. It generates a global descriptor for the protein surface.

**Key Components:**
- **Input:** Similar to `MaSIF_site_model`, features from protein surface points.
- **`nn.Linear` layers:** Initial feature processing.
- **`MaSIF_conv_block`:** Multiple layers of geometric convolutions to learn hierarchical features.
- **Global Pooling:** After the convolutional layers, a global pooling operation (e.g., `torch.max` or `torch.mean` across all points) is applied to aggregate point-wise features into a single, fixed-size global descriptor for the entire surface.
- **Output Layer:** A final `nn.Linear` layer on the global descriptor to produce the search/comparison output (e.g., a score indicating similarity or interaction likelihood).

**Architecture Flow:**
Input Features -> Linear Layer -> (MaSIF_conv_block -> MaSIF_conv_block -> ...) -> Global Pooling -> Output Linear Layer -> Search/Comparison Score

### 3. `NoMaSIF_site_model`

This model serves as a baseline or ablation study, similar to `MaSIF_site_model` but without the specialized geometric convolutions. It typically uses standard fully connected layers.

**Key Components:**
- **Input:** Protein surface point features.
- **`nn.Linear` layers:** Multiple fully connected layers.
- **`nn.LeakyReLU`:** Activation functions.
- **`nn.BatchNorm1d`:** Batch normalization.
- **Output Layer:** A final `nn.Linear` layer for site prediction.

**Architecture Flow:**
Input Features -> Linear Layer -> (Linear Layer -> LeakyReLU -> BatchNorm1d -> ...) -> Output Linear Layer -> Prediction Scores

### 4. `NoMaSIF_search_model`

Similar to `MaSIF_search_model` but without the geometric convolutions, serving as a non-geometric baseline for surface search/comparison tasks.

**Key Components:**
- **Input:** Protein surface point features.
- **`nn.Linear` layers:** Multiple fully connected layers.
- **`nn.LeakyReLU`:** Activation functions.
- **`nn.BatchNorm1d`:** Batch normalization.
- **Global Pooling:** Aggregates point-wise features into a global descriptor.
- **Output Layer:** A final `nn.Linear` layer on the global descriptor for search/comparison.

**Architecture Flow:**
Input Features -> Linear Layer -> (Linear Layer -> LeakyReLU -> BatchNorm1d -> ...) -> Global Pooling -> Output Linear Layer -> Search/Comparison Score

These models provide a comprehensive set of tools for analyzing protein surfaces, with the `MaSIF` models leveraging geometric deep learning for enhanced performance and the `NoMaSIF` models offering a comparison baseline.