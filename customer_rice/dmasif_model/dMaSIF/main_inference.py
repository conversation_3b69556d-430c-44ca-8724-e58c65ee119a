# Standard imports:
import numpy as np
import torch
from torch.utils.tensorboard import SummaryWriter
from torch.utils.data import random_split
from torch_geometric.data import DataLoader
from torch_geometric.transforms import Compose
from pathlib import Path

# Custom data loader and model:
from data import ProteinPairsSurfaces, PairData, CenterPairAtoms, load_protein_pair
from data import RandomRotationPairAtoms, NormalizeChemFeatures, iface_valid_filter
from model import dMaSIF
from data_iteration import iterate
from helper import *
from Arguments import parser
from evaluation_metrics import ModelEvaluator

args = parser.parse_args()
model_path = "models/" + args.experiment_name
save_predictions_path = Path("preds/" + args.experiment_name)

# Ensure reproducability:
torch.backends.cudnn.deterministic = True
torch.manual_seed(args.seed)
torch.cuda.manual_seed_all(args.seed)
np.random.seed(args.seed)


# Load the train and test datasets:
transformations = (
    Compose([NormalizeChemFeatures(), CenterPairAtoms(), RandomRotationPairAtoms()])
    if args.random_rotation
    else Compose([NormalizeChemFeatures()])
)

if args.single_pdb != "":
    single_data_dir = Path("./data_preprocessing/npys/")
    test_dataset = [load_protein_pair(args.single_pdb, single_data_dir,single_pdb=True)]
    test_pdb_ids = [args.single_pdb]
elif args.pdb_list != "":
    with open(args.pdb_list) as f:
        pdb_list = f.read().splitlines()
    single_data_dir = Path("./data_preprocessing/npys/")
    test_dataset = [load_protein_pair(pdb, single_data_dir,single_pdb=True) for pdb in pdb_list]
    test_pdb_ids = [pdb for pdb in pdb_list]
else:
    test_dataset = ProteinPairsSurfaces(
        "surface_data", train=False, ppi=args.search, transform=transformations
    )
    test_pdb_ids = (
        np.load("surface_data/processed/testing_pairs_data_ids.npy")
        if args.site
        else np.load("surface_data/processed/testing_pairs_data_ids_ppi.npy")
    )

    test_dataset = [
        (data, pdb_id)
        for data, pdb_id in zip(test_dataset, test_pdb_ids)
        if iface_valid_filter(data)
    ]
    test_dataset, test_pdb_ids = list(zip(*test_dataset))


# PyTorch geometric expects an explicit list of "batched variables":
batch_vars = ["xyz_p1", "xyz_p2", "atom_coords_p1", "atom_coords_p2"]
test_loader = DataLoader(
    test_dataset, batch_size=args.batch_size, follow_batch=batch_vars
)

net = dMaSIF(args)
# net.load_state_dict(torch.load(model_path, map_location=args.device))
net.load_state_dict(
    torch.load(model_path, map_location=args.device)["model_state_dict"]
)
net = net.to(args.device)

# Perform one pass through the data:
info = iterate(
    net,
    test_loader,
    None,
    args,
    test=True,
    save_path=save_predictions_path,
    pdb_ids=test_pdb_ids,
)

# 生成详细的评估报告
print("\n" + "="*60)
print("生成详细评估报告...")
print("="*60)

# 创建评估器
evaluator = ModelEvaluator(f"evaluation_results/{args.experiment_name}")

# 打印基础统计信息
print(f"\n推理完成!")
print(f"实验名称: {args.experiment_name}")
print(f"测试样本数: {len(info['Loss']) if 'Loss' in info else 0}")

# 计算平均指标
if info:
    print(f"\n平均指标:")
    for key, values in info.items():
        if key in ['Loss', 'ROC-AUC', 'Accuracy', 'Precision', 'Recall', 'F1-Score',
                  'Specificity', 'MCC', 'PR-AUC']:
            mean_val = np.mean(values) if values else 0.0
            print(f"{key}: {mean_val:.4f}")

    # 计算混淆矩阵总和
    if all(key in info for key in ['True_Positives', 'False_Positives',
                                  'True_Negatives', 'False_Negatives']):
        total_tp = np.sum(info['True_Positives'])
        total_fp = np.sum(info['False_Positives'])
        total_tn = np.sum(info['True_Negatives'])
        total_fn = np.sum(info['False_Negatives'])

        print(f"\n总体混淆矩阵:")
        print(f"真阴性 (TN): {total_tn:6d}  |  假阳性 (FP): {total_fp:6d}")
        print(f"假阴性 (FN): {total_fn:6d}  |  真阳性 (TP): {total_tp:6d}")

        # 计算总体指标
        total_accuracy = (total_tp + total_tn) / (total_tp + total_tn + total_fp + total_fn) if (total_tp + total_tn + total_fp + total_fn) > 0 else 0
        total_precision = total_tp / (total_tp + total_fp) if (total_tp + total_fp) > 0 else 0
        total_recall = total_tp / (total_tp + total_fn) if (total_tp + total_fn) > 0 else 0
        total_f1 = 2 * (total_precision * total_recall) / (total_precision + total_recall) if (total_precision + total_recall) > 0 else 0

        print(f"\n总体性能指标:")
        print(f"总体准确率: {total_accuracy:.4f}")
        print(f"总体精确率: {total_precision:.4f}")
        print(f"总体召回率: {total_recall:.4f}")
        print(f"总体F1分数: {total_f1:.4f}")

print(f"\n评估结果已保存到: evaluation_results/{args.experiment_name}/")

#np.save(f"timings/{args.experiment_name}_convtime.npy", info["conv_time"])
#np.save(f"timings/{args.experiment_name}_memoryusage.npy", info["memory_usage"])
