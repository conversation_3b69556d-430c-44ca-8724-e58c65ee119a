{"metadata": {"language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.7-final"}, "orig_nbformat": 2, "kernelspec": {"name": "python37764bitvenv9d3d77f519b14852b6e336264c4e59e7", "display_name": "Python 3.7.7 64-bit ('venv')", "language": "python"}}, "nbformat": 4, "nbformat_minor": 2, "cells": [{"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["import numpy as np \n", "import matplotlib.pyplot as plt\n", "import plotly.graph_objects as go\n", "from plotly.subplots import make_subplots\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["batch_sizes = [2**i for i in range(8)]"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"output_type": "error", "ename": "FileNotFoundError", "evalue": "[Errno 2] No such file or directory: '../timings/TangentConv_benchmark_1layer_pointmatching_faster_fixed_randNeg_correctData_biConv_16dim_epoch79_surfacegen_generation_times_1.npy'", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mFileNotFoundError\u001b[0m                         <PERSON><PERSON> (most recent call last)", "\u001b[0;32m<ipython-input-6-611355b12fb7>\u001b[0m in \u001b[0;36m<module>\u001b[0;34m\u001b[0m\n\u001b[1;32m      4\u001b[0m \u001b[0mmemory_std\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;34m[\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      5\u001b[0m \u001b[0;32mfor\u001b[0m \u001b[0msize\u001b[0m \u001b[0;32min\u001b[0m \u001b[0mbatch_sizes\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m----> 6\u001b[0;31m     \u001b[0mtimes\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mnp\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mload\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34mf'../timings/TangentConv_benchmark_1layer_pointmatching_faster_fixed_randNeg_correctData_biConv_16dim_epoch79_surfacegen_generation_times_{size}.npy'\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m      7\u001b[0m     \u001b[0mmemory\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mnp\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mload\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34mf'../timings/TangentConv_benchmark_1layer_pointmatching_faster_fixed_randNeg_correctData_biConv_16dim_epoch79_surfacegen_all_memory_usage_batch_{size}.npy'\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      8\u001b[0m     \u001b[0mmemory\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mmemory\u001b[0m\u001b[0;34m*\u001b[0m\u001b[0;36m1e-6\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/GitHub/protein_tangentconv/venv/lib/python3.7/site-packages/numpy/lib/npyio.py\u001b[0m in \u001b[0;36mload\u001b[0;34m(file, mmap_mode, allow_pickle, fix_imports, encoding)\u001b[0m\n\u001b[1;32m    414\u001b[0m             \u001b[0mown_fid\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;32mFalse\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    415\u001b[0m         \u001b[0;32melse\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 416\u001b[0;31m             \u001b[0mfid\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mstack\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0menter_context\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mopen\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mos_fspath\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mfile\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m\"rb\"\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    417\u001b[0m             \u001b[0mown_fid\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;32mTrue\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    418\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mFileNotFoundError\u001b[0m: [Errno 2] No such file or directory: '../timings/TangentConv_benchmark_1layer_pointmatching_faster_fixed_randNeg_correctData_biConv_16dim_epoch79_surfacegen_generation_times_1.npy'"]}], "source": ["time_means = []\n", "time_std = []\n", "memory_means = []\n", "memory_std = []\n", "for size in batch_sizes:\n", "    times = np.load(f'timings/TangentConv_benchmark_1layer_pointmatching_faster_fixed_randNeg_correctData_biConv_16dim_epoch79_surfacegen_generation_times_{size}.npy')\n", "    memory = np.load(f'timings/TangentConv_benchmark_1layer_pointmatching_faster_fixed_randNeg_correctData_biConv_16dim_epoch79_surfacegen_all_memory_usage_batch_{size}.npy')\n", "    memory = memory*1e-6\n", "    time_means.append(times.mean())\n", "    memory_means.append(memory.mean())\n", "    time_std.append(times.std())\n", "    memory_std.append(memory.std())"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"output_type": "display_data", "data": {"text/plain": "<Figure size 432x288 with 1 Axes>", "image/svg+xml": "<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n<!-- Created with matplotlib (https://matplotlib.org/) -->\n<svg height=\"266.185625pt\" version=\"1.1\" viewBox=\"0 0 410.378125 266.185625\" width=\"410.378125pt\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\">\n <defs>\n  <style type=\"text/css\">\n*{stroke-linecap:butt;stroke-linejoin:round;}\n  </style>\n </defs>\n <g id=\"figure_1\">\n  <g id=\"patch_1\">\n   <path d=\"M 0 266.185625 \nL 410.378125 266.185625 \nL 410.378125 0 \nL 0 0 \nz\n\" style=\"fill:none;\"/>\n  </g>\n  <g id=\"axes_1\">\n   <g id=\"patch_2\">\n    <path d=\"M 68.378125 228.629375 \nL 403.178125 228.629375 \nL 403.178125 11.189375 \nL 68.378125 11.189375 \nz\n\" style=\"fill:#ffffff;\"/>\n   </g>\n   <g id=\"matplotlib.axis_1\">\n    <g id=\"xtick_1\">\n     <g id=\"line2d_1\">\n      <defs>\n       <path d=\"M 0 0 \nL 0 3.5 \n\" id=\"m88cf3eac89\" style=\"stroke:#000000;stroke-width:0.8;\"/>\n      </defs>\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"83.596307\" xlink:href=\"#m88cf3eac89\" y=\"228.629375\"/>\n      </g>\n     </g>\n     <g id=\"text_1\">\n      <!-- $\\mathdefault{10^{0}}$ -->\n      <defs>\n       <path d=\"M 12.40625 8.296875 \nL 28.515625 8.296875 \nL 28.515625 63.921875 \nL 10.984375 60.40625 \nL 10.984375 69.390625 \nL 28.421875 72.90625 \nL 38.28125 72.90625 \nL 38.28125 8.296875 \nL 54.390625 8.296875 \nL 54.390625 0 \nL 12.40625 0 \nz\n\" id=\"DejaVuSans-49\"/>\n       <path d=\"M 31.78125 66.40625 \nQ 24.171875 66.40625 20.328125 58.90625 \nQ 16.5 51.421875 16.5 36.375 \nQ 16.5 21.390625 20.328125 13.890625 \nQ 24.171875 6.390625 31.78125 6.390625 \nQ 39.453125 6.390625 43.28125 13.890625 \nQ 47.125 21.390625 47.125 36.375 \nQ 47.125 51.421875 43.28125 58.90625 \nQ 39.453125 66.40625 31.78125 66.40625 \nz\nM 31.78125 74.21875 \nQ 44.046875 74.21875 50.515625 64.515625 \nQ 56.984375 54.828125 56.984375 36.375 \nQ 56.984375 17.96875 50.515625 8.265625 \nQ 44.046875 -1.421875 31.78125 -1.421875 \nQ 19.53125 -1.421875 13.0625 8.265625 \nQ 6.59375 17.96875 6.59375 36.375 \nQ 6.59375 54.828125 13.0625 64.515625 \nQ 19.53125 74.21875 31.78125 74.21875 \nz\n\" id=\"DejaVuSans-48\"/>\n      </defs>\n      <g transform=\"translate(74.796307 243.227812)scale(0.1 -0.1)\">\n       <use transform=\"translate(0 0.765625)\" xlink:href=\"#DejaVuSans-49\"/>\n       <use transform=\"translate(63.623047 0.765625)\" xlink:href=\"#DejaVuSans-48\"/>\n       <use transform=\"translate(128.203125 39.046875)scale(0.7)\" xlink:href=\"#DejaVuSans-48\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_2\">\n     <g id=\"line2d_2\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"228.035466\" xlink:href=\"#m88cf3eac89\" y=\"228.629375\"/>\n      </g>\n     </g>\n     <g id=\"text_2\">\n      <!-- $\\mathdefault{10^{1}}$ -->\n      <g transform=\"translate(219.235466 243.227812)scale(0.1 -0.1)\">\n       <use transform=\"translate(0 0.684375)\" xlink:href=\"#DejaVuSans-49\"/>\n       <use transform=\"translate(63.623047 0.684375)\" xlink:href=\"#DejaVuSans-48\"/>\n       <use transform=\"translate(128.203125 38.965625)scale(0.7)\" xlink:href=\"#DejaVuSans-49\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_3\">\n     <g id=\"line2d_3\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"372.474625\" xlink:href=\"#m88cf3eac89\" y=\"228.629375\"/>\n      </g>\n     </g>\n     <g id=\"text_3\">\n      <!-- $\\mathdefault{10^{2}}$ -->\n      <defs>\n       <path d=\"M 19.1875 8.296875 \nL 53.609375 8.296875 \nL 53.609375 0 \nL 7.328125 0 \nL 7.328125 8.296875 \nQ 12.9375 14.109375 22.625 23.890625 \nQ 32.328125 33.6875 34.8125 36.53125 \nQ 39.546875 41.84375 41.421875 45.53125 \nQ 43.3125 49.21875 43.3125 52.78125 \nQ 43.3125 58.59375 39.234375 62.25 \nQ 35.15625 65.921875 28.609375 65.921875 \nQ 23.96875 65.921875 18.8125 64.3125 \nQ 13.671875 62.703125 7.8125 59.421875 \nL 7.8125 69.390625 \nQ 13.765625 71.78125 18.9375 73 \nQ 24.125 74.21875 28.421875 74.21875 \nQ 39.75 74.21875 46.484375 68.546875 \nQ 53.21875 62.890625 53.21875 53.421875 \nQ 53.21875 48.921875 51.53125 44.890625 \nQ 49.859375 40.875 45.40625 35.40625 \nQ 44.1875 33.984375 37.640625 27.21875 \nQ 31.109375 20.453125 19.1875 8.296875 \nz\n\" id=\"DejaVuSans-50\"/>\n      </defs>\n      <g transform=\"translate(363.674625 243.227812)scale(0.1 -0.1)\">\n       <use transform=\"translate(0 0.765625)\" xlink:href=\"#DejaVuSans-49\"/>\n       <use transform=\"translate(63.623047 0.765625)\" xlink:href=\"#DejaVuSans-48\"/>\n       <use transform=\"translate(128.203125 39.046875)scale(0.7)\" xlink:href=\"#DejaVuSans-50\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_4\">\n     <g id=\"line2d_4\">\n      <defs>\n       <path d=\"M 0 0 \nL 0 2 \n\" id=\"m2d2a1e16a3\" style=\"stroke:#000000;stroke-width:0.6;\"/>\n      </defs>\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.6;\" x=\"69.598706\" xlink:href=\"#m2d2a1e16a3\" y=\"228.629375\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_5\">\n     <g id=\"line2d_5\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.6;\" x=\"76.987133\" xlink:href=\"#m2d2a1e16a3\" y=\"228.629375\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_6\">\n     <g id=\"line2d_6\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.6;\" x=\"127.076826\" xlink:href=\"#m2d2a1e16a3\" y=\"228.629375\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_7\">\n     <g id=\"line2d_7\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.6;\" x=\"152.5113\" xlink:href=\"#m2d2a1e16a3\" y=\"228.629375\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_8\">\n     <g id=\"line2d_8\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.6;\" x=\"170.557346\" xlink:href=\"#m2d2a1e16a3\" y=\"228.629375\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_9\">\n     <g id=\"line2d_9\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.6;\" x=\"184.554947\" xlink:href=\"#m2d2a1e16a3\" y=\"228.629375\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_10\">\n     <g id=\"line2d_10\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.6;\" x=\"195.991819\" xlink:href=\"#m2d2a1e16a3\" y=\"228.629375\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_11\">\n     <g id=\"line2d_11\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.6;\" x=\"205.661557\" xlink:href=\"#m2d2a1e16a3\" y=\"228.629375\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_12\">\n     <g id=\"line2d_12\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.6;\" x=\"214.037865\" xlink:href=\"#m2d2a1e16a3\" y=\"228.629375\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_13\">\n     <g id=\"line2d_13\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.6;\" x=\"221.426293\" xlink:href=\"#m2d2a1e16a3\" y=\"228.629375\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_14\">\n     <g id=\"line2d_14\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.6;\" x=\"271.515986\" xlink:href=\"#m2d2a1e16a3\" y=\"228.629375\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_15\">\n     <g id=\"line2d_15\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.6;\" x=\"296.950459\" xlink:href=\"#m2d2a1e16a3\" y=\"228.629375\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_16\">\n     <g id=\"line2d_16\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.6;\" x=\"314.996505\" xlink:href=\"#m2d2a1e16a3\" y=\"228.629375\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_17\">\n     <g id=\"line2d_17\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.6;\" x=\"328.994106\" xlink:href=\"#m2d2a1e16a3\" y=\"228.629375\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_18\">\n     <g id=\"line2d_18\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.6;\" x=\"340.430978\" xlink:href=\"#m2d2a1e16a3\" y=\"228.629375\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_19\">\n     <g id=\"line2d_19\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.6;\" x=\"350.100716\" xlink:href=\"#m2d2a1e16a3\" y=\"228.629375\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_20\">\n     <g id=\"line2d_20\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.6;\" x=\"358.477025\" xlink:href=\"#m2d2a1e16a3\" y=\"228.629375\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_21\">\n     <g id=\"line2d_21\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.6;\" x=\"365.865452\" xlink:href=\"#m2d2a1e16a3\" y=\"228.629375\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"text_4\">\n     <!-- Batch size -->\n     <defs>\n      <path d=\"M 19.671875 34.8125 \nL 19.671875 8.109375 \nL 35.5 8.109375 \nQ 43.453125 8.109375 47.28125 11.40625 \nQ 51.125 14.703125 51.125 21.484375 \nQ 51.125 28.328125 47.28125 31.5625 \nQ 43.453125 34.8125 35.5 34.8125 \nz\nM 19.671875 64.796875 \nL 19.671875 42.828125 \nL 34.28125 42.828125 \nQ 41.5 42.828125 45.03125 45.53125 \nQ 48.578125 48.25 48.578125 53.8125 \nQ 48.578125 59.328125 45.03125 62.0625 \nQ 41.5 64.796875 34.28125 64.796875 \nz\nM 9.8125 72.90625 \nL 35.015625 72.90625 \nQ 46.296875 72.90625 52.390625 68.21875 \nQ 58.5 63.53125 58.5 54.890625 \nQ 58.5 48.1875 55.375 44.234375 \nQ 52.25 40.28125 46.1875 39.3125 \nQ 53.46875 37.75 57.5 32.78125 \nQ 61.53125 27.828125 61.53125 20.40625 \nQ 61.53125 10.640625 54.890625 5.3125 \nQ 48.25 0 35.984375 0 \nL 9.8125 0 \nz\n\" id=\"DejaVuSans-66\"/>\n      <path d=\"M 34.28125 27.484375 \nQ 23.390625 27.484375 19.1875 25 \nQ 14.984375 22.515625 14.984375 16.5 \nQ 14.984375 11.71875 18.140625 8.90625 \nQ 21.296875 6.109375 26.703125 6.109375 \nQ 34.1875 6.109375 38.703125 11.40625 \nQ 43.21875 16.703125 43.21875 25.484375 \nL 43.21875 27.484375 \nz\nM 52.203125 31.203125 \nL 52.203125 0 \nL 43.21875 0 \nL 43.21875 8.296875 \nQ 40.140625 3.328125 35.546875 0.953125 \nQ 30.953125 -1.421875 24.3125 -1.421875 \nQ 15.921875 -1.421875 10.953125 3.296875 \nQ 6 8.015625 6 15.921875 \nQ 6 25.140625 12.171875 29.828125 \nQ 18.359375 34.515625 30.609375 34.515625 \nL 43.21875 34.515625 \nL 43.21875 35.40625 \nQ 43.21875 41.609375 39.140625 45 \nQ 35.0625 48.390625 27.6875 48.390625 \nQ 23 48.390625 18.546875 47.265625 \nQ 14.109375 46.140625 10.015625 43.890625 \nL 10.015625 52.203125 \nQ 14.9375 54.109375 19.578125 55.046875 \nQ 24.21875 56 28.609375 56 \nQ 40.484375 56 46.34375 49.84375 \nQ 52.203125 43.703125 52.203125 31.203125 \nz\n\" id=\"DejaVuSans-97\"/>\n      <path d=\"M 18.3125 70.21875 \nL 18.3125 54.6875 \nL 36.8125 54.6875 \nL 36.8125 47.703125 \nL 18.3125 47.703125 \nL 18.3125 18.015625 \nQ 18.3125 11.328125 20.140625 9.421875 \nQ 21.96875 7.515625 27.59375 7.515625 \nL 36.8125 7.515625 \nL 36.8125 0 \nL 27.59375 0 \nQ 17.1875 0 13.234375 3.875 \nQ 9.28125 7.765625 9.28125 18.015625 \nL 9.28125 47.703125 \nL 2.6875 47.703125 \nL 2.6875 54.6875 \nL 9.28125 54.6875 \nL 9.28125 70.21875 \nz\n\" id=\"DejaVuSans-116\"/>\n      <path d=\"M 48.78125 52.59375 \nL 48.78125 44.1875 \nQ 44.96875 46.296875 41.140625 47.34375 \nQ 37.3125 48.390625 33.40625 48.390625 \nQ 24.65625 48.390625 19.8125 42.84375 \nQ 14.984375 37.3125 14.984375 27.296875 \nQ 14.984375 17.28125 19.8125 11.734375 \nQ 24.65625 6.203125 33.40625 6.203125 \nQ 37.3125 6.203125 41.140625 7.25 \nQ 44.96875 8.296875 48.78125 10.40625 \nL 48.78125 2.09375 \nQ 45.015625 0.34375 40.984375 -0.53125 \nQ 36.96875 -1.421875 32.421875 -1.421875 \nQ 20.0625 -1.421875 12.78125 6.34375 \nQ 5.515625 14.109375 5.515625 27.296875 \nQ 5.515625 40.671875 12.859375 48.328125 \nQ 20.21875 56 33.015625 56 \nQ 37.15625 56 41.109375 55.140625 \nQ 45.0625 54.296875 48.78125 52.59375 \nz\n\" id=\"DejaVuSans-99\"/>\n      <path d=\"M 54.890625 33.015625 \nL 54.890625 0 \nL 45.90625 0 \nL 45.90625 32.71875 \nQ 45.90625 40.484375 42.875 44.328125 \nQ 39.84375 48.1875 33.796875 48.1875 \nQ 26.515625 48.1875 22.3125 43.546875 \nQ 18.109375 38.921875 18.109375 30.90625 \nL 18.109375 0 \nL 9.078125 0 \nL 9.078125 75.984375 \nL 18.109375 75.984375 \nL 18.109375 46.1875 \nQ 21.34375 51.125 25.703125 53.5625 \nQ 30.078125 56 35.796875 56 \nQ 45.21875 56 50.046875 50.171875 \nQ 54.890625 44.34375 54.890625 33.015625 \nz\n\" id=\"DejaVuSans-104\"/>\n      <path id=\"DejaVuSans-32\"/>\n      <path d=\"M 44.28125 53.078125 \nL 44.28125 44.578125 \nQ 40.484375 46.53125 36.375 47.5 \nQ 32.28125 48.484375 27.875 48.484375 \nQ 21.1875 48.484375 17.84375 46.4375 \nQ 14.5 44.390625 14.5 40.28125 \nQ 14.5 37.15625 16.890625 35.375 \nQ 19.28125 33.59375 26.515625 31.984375 \nL 29.59375 31.296875 \nQ 39.15625 29.25 43.1875 25.515625 \nQ 47.21875 21.78125 47.21875 15.09375 \nQ 47.21875 7.46875 41.1875 3.015625 \nQ 35.15625 -1.421875 24.609375 -1.421875 \nQ 20.21875 -1.421875 15.453125 -0.5625 \nQ 10.6875 0.296875 5.421875 2 \nL 5.421875 11.28125 \nQ 10.40625 8.6875 15.234375 7.390625 \nQ 20.0625 6.109375 24.8125 6.109375 \nQ 31.15625 6.109375 34.5625 8.28125 \nQ 37.984375 10.453125 37.984375 14.40625 \nQ 37.984375 18.0625 35.515625 20.015625 \nQ 33.0625 21.96875 24.703125 23.78125 \nL 21.578125 24.515625 \nQ 13.234375 26.265625 9.515625 29.90625 \nQ 5.8125 33.546875 5.8125 39.890625 \nQ 5.8125 47.609375 11.28125 51.796875 \nQ 16.75 56 26.8125 56 \nQ 31.78125 56 36.171875 55.265625 \nQ 40.578125 54.546875 44.28125 53.078125 \nz\n\" id=\"DejaVuSans-115\"/>\n      <path d=\"M 9.421875 54.6875 \nL 18.40625 54.6875 \nL 18.40625 0 \nL 9.421875 0 \nz\nM 9.421875 75.984375 \nL 18.40625 75.984375 \nL 18.40625 64.59375 \nL 9.421875 64.59375 \nz\n\" id=\"DejaVuSans-105\"/>\n      <path d=\"M 5.515625 54.6875 \nL 48.1875 54.6875 \nL 48.1875 46.484375 \nL 14.40625 7.171875 \nL 48.1875 7.171875 \nL 48.1875 0 \nL 4.296875 0 \nL 4.296875 8.203125 \nL 38.09375 47.515625 \nL 5.515625 47.515625 \nz\n\" id=\"DejaVuSans-122\"/>\n      <path d=\"M 56.203125 29.59375 \nL 56.203125 25.203125 \nL 14.890625 25.203125 \nQ 15.484375 15.921875 20.484375 11.0625 \nQ 25.484375 6.203125 34.421875 6.203125 \nQ 39.59375 6.203125 44.453125 7.46875 \nQ 49.3125 8.734375 54.109375 11.28125 \nL 54.109375 2.78125 \nQ 49.265625 0.734375 44.1875 -0.34375 \nQ 39.109375 -1.421875 33.890625 -1.421875 \nQ 20.796875 -1.421875 13.15625 6.1875 \nQ 5.515625 13.8125 5.515625 26.8125 \nQ 5.515625 40.234375 12.765625 48.109375 \nQ 20.015625 56 32.328125 56 \nQ 43.359375 56 49.78125 48.890625 \nQ 56.203125 41.796875 56.203125 29.59375 \nz\nM 47.21875 32.234375 \nQ 47.125 39.59375 43.09375 43.984375 \nQ 39.0625 48.390625 32.421875 48.390625 \nQ 24.90625 48.390625 20.390625 44.140625 \nQ 15.875 39.890625 15.1875 32.171875 \nz\n\" id=\"DejaVuSans-101\"/>\n     </defs>\n     <g transform=\"translate(210.121875 256.905937)scale(0.1 -0.1)\">\n      <use xlink:href=\"#DejaVuSans-66\"/>\n      <use x=\"68.603516\" xlink:href=\"#DejaVuSans-97\"/>\n      <use x=\"129.882812\" xlink:href=\"#DejaVuSans-116\"/>\n      <use x=\"169.091797\" xlink:href=\"#DejaVuSans-99\"/>\n      <use x=\"224.072266\" xlink:href=\"#DejaVuSans-104\"/>\n      <use x=\"287.451172\" xlink:href=\"#DejaVuSans-32\"/>\n      <use x=\"319.238281\" xlink:href=\"#DejaVuSans-115\"/>\n      <use x=\"371.337891\" xlink:href=\"#DejaVuSans-105\"/>\n      <use x=\"399.121094\" xlink:href=\"#DejaVuSans-122\"/>\n      <use x=\"451.611328\" xlink:href=\"#DejaVuSans-101\"/>\n     </g>\n    </g>\n   </g>\n   <g id=\"matplotlib.axis_2\">\n    <g id=\"ytick_1\">\n     <g id=\"line2d_22\">\n      <defs>\n       <path d=\"M 0 0 \nL -3.5 0 \n\" id=\"m4e72441781\" style=\"stroke:#000000;stroke-width:0.8;\"/>\n      </defs>\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"68.378125\" xlink:href=\"#m4e72441781\" y=\"181.22724\"/>\n      </g>\n     </g>\n     <g id=\"text_5\">\n      <!-- $\\mathdefault{10^{-1}}$ -->\n      <defs>\n       <path d=\"M 10.59375 35.5 \nL 73.1875 35.5 \nL 73.1875 27.203125 \nL 10.59375 27.203125 \nz\n\" id=\"DejaVuSans-8722\"/>\n      </defs>\n      <g transform=\"translate(37.878125 185.026458)scale(0.1 -0.1)\">\n       <use transform=\"translate(0 0.684375)\" xlink:href=\"#DejaVuSans-49\"/>\n       <use transform=\"translate(63.623047 0.684375)\" xlink:href=\"#DejaVuSans-48\"/>\n       <use transform=\"translate(128.203125 38.965625)scale(0.7)\" xlink:href=\"#DejaVuSans-8722\"/>\n       <use transform=\"translate(186.855469 38.965625)scale(0.7)\" xlink:href=\"#DejaVuSans-49\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"ytick_2\">\n     <g id=\"line2d_23\">\n      <defs>\n       <path d=\"M 0 0 \nL -2 0 \n\" id=\"m02ac35c477\" style=\"stroke:#000000;stroke-width:0.6;\"/>\n      </defs>\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.6;\" x=\"68.378125\" xlink:href=\"#m02ac35c477\" y=\"219.114471\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"ytick_3\">\n     <g id=\"line2d_24\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.6;\" x=\"68.378125\" xlink:href=\"#m02ac35c477\" y=\"204.93031\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"ytick_4\">\n     <g id=\"line2d_25\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.6;\" x=\"68.378125\" xlink:href=\"#m02ac35c477\" y=\"192.418994\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"ytick_5\">\n     <g id=\"line2d_26\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.6;\" x=\"68.378125\" xlink:href=\"#m02ac35c477\" y=\"107.598777\"/>\n      </g>\n     </g>\n     <g id=\"text_6\">\n      <!-- $\\mathdefault{2\\times10^{-1}}$ -->\n      <defs>\n       <path d=\"M 70.125 53.71875 \nL 47.796875 31.296875 \nL 70.125 8.984375 \nL 64.3125 3.078125 \nL 41.890625 25.484375 \nL 19.484375 3.078125 \nL 13.71875 8.984375 \nL 35.984375 31.296875 \nL 13.71875 53.71875 \nL 19.484375 59.625 \nL 41.890625 37.203125 \nL 64.3125 59.625 \nz\n\" id=\"DejaVuSans-215\"/>\n      </defs>\n      <g transform=\"translate(20.878125 111.397996)scale(0.1 -0.1)\">\n       <use transform=\"translate(0 0.684375)\" xlink:href=\"#DejaVuSans-50\"/>\n       <use transform=\"translate(83.105469 0.684375)\" xlink:href=\"#DejaVuSans-215\"/>\n       <use transform=\"translate(186.376953 0.684375)\" xlink:href=\"#DejaVuSans-49\"/>\n       <use transform=\"translate(250 0.684375)\" xlink:href=\"#DejaVuSans-48\"/>\n       <use transform=\"translate(314.580078 38.965625)scale(0.7)\" xlink:href=\"#DejaVuSans-8722\"/>\n       <use transform=\"translate(373.232422 38.965625)scale(0.7)\" xlink:href=\"#DejaVuSans-49\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"ytick_6\">\n     <g id=\"line2d_27\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.6;\" x=\"68.378125\" xlink:href=\"#m02ac35c477\" y=\"64.528887\"/>\n      </g>\n     </g>\n     <g id=\"text_7\">\n      <!-- $\\mathdefault{3\\times10^{-1}}$ -->\n      <defs>\n       <path d=\"M 40.578125 39.3125 \nQ 47.65625 37.796875 51.625 33 \nQ 55.609375 28.21875 55.609375 21.1875 \nQ 55.609375 10.40625 48.1875 4.484375 \nQ 40.765625 -1.421875 27.09375 -1.421875 \nQ 22.515625 -1.421875 17.65625 -0.515625 \nQ 12.796875 0.390625 7.625 2.203125 \nL 7.625 11.71875 \nQ 11.71875 9.328125 16.59375 8.109375 \nQ 21.484375 6.890625 26.8125 6.890625 \nQ 36.078125 6.890625 40.9375 10.546875 \nQ 45.796875 14.203125 45.796875 21.1875 \nQ 45.796875 27.640625 41.28125 31.265625 \nQ 36.765625 34.90625 28.71875 34.90625 \nL 20.21875 34.90625 \nL 20.21875 43.015625 \nL 29.109375 43.015625 \nQ 36.375 43.015625 40.234375 45.921875 \nQ 44.09375 48.828125 44.09375 54.296875 \nQ 44.09375 59.90625 40.109375 62.90625 \nQ 36.140625 65.921875 28.71875 65.921875 \nQ 24.65625 65.921875 20.015625 65.03125 \nQ 15.375 64.15625 9.8125 62.3125 \nL 9.8125 71.09375 \nQ 15.4375 72.65625 20.34375 73.4375 \nQ 25.25 74.21875 29.59375 74.21875 \nQ 40.828125 74.21875 47.359375 69.109375 \nQ 53.90625 64.015625 53.90625 55.328125 \nQ 53.90625 49.265625 50.4375 45.09375 \nQ 46.96875 40.921875 40.578125 39.3125 \nz\n\" id=\"DejaVuSans-51\"/>\n      </defs>\n      <g transform=\"translate(20.878125 68.328106)scale(0.1 -0.1)\">\n       <use transform=\"translate(0 0.684375)\" xlink:href=\"#DejaVuSans-51\"/>\n       <use transform=\"translate(83.105469 0.684375)\" xlink:href=\"#DejaVuSans-215\"/>\n       <use transform=\"translate(186.376953 0.684375)\" xlink:href=\"#DejaVuSans-49\"/>\n       <use transform=\"translate(250 0.684375)\" xlink:href=\"#DejaVuSans-48\"/>\n       <use transform=\"translate(314.580078 38.965625)scale(0.7)\" xlink:href=\"#DejaVuSans-8722\"/>\n       <use transform=\"translate(373.232422 38.965625)scale(0.7)\" xlink:href=\"#DejaVuSans-49\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"ytick_7\">\n     <g id=\"line2d_28\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.6;\" x=\"68.378125\" xlink:href=\"#m02ac35c477\" y=\"33.970314\"/>\n      </g>\n     </g>\n     <g id=\"text_8\">\n      <!-- $\\mathdefault{4\\times10^{-1}}$ -->\n      <defs>\n       <path d=\"M 37.796875 64.3125 \nL 12.890625 25.390625 \nL 37.796875 25.390625 \nz\nM 35.203125 72.90625 \nL 47.609375 72.90625 \nL 47.609375 25.390625 \nL 58.015625 25.390625 \nL 58.015625 17.1875 \nL 47.609375 17.1875 \nL 47.609375 0 \nL 37.796875 0 \nL 37.796875 17.1875 \nL 4.890625 17.1875 \nL 4.890625 26.703125 \nz\n\" id=\"DejaVuSans-52\"/>\n      </defs>\n      <g transform=\"translate(20.878125 37.769533)scale(0.1 -0.1)\">\n       <use transform=\"translate(0 0.684375)\" xlink:href=\"#DejaVuSans-52\"/>\n       <use transform=\"translate(83.105469 0.684375)\" xlink:href=\"#DejaVuSans-215\"/>\n       <use transform=\"translate(186.376953 0.684375)\" xlink:href=\"#DejaVuSans-49\"/>\n       <use transform=\"translate(250 0.684375)\" xlink:href=\"#DejaVuSans-48\"/>\n       <use transform=\"translate(314.580078 38.965625)scale(0.7)\" xlink:href=\"#DejaVuSans-8722\"/>\n       <use transform=\"translate(373.232422 38.965625)scale(0.7)\" xlink:href=\"#DejaVuSans-49\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"text_9\">\n     <!-- Mean surface generation time per protein [s] -->\n     <defs>\n      <path d=\"M 9.8125 72.90625 \nL 24.515625 72.90625 \nL 43.109375 23.296875 \nL 61.8125 72.90625 \nL 76.515625 72.90625 \nL 76.515625 0 \nL 66.890625 0 \nL 66.890625 64.015625 \nL 48.09375 14.015625 \nL 38.1875 14.015625 \nL 19.390625 64.015625 \nL 19.390625 0 \nL 9.8125 0 \nz\n\" id=\"DejaVuSans-77\"/>\n      <path d=\"M 54.890625 33.015625 \nL 54.890625 0 \nL 45.90625 0 \nL 45.90625 32.71875 \nQ 45.90625 40.484375 42.875 44.328125 \nQ 39.84375 48.1875 33.796875 48.1875 \nQ 26.515625 48.1875 22.3125 43.546875 \nQ 18.109375 38.921875 18.109375 30.90625 \nL 18.109375 0 \nL 9.078125 0 \nL 9.078125 54.6875 \nL 18.109375 54.6875 \nL 18.109375 46.1875 \nQ 21.34375 51.125 25.703125 53.5625 \nQ 30.078125 56 35.796875 56 \nQ 45.21875 56 50.046875 50.171875 \nQ 54.890625 44.34375 54.890625 33.015625 \nz\n\" id=\"DejaVuSans-110\"/>\n      <path d=\"M 8.5 21.578125 \nL 8.5 54.6875 \nL 17.484375 54.6875 \nL 17.484375 21.921875 \nQ 17.484375 14.15625 20.5 10.265625 \nQ 23.53125 6.390625 29.59375 6.390625 \nQ 36.859375 6.390625 41.078125 11.03125 \nQ 45.3125 15.671875 45.3125 23.6875 \nL 45.3125 54.6875 \nL 54.296875 54.6875 \nL 54.296875 0 \nL 45.3125 0 \nL 45.3125 8.40625 \nQ 42.046875 3.421875 37.71875 1 \nQ 33.40625 -1.421875 27.6875 -1.421875 \nQ 18.265625 -1.421875 13.375 4.4375 \nQ 8.5 10.296875 8.5 21.578125 \nz\nM 31.109375 56 \nz\n\" id=\"DejaVuSans-117\"/>\n      <path d=\"M 41.109375 46.296875 \nQ 39.59375 47.171875 37.8125 47.578125 \nQ 36.03125 48 33.890625 48 \nQ 26.265625 48 22.1875 43.046875 \nQ 18.109375 38.09375 18.109375 28.8125 \nL 18.109375 0 \nL 9.078125 0 \nL 9.078125 54.6875 \nL 18.109375 54.6875 \nL 18.109375 46.1875 \nQ 20.953125 51.171875 25.484375 53.578125 \nQ 30.03125 56 36.53125 56 \nQ 37.453125 56 38.578125 55.875 \nQ 39.703125 55.765625 41.0625 55.515625 \nz\n\" id=\"DejaVuSans-114\"/>\n      <path d=\"M 37.109375 75.984375 \nL 37.109375 68.5 \nL 28.515625 68.5 \nQ 23.6875 68.5 21.796875 66.546875 \nQ 19.921875 64.59375 19.921875 59.515625 \nL 19.921875 54.6875 \nL 34.71875 54.6875 \nL 34.71875 47.703125 \nL 19.921875 47.703125 \nL 19.921875 0 \nL 10.890625 0 \nL 10.890625 47.703125 \nL 2.296875 47.703125 \nL 2.296875 54.6875 \nL 10.890625 54.6875 \nL 10.890625 58.5 \nQ 10.890625 67.625 15.140625 71.796875 \nQ 19.390625 75.984375 28.609375 75.984375 \nz\n\" id=\"DejaVuSans-102\"/>\n      <path d=\"M 45.40625 27.984375 \nQ 45.40625 37.75 41.375 43.109375 \nQ 37.359375 48.484375 30.078125 48.484375 \nQ 22.859375 48.484375 18.828125 43.109375 \nQ 14.796875 37.75 14.796875 27.984375 \nQ 14.796875 18.265625 18.828125 12.890625 \nQ 22.859375 7.515625 30.078125 7.515625 \nQ 37.359375 7.515625 41.375 12.890625 \nQ 45.40625 18.265625 45.40625 27.984375 \nz\nM 54.390625 6.78125 \nQ 54.390625 -7.171875 48.1875 -13.984375 \nQ 42 -20.796875 29.203125 -20.796875 \nQ 24.46875 -20.796875 20.265625 -20.09375 \nQ 16.0625 -19.390625 12.109375 -17.921875 \nL 12.109375 -9.1875 \nQ 16.0625 -11.328125 19.921875 -12.34375 \nQ 23.78125 -13.375 27.78125 -13.375 \nQ 36.625 -13.375 41.015625 -8.765625 \nQ 45.40625 -4.15625 45.40625 5.171875 \nL 45.40625 9.625 \nQ 42.625 4.78125 38.28125 2.390625 \nQ 33.9375 0 27.875 0 \nQ 17.828125 0 11.671875 7.65625 \nQ 5.515625 15.328125 5.515625 27.984375 \nQ 5.515625 40.671875 11.671875 48.328125 \nQ 17.828125 56 27.875 56 \nQ 33.9375 56 38.28125 53.609375 \nQ 42.625 51.21875 45.40625 46.390625 \nL 45.40625 54.6875 \nL 54.390625 54.6875 \nz\n\" id=\"DejaVuSans-103\"/>\n      <path d=\"M 30.609375 48.390625 \nQ 23.390625 48.390625 19.1875 42.75 \nQ 14.984375 37.109375 14.984375 27.296875 \nQ 14.984375 17.484375 19.15625 11.84375 \nQ 23.34375 6.203125 30.609375 6.203125 \nQ 37.796875 6.203125 41.984375 11.859375 \nQ 46.1875 17.53125 46.1875 27.296875 \nQ 46.1875 37.015625 41.984375 42.703125 \nQ 37.796875 48.390625 30.609375 48.390625 \nz\nM 30.609375 56 \nQ 42.328125 56 49.015625 48.375 \nQ 55.71875 40.765625 55.71875 27.296875 \nQ 55.71875 13.875 49.015625 6.21875 \nQ 42.328125 -1.421875 30.609375 -1.421875 \nQ 18.84375 -1.421875 12.171875 6.21875 \nQ 5.515625 13.875 5.515625 27.296875 \nQ 5.515625 40.765625 12.171875 48.375 \nQ 18.84375 56 30.609375 56 \nz\n\" id=\"DejaVuSans-111\"/>\n      <path d=\"M 52 44.1875 \nQ 55.375 50.25 60.0625 53.125 \nQ 64.75 56 71.09375 56 \nQ 79.640625 56 84.28125 50.015625 \nQ 88.921875 44.046875 88.921875 33.015625 \nL 88.921875 0 \nL 79.890625 0 \nL 79.890625 32.71875 \nQ 79.890625 40.578125 77.09375 44.375 \nQ 74.3125 48.1875 68.609375 48.1875 \nQ 61.625 48.1875 57.5625 43.546875 \nQ 53.515625 38.921875 53.515625 30.90625 \nL 53.515625 0 \nL 44.484375 0 \nL 44.484375 32.71875 \nQ 44.484375 40.625 41.703125 44.40625 \nQ 38.921875 48.1875 33.109375 48.1875 \nQ 26.21875 48.1875 22.15625 43.53125 \nQ 18.109375 38.875 18.109375 30.90625 \nL 18.109375 0 \nL 9.078125 0 \nL 9.078125 54.6875 \nL 18.109375 54.6875 \nL 18.109375 46.1875 \nQ 21.1875 51.21875 25.484375 53.609375 \nQ 29.78125 56 35.6875 56 \nQ 41.65625 56 45.828125 52.96875 \nQ 50 49.953125 52 44.1875 \nz\n\" id=\"DejaVuSans-109\"/>\n      <path d=\"M 18.109375 8.203125 \nL 18.109375 -20.796875 \nL 9.078125 -20.796875 \nL 9.078125 54.6875 \nL 18.109375 54.6875 \nL 18.109375 46.390625 \nQ 20.953125 51.265625 25.265625 53.625 \nQ 29.59375 56 35.59375 56 \nQ 45.5625 56 51.78125 48.09375 \nQ 58.015625 40.1875 58.015625 27.296875 \nQ 58.015625 14.40625 51.78125 6.484375 \nQ 45.5625 -1.421875 35.59375 -1.421875 \nQ 29.59375 -1.421875 25.265625 0.953125 \nQ 20.953125 3.328125 18.109375 8.203125 \nz\nM 48.6875 27.296875 \nQ 48.6875 37.203125 44.609375 42.84375 \nQ 40.53125 48.484375 33.40625 48.484375 \nQ 26.265625 48.484375 22.1875 42.84375 \nQ 18.109375 37.203125 18.109375 27.296875 \nQ 18.109375 17.390625 22.1875 11.75 \nQ 26.265625 6.109375 33.40625 6.109375 \nQ 40.53125 6.109375 44.609375 11.75 \nQ 48.6875 17.390625 48.6875 27.296875 \nz\n\" id=\"DejaVuSans-112\"/>\n      <path d=\"M 8.59375 75.984375 \nL 29.296875 75.984375 \nL 29.296875 69 \nL 17.578125 69 \nL 17.578125 -6.203125 \nL 29.296875 -6.203125 \nL 29.296875 -13.1875 \nL 8.59375 -13.1875 \nz\n\" id=\"DejaVuSans-91\"/>\n      <path d=\"M 30.421875 75.984375 \nL 30.421875 -13.1875 \nL 9.71875 -13.1875 \nL 9.71875 -6.203125 \nL 21.390625 -6.203125 \nL 21.390625 69 \nL 9.71875 69 \nL 9.71875 75.984375 \nz\n\" id=\"DejaVuSans-93\"/>\n     </defs>\n     <g transform=\"translate(14.798438 232.61875)rotate(-90)scale(0.1 -0.1)\">\n      <use xlink:href=\"#DejaVuSans-77\"/>\n      <use x=\"86.279297\" xlink:href=\"#DejaVuSans-101\"/>\n      <use x=\"147.802734\" xlink:href=\"#DejaVuSans-97\"/>\n      <use x=\"209.082031\" xlink:href=\"#DejaVuSans-110\"/>\n      <use x=\"272.460938\" xlink:href=\"#DejaVuSans-32\"/>\n      <use x=\"304.248047\" xlink:href=\"#DejaVuSans-115\"/>\n      <use x=\"356.347656\" xlink:href=\"#DejaVuSans-117\"/>\n      <use x=\"419.726562\" xlink:href=\"#DejaVuSans-114\"/>\n      <use x=\"460.839844\" xlink:href=\"#DejaVuSans-102\"/>\n      <use x=\"496.044922\" xlink:href=\"#DejaVuSans-97\"/>\n      <use x=\"557.324219\" xlink:href=\"#DejaVuSans-99\"/>\n      <use x=\"612.304688\" xlink:href=\"#DejaVuSans-101\"/>\n      <use x=\"673.828125\" xlink:href=\"#DejaVuSans-32\"/>\n      <use x=\"705.615234\" xlink:href=\"#DejaVuSans-103\"/>\n      <use x=\"769.091797\" xlink:href=\"#DejaVuSans-101\"/>\n      <use x=\"830.615234\" xlink:href=\"#DejaVuSans-110\"/>\n      <use x=\"893.994141\" xlink:href=\"#DejaVuSans-101\"/>\n      <use x=\"955.517578\" xlink:href=\"#DejaVuSans-114\"/>\n      <use x=\"996.630859\" xlink:href=\"#DejaVuSans-97\"/>\n      <use x=\"1057.910156\" xlink:href=\"#DejaVuSans-116\"/>\n      <use x=\"1097.119141\" xlink:href=\"#DejaVuSans-105\"/>\n      <use x=\"1124.902344\" xlink:href=\"#DejaVuSans-111\"/>\n      <use x=\"1186.083984\" xlink:href=\"#DejaVuSans-110\"/>\n      <use x=\"1249.462891\" xlink:href=\"#DejaVuSans-32\"/>\n      <use x=\"1281.25\" xlink:href=\"#DejaVuSans-116\"/>\n      <use x=\"1320.458984\" xlink:href=\"#DejaVuSans-105\"/>\n      <use x=\"1348.242188\" xlink:href=\"#DejaVuSans-109\"/>\n      <use x=\"1445.654297\" xlink:href=\"#DejaVuSans-101\"/>\n      <use x=\"1507.177734\" xlink:href=\"#DejaVuSans-32\"/>\n      <use x=\"1538.964844\" xlink:href=\"#DejaVuSans-112\"/>\n      <use x=\"1602.441406\" xlink:href=\"#DejaVuSans-101\"/>\n      <use x=\"1663.964844\" xlink:href=\"#DejaVuSans-114\"/>\n      <use x=\"1705.078125\" xlink:href=\"#DejaVuSans-32\"/>\n      <use x=\"1736.865234\" xlink:href=\"#DejaVuSans-112\"/>\n      <use x=\"1800.341797\" xlink:href=\"#DejaVuSans-114\"/>\n      <use x=\"1839.205078\" xlink:href=\"#DejaVuSans-111\"/>\n      <use x=\"1900.386719\" xlink:href=\"#DejaVuSans-116\"/>\n      <use x=\"1939.595703\" xlink:href=\"#DejaVuSans-101\"/>\n      <use x=\"2001.119141\" xlink:href=\"#DejaVuSans-105\"/>\n      <use x=\"2028.902344\" xlink:href=\"#DejaVuSans-110\"/>\n      <use x=\"2092.28125\" xlink:href=\"#DejaVuSans-32\"/>\n      <use x=\"2124.068359\" xlink:href=\"#DejaVuSans-91\"/>\n      <use x=\"2163.082031\" xlink:href=\"#DejaVuSans-115\"/>\n      <use x=\"2215.181641\" xlink:href=\"#DejaVuSans-93\"/>\n     </g>\n    </g>\n   </g>\n   <g id=\"line2d_29\">\n    <path clip-path=\"url(#p7fec2d3163)\" d=\"M 83.596307 21.073011 \nL 127.076826 86.366217 \nL 170.557346 142.870938 \nL 214.037865 181.568635 \nL 257.518385 199.875337 \nL 300.998904 211.93939 \nL 344.479424 215.836296 \nL 387.959943 218.745739 \n\" style=\"fill:none;stroke:#1f77b4;stroke-linecap:square;stroke-width:1.5;\"/>\n   </g>\n   <g id=\"patch_3\">\n    <path d=\"M 68.378125 228.629375 \nL 68.378125 11.189375 \n\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;stroke-width:0.8;\"/>\n   </g>\n   <g id=\"patch_4\">\n    <path d=\"M 403.178125 228.629375 \nL 403.178125 11.189375 \n\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;stroke-width:0.8;\"/>\n   </g>\n   <g id=\"patch_5\">\n    <path d=\"M 68.378125 228.629375 \nL 403.178125 228.629375 \n\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;stroke-width:0.8;\"/>\n   </g>\n   <g id=\"patch_6\">\n    <path d=\"M 68.378125 11.189375 \nL 403.178125 11.189375 \n\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;stroke-width:0.8;\"/>\n   </g>\n  </g>\n </g>\n <defs>\n  <clipPath id=\"p7fec2d3163\">\n   <rect height=\"217.44\" width=\"334.8\" x=\"68.378125\" y=\"11.189375\"/>\n  </clipPath>\n </defs>\n</svg>\n", "image/png": "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\n"}, "metadata": {"needs_background": "light"}}], "source": ["plt.plot(batch_sizes,time_means)\n", "plt.xlabel('Batch size')\n", "plt.ylabel('Mean surface generation time per protein [s]')\n", "plt.xscale('log')\n", "plt.yscale('log')"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"output_type": "display_data", "data": {"text/plain": "<Figure size 432x288 with 1 Axes>", "image/svg+xml": "<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n<!-- Created with matplotlib (https://matplotlib.org/) -->\n<svg height=\"262.19625pt\" version=\"1.1\" viewBox=\"0 0 387.478125 262.19625\" width=\"387.478125pt\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\">\n <defs>\n  <style type=\"text/css\">\n*{stroke-linecap:butt;stroke-linejoin:round;}\n  </style>\n </defs>\n <g id=\"figure_1\">\n  <g id=\"patch_1\">\n   <path d=\"M 0 262.19625 \nL 387.478125 262.19625 \nL 387.478125 0 \nL 0 0 \nz\n\" style=\"fill:none;\"/>\n  </g>\n  <g id=\"axes_1\">\n   <g id=\"patch_2\">\n    <path d=\"M 45.478125 224.64 \nL 380.278125 224.64 \nL 380.278125 7.2 \nL 45.478125 7.2 \nz\n\" style=\"fill:#ffffff;\"/>\n   </g>\n   <g id=\"matplotlib.axis_1\">\n    <g id=\"xtick_1\">\n     <g id=\"line2d_1\">\n      <defs>\n       <path d=\"M 0 0 \nL 0 3.5 \n\" id=\"m6e4ea7cf1c\" style=\"stroke:#000000;stroke-width:0.8;\"/>\n      </defs>\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"60.696307\" xlink:href=\"#m6e4ea7cf1c\" y=\"224.64\"/>\n      </g>\n     </g>\n     <g id=\"text_1\">\n      <!-- $\\mathdefault{10^{0}}$ -->\n      <defs>\n       <path d=\"M 12.40625 8.296875 \nL 28.515625 8.296875 \nL 28.515625 63.921875 \nL 10.984375 60.40625 \nL 10.984375 69.390625 \nL 28.421875 72.90625 \nL 38.28125 72.90625 \nL 38.28125 8.296875 \nL 54.390625 8.296875 \nL 54.390625 0 \nL 12.40625 0 \nz\n\" id=\"DejaVuSans-49\"/>\n       <path d=\"M 31.78125 66.40625 \nQ 24.171875 66.40625 20.328125 58.90625 \nQ 16.5 51.421875 16.5 36.375 \nQ 16.5 21.390625 20.328125 13.890625 \nQ 24.171875 6.390625 31.78125 6.390625 \nQ 39.453125 6.390625 43.28125 13.890625 \nQ 47.125 21.390625 47.125 36.375 \nQ 47.125 51.421875 43.28125 58.90625 \nQ 39.453125 66.40625 31.78125 66.40625 \nz\nM 31.78125 74.21875 \nQ 44.046875 74.21875 50.515625 64.515625 \nQ 56.984375 54.828125 56.984375 36.375 \nQ 56.984375 17.96875 50.515625 8.265625 \nQ 44.046875 -1.421875 31.78125 -1.421875 \nQ 19.53125 -1.421875 13.0625 8.265625 \nQ 6.59375 17.96875 6.59375 36.375 \nQ 6.59375 54.828125 13.0625 64.515625 \nQ 19.53125 74.21875 31.78125 74.21875 \nz\n\" id=\"DejaVuSans-48\"/>\n      </defs>\n      <g transform=\"translate(51.896307 239.238437)scale(0.1 -0.1)\">\n       <use transform=\"translate(0 0.765625)\" xlink:href=\"#DejaVuSans-49\"/>\n       <use transform=\"translate(63.623047 0.765625)\" xlink:href=\"#DejaVuSans-48\"/>\n       <use transform=\"translate(128.203125 39.046875)scale(0.7)\" xlink:href=\"#DejaVuSans-48\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_2\">\n     <g id=\"line2d_2\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"205.135466\" xlink:href=\"#m6e4ea7cf1c\" y=\"224.64\"/>\n      </g>\n     </g>\n     <g id=\"text_2\">\n      <!-- $\\mathdefault{10^{1}}$ -->\n      <g transform=\"translate(196.335466 239.238437)scale(0.1 -0.1)\">\n       <use transform=\"translate(0 0.684375)\" xlink:href=\"#DejaVuSans-49\"/>\n       <use transform=\"translate(63.623047 0.684375)\" xlink:href=\"#DejaVuSans-48\"/>\n       <use transform=\"translate(128.203125 38.965625)scale(0.7)\" xlink:href=\"#DejaVuSans-49\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_3\">\n     <g id=\"line2d_3\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"349.574625\" xlink:href=\"#m6e4ea7cf1c\" y=\"224.64\"/>\n      </g>\n     </g>\n     <g id=\"text_3\">\n      <!-- $\\mathdefault{10^{2}}$ -->\n      <defs>\n       <path d=\"M 19.1875 8.296875 \nL 53.609375 8.296875 \nL 53.609375 0 \nL 7.328125 0 \nL 7.328125 8.296875 \nQ 12.9375 14.109375 22.625 23.890625 \nQ 32.328125 33.6875 34.8125 36.53125 \nQ 39.546875 41.84375 41.421875 45.53125 \nQ 43.3125 49.21875 43.3125 52.78125 \nQ 43.3125 58.59375 39.234375 62.25 \nQ 35.15625 65.921875 28.609375 65.921875 \nQ 23.96875 65.921875 18.8125 64.3125 \nQ 13.671875 62.703125 7.8125 59.421875 \nL 7.8125 69.390625 \nQ 13.765625 71.78125 18.9375 73 \nQ 24.125 74.21875 28.421875 74.21875 \nQ 39.75 74.21875 46.484375 68.546875 \nQ 53.21875 62.890625 53.21875 53.421875 \nQ 53.21875 48.921875 51.53125 44.890625 \nQ 49.859375 40.875 45.40625 35.40625 \nQ 44.1875 33.984375 37.640625 27.21875 \nQ 31.109375 20.453125 19.1875 8.296875 \nz\n\" id=\"DejaVuSans-50\"/>\n      </defs>\n      <g transform=\"translate(340.774625 239.238437)scale(0.1 -0.1)\">\n       <use transform=\"translate(0 0.765625)\" xlink:href=\"#DejaVuSans-49\"/>\n       <use transform=\"translate(63.623047 0.765625)\" xlink:href=\"#DejaVuSans-48\"/>\n       <use transform=\"translate(128.203125 39.046875)scale(0.7)\" xlink:href=\"#DejaVuSans-50\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_4\">\n     <g id=\"line2d_4\">\n      <defs>\n       <path d=\"M 0 0 \nL 0 2 \n\" id=\"me01f1e92d9\" style=\"stroke:#000000;stroke-width:0.6;\"/>\n      </defs>\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.6;\" x=\"46.698706\" xlink:href=\"#me01f1e92d9\" y=\"224.64\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_5\">\n     <g id=\"line2d_5\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.6;\" x=\"54.087133\" xlink:href=\"#me01f1e92d9\" y=\"224.64\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_6\">\n     <g id=\"line2d_6\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.6;\" x=\"104.176826\" xlink:href=\"#me01f1e92d9\" y=\"224.64\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_7\">\n     <g id=\"line2d_7\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.6;\" x=\"129.6113\" xlink:href=\"#me01f1e92d9\" y=\"224.64\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_8\">\n     <g id=\"line2d_8\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.6;\" x=\"147.657346\" xlink:href=\"#me01f1e92d9\" y=\"224.64\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_9\">\n     <g id=\"line2d_9\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.6;\" x=\"161.654947\" xlink:href=\"#me01f1e92d9\" y=\"224.64\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_10\">\n     <g id=\"line2d_10\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.6;\" x=\"173.091819\" xlink:href=\"#me01f1e92d9\" y=\"224.64\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_11\">\n     <g id=\"line2d_11\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.6;\" x=\"182.761557\" xlink:href=\"#me01f1e92d9\" y=\"224.64\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_12\">\n     <g id=\"line2d_12\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.6;\" x=\"191.137865\" xlink:href=\"#me01f1e92d9\" y=\"224.64\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_13\">\n     <g id=\"line2d_13\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.6;\" x=\"198.526293\" xlink:href=\"#me01f1e92d9\" y=\"224.64\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_14\">\n     <g id=\"line2d_14\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.6;\" x=\"248.615986\" xlink:href=\"#me01f1e92d9\" y=\"224.64\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_15\">\n     <g id=\"line2d_15\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.6;\" x=\"274.050459\" xlink:href=\"#me01f1e92d9\" y=\"224.64\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_16\">\n     <g id=\"line2d_16\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.6;\" x=\"292.096505\" xlink:href=\"#me01f1e92d9\" y=\"224.64\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_17\">\n     <g id=\"line2d_17\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.6;\" x=\"306.094106\" xlink:href=\"#me01f1e92d9\" y=\"224.64\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_18\">\n     <g id=\"line2d_18\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.6;\" x=\"317.530978\" xlink:href=\"#me01f1e92d9\" y=\"224.64\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_19\">\n     <g id=\"line2d_19\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.6;\" x=\"327.200716\" xlink:href=\"#me01f1e92d9\" y=\"224.64\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_20\">\n     <g id=\"line2d_20\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.6;\" x=\"335.577025\" xlink:href=\"#me01f1e92d9\" y=\"224.64\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_21\">\n     <g id=\"line2d_21\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.6;\" x=\"342.965452\" xlink:href=\"#me01f1e92d9\" y=\"224.64\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"text_4\">\n     <!-- Batch size -->\n     <defs>\n      <path d=\"M 19.671875 34.8125 \nL 19.671875 8.109375 \nL 35.5 8.109375 \nQ 43.453125 8.109375 47.28125 11.40625 \nQ 51.125 14.703125 51.125 21.484375 \nQ 51.125 28.328125 47.28125 31.5625 \nQ 43.453125 34.8125 35.5 34.8125 \nz\nM 19.671875 64.796875 \nL 19.671875 42.828125 \nL 34.28125 42.828125 \nQ 41.5 42.828125 45.03125 45.53125 \nQ 48.578125 48.25 48.578125 53.8125 \nQ 48.578125 59.328125 45.03125 62.0625 \nQ 41.5 64.796875 34.28125 64.796875 \nz\nM 9.8125 72.90625 \nL 35.015625 72.90625 \nQ 46.296875 72.90625 52.390625 68.21875 \nQ 58.5 63.53125 58.5 54.890625 \nQ 58.5 48.1875 55.375 44.234375 \nQ 52.25 40.28125 46.1875 39.3125 \nQ 53.46875 37.75 57.5 32.78125 \nQ 61.53125 27.828125 61.53125 20.40625 \nQ 61.53125 10.640625 54.890625 5.3125 \nQ 48.25 0 35.984375 0 \nL 9.8125 0 \nz\n\" id=\"DejaVuSans-66\"/>\n      <path d=\"M 34.28125 27.484375 \nQ 23.390625 27.484375 19.1875 25 \nQ 14.984375 22.515625 14.984375 16.5 \nQ 14.984375 11.71875 18.140625 8.90625 \nQ 21.296875 6.109375 26.703125 6.109375 \nQ 34.1875 6.109375 38.703125 11.40625 \nQ 43.21875 16.703125 43.21875 25.484375 \nL 43.21875 27.484375 \nz\nM 52.203125 31.203125 \nL 52.203125 0 \nL 43.21875 0 \nL 43.21875 8.296875 \nQ 40.140625 3.328125 35.546875 0.953125 \nQ 30.953125 -1.421875 24.3125 -1.421875 \nQ 15.921875 -1.421875 10.953125 3.296875 \nQ 6 8.015625 6 15.921875 \nQ 6 25.140625 12.171875 29.828125 \nQ 18.359375 34.515625 30.609375 34.515625 \nL 43.21875 34.515625 \nL 43.21875 35.40625 \nQ 43.21875 41.609375 39.140625 45 \nQ 35.0625 48.390625 27.6875 48.390625 \nQ 23 48.390625 18.546875 47.265625 \nQ 14.109375 46.140625 10.015625 43.890625 \nL 10.015625 52.203125 \nQ 14.9375 54.109375 19.578125 55.046875 \nQ 24.21875 56 28.609375 56 \nQ 40.484375 56 46.34375 49.84375 \nQ 52.203125 43.703125 52.203125 31.203125 \nz\n\" id=\"DejaVuSans-97\"/>\n      <path d=\"M 18.3125 70.21875 \nL 18.3125 54.6875 \nL 36.8125 54.6875 \nL 36.8125 47.703125 \nL 18.3125 47.703125 \nL 18.3125 18.015625 \nQ 18.3125 11.328125 20.140625 9.421875 \nQ 21.96875 7.515625 27.59375 7.515625 \nL 36.8125 7.515625 \nL 36.8125 0 \nL 27.59375 0 \nQ 17.1875 0 13.234375 3.875 \nQ 9.28125 7.765625 9.28125 18.015625 \nL 9.28125 47.703125 \nL 2.6875 47.703125 \nL 2.6875 54.6875 \nL 9.28125 54.6875 \nL 9.28125 70.21875 \nz\n\" id=\"DejaVuSans-116\"/>\n      <path d=\"M 48.78125 52.59375 \nL 48.78125 44.1875 \nQ 44.96875 46.296875 41.140625 47.34375 \nQ 37.3125 48.390625 33.40625 48.390625 \nQ 24.65625 48.390625 19.8125 42.84375 \nQ 14.984375 37.3125 14.984375 27.296875 \nQ 14.984375 17.28125 19.8125 11.734375 \nQ 24.65625 6.203125 33.40625 6.203125 \nQ 37.3125 6.203125 41.140625 7.25 \nQ 44.96875 8.296875 48.78125 10.40625 \nL 48.78125 2.09375 \nQ 45.015625 0.34375 40.984375 -0.53125 \nQ 36.96875 -1.421875 32.421875 -1.421875 \nQ 20.0625 -1.421875 12.78125 6.34375 \nQ 5.515625 14.109375 5.515625 27.296875 \nQ 5.515625 40.671875 12.859375 48.328125 \nQ 20.21875 56 33.015625 56 \nQ 37.15625 56 41.109375 55.140625 \nQ 45.0625 54.296875 48.78125 52.59375 \nz\n\" id=\"DejaVuSans-99\"/>\n      <path d=\"M 54.890625 33.015625 \nL 54.890625 0 \nL 45.90625 0 \nL 45.90625 32.71875 \nQ 45.90625 40.484375 42.875 44.328125 \nQ 39.84375 48.1875 33.796875 48.1875 \nQ 26.515625 48.1875 22.3125 43.546875 \nQ 18.109375 38.921875 18.109375 30.90625 \nL 18.109375 0 \nL 9.078125 0 \nL 9.078125 75.984375 \nL 18.109375 75.984375 \nL 18.109375 46.1875 \nQ 21.34375 51.125 25.703125 53.5625 \nQ 30.078125 56 35.796875 56 \nQ 45.21875 56 50.046875 50.171875 \nQ 54.890625 44.34375 54.890625 33.015625 \nz\n\" id=\"DejaVuSans-104\"/>\n      <path id=\"DejaVuSans-32\"/>\n      <path d=\"M 44.28125 53.078125 \nL 44.28125 44.578125 \nQ 40.484375 46.53125 36.375 47.5 \nQ 32.28125 48.484375 27.875 48.484375 \nQ 21.1875 48.484375 17.84375 46.4375 \nQ 14.5 44.390625 14.5 40.28125 \nQ 14.5 37.15625 16.890625 35.375 \nQ 19.28125 33.59375 26.515625 31.984375 \nL 29.59375 31.296875 \nQ 39.15625 29.25 43.1875 25.515625 \nQ 47.21875 21.78125 47.21875 15.09375 \nQ 47.21875 7.46875 41.1875 3.015625 \nQ 35.15625 -1.421875 24.609375 -1.421875 \nQ 20.21875 -1.421875 15.453125 -0.5625 \nQ 10.6875 0.296875 5.421875 2 \nL 5.421875 11.28125 \nQ 10.40625 8.6875 15.234375 7.390625 \nQ 20.0625 6.109375 24.8125 6.109375 \nQ 31.15625 6.109375 34.5625 8.28125 \nQ 37.984375 10.453125 37.984375 14.40625 \nQ 37.984375 18.0625 35.515625 20.015625 \nQ 33.0625 21.96875 24.703125 23.78125 \nL 21.578125 24.515625 \nQ 13.234375 26.265625 9.515625 29.90625 \nQ 5.8125 33.546875 5.8125 39.890625 \nQ 5.8125 47.609375 11.28125 51.796875 \nQ 16.75 56 26.8125 56 \nQ 31.78125 56 36.171875 55.265625 \nQ 40.578125 54.546875 44.28125 53.078125 \nz\n\" id=\"DejaVuSans-115\"/>\n      <path d=\"M 9.421875 54.6875 \nL 18.40625 54.6875 \nL 18.40625 0 \nL 9.421875 0 \nz\nM 9.421875 75.984375 \nL 18.40625 75.984375 \nL 18.40625 64.59375 \nL 9.421875 64.59375 \nz\n\" id=\"DejaVuSans-105\"/>\n      <path d=\"M 5.515625 54.6875 \nL 48.1875 54.6875 \nL 48.1875 46.484375 \nL 14.40625 7.171875 \nL 48.1875 7.171875 \nL 48.1875 0 \nL 4.296875 0 \nL 4.296875 8.203125 \nL 38.09375 47.515625 \nL 5.515625 47.515625 \nz\n\" id=\"DejaVuSans-122\"/>\n      <path d=\"M 56.203125 29.59375 \nL 56.203125 25.203125 \nL 14.890625 25.203125 \nQ 15.484375 15.921875 20.484375 11.0625 \nQ 25.484375 6.203125 34.421875 6.203125 \nQ 39.59375 6.203125 44.453125 7.46875 \nQ 49.3125 8.734375 54.109375 11.28125 \nL 54.109375 2.78125 \nQ 49.265625 0.734375 44.1875 -0.34375 \nQ 39.109375 -1.421875 33.890625 -1.421875 \nQ 20.796875 -1.421875 13.15625 6.1875 \nQ 5.515625 13.8125 5.515625 26.8125 \nQ 5.515625 40.234375 12.765625 48.109375 \nQ 20.015625 56 32.328125 56 \nQ 43.359375 56 49.78125 48.890625 \nQ 56.203125 41.796875 56.203125 29.59375 \nz\nM 47.21875 32.234375 \nQ 47.125 39.59375 43.09375 43.984375 \nQ 39.0625 48.390625 32.421875 48.390625 \nQ 24.90625 48.390625 20.390625 44.140625 \nQ 15.875 39.890625 15.1875 32.171875 \nz\n\" id=\"DejaVuSans-101\"/>\n     </defs>\n     <g transform=\"translate(187.221875 252.916562)scale(0.1 -0.1)\">\n      <use xlink:href=\"#DejaVuSans-66\"/>\n      <use x=\"68.603516\" xlink:href=\"#DejaVuSans-97\"/>\n      <use x=\"129.882812\" xlink:href=\"#DejaVuSans-116\"/>\n      <use x=\"169.091797\" xlink:href=\"#DejaVuSans-99\"/>\n      <use x=\"224.072266\" xlink:href=\"#DejaVuSans-104\"/>\n      <use x=\"287.451172\" xlink:href=\"#DejaVuSans-32\"/>\n      <use x=\"319.238281\" xlink:href=\"#DejaVuSans-115\"/>\n      <use x=\"371.337891\" xlink:href=\"#DejaVuSans-105\"/>\n      <use x=\"399.121094\" xlink:href=\"#DejaVuSans-122\"/>\n      <use x=\"451.611328\" xlink:href=\"#DejaVuSans-101\"/>\n     </g>\n    </g>\n   </g>\n   <g id=\"matplotlib.axis_2\">\n    <g id=\"ytick_1\">\n     <g id=\"line2d_22\">\n      <defs>\n       <path d=\"M 0 0 \nL -3.5 0 \n\" id=\"mc91366a183\" style=\"stroke:#000000;stroke-width:0.8;\"/>\n      </defs>\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"45.478125\" xlink:href=\"#mc91366a183\" y=\"78.104544\"/>\n      </g>\n     </g>\n     <g id=\"text_5\">\n      <!-- $\\mathdefault{10^{2}}$ -->\n      <g transform=\"translate(20.878125 81.903763)scale(0.1 -0.1)\">\n       <use transform=\"translate(0 0.765625)\" xlink:href=\"#DejaVuSans-49\"/>\n       <use transform=\"translate(63.623047 0.765625)\" xlink:href=\"#DejaVuSans-48\"/>\n       <use transform=\"translate(128.203125 39.046875)scale(0.7)\" xlink:href=\"#DejaVuSans-50\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"ytick_2\">\n     <g id=\"line2d_23\">\n      <defs>\n       <path d=\"M 0 0 \nL -2 0 \n\" id=\"m33f72b9010\" style=\"stroke:#000000;stroke-width:0.6;\"/>\n      </defs>\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.6;\" x=\"45.478125\" xlink:href=\"#m33f72b9010\" y=\"192.75385\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"ytick_3\">\n     <g id=\"line2d_24\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.6;\" x=\"45.478125\" xlink:href=\"#m33f72b9010\" y=\"163.870292\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"ytick_4\">\n     <g id=\"line2d_25\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.6;\" x=\"45.478125\" xlink:href=\"#m33f72b9010\" y=\"143.377082\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"ytick_5\">\n     <g id=\"line2d_26\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.6;\" x=\"45.478125\" xlink:href=\"#m33f72b9010\" y=\"127.481313\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"ytick_6\">\n     <g id=\"line2d_27\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.6;\" x=\"45.478125\" xlink:href=\"#m33f72b9010\" y=\"114.493524\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"ytick_7\">\n     <g id=\"line2d_28\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.6;\" x=\"45.478125\" xlink:href=\"#m33f72b9010\" y=\"103.512505\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"ytick_8\">\n     <g id=\"line2d_29\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.6;\" x=\"45.478125\" xlink:href=\"#m33f72b9010\" y=\"94.000313\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"ytick_9\">\n     <g id=\"line2d_30\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.6;\" x=\"45.478125\" xlink:href=\"#m33f72b9010\" y=\"85.609966\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"ytick_10\">\n     <g id=\"line2d_31\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.6;\" x=\"45.478125\" xlink:href=\"#m33f72b9010\" y=\"28.727776\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"text_6\">\n     <!-- Mean GPU memory usage per protein [MB] -->\n     <defs>\n      <path d=\"M 9.8125 72.90625 \nL 24.515625 72.90625 \nL 43.109375 23.296875 \nL 61.8125 72.90625 \nL 76.515625 72.90625 \nL 76.515625 0 \nL 66.890625 0 \nL 66.890625 64.015625 \nL 48.09375 14.015625 \nL 38.1875 14.015625 \nL 19.390625 64.015625 \nL 19.390625 0 \nL 9.8125 0 \nz\n\" id=\"DejaVuSans-77\"/>\n      <path d=\"M 54.890625 33.015625 \nL 54.890625 0 \nL 45.90625 0 \nL 45.90625 32.71875 \nQ 45.90625 40.484375 42.875 44.328125 \nQ 39.84375 48.1875 33.796875 48.1875 \nQ 26.515625 48.1875 22.3125 43.546875 \nQ 18.109375 38.921875 18.109375 30.90625 \nL 18.109375 0 \nL 9.078125 0 \nL 9.078125 54.6875 \nL 18.109375 54.6875 \nL 18.109375 46.1875 \nQ 21.34375 51.125 25.703125 53.5625 \nQ 30.078125 56 35.796875 56 \nQ 45.21875 56 50.046875 50.171875 \nQ 54.890625 44.34375 54.890625 33.015625 \nz\n\" id=\"DejaVuSans-110\"/>\n      <path d=\"M 59.515625 10.40625 \nL 59.515625 29.984375 \nL 43.40625 29.984375 \nL 43.40625 38.09375 \nL 69.28125 38.09375 \nL 69.28125 6.78125 \nQ 63.578125 2.734375 56.6875 0.65625 \nQ 49.8125 -1.421875 42 -1.421875 \nQ 24.90625 -1.421875 15.25 8.5625 \nQ 5.609375 18.5625 5.609375 36.375 \nQ 5.609375 54.25 15.25 64.234375 \nQ 24.90625 74.21875 42 74.21875 \nQ 49.125 74.21875 55.546875 72.453125 \nQ 61.96875 70.703125 67.390625 67.28125 \nL 67.390625 56.78125 \nQ 61.921875 61.421875 55.765625 63.765625 \nQ 49.609375 66.109375 42.828125 66.109375 \nQ 29.4375 66.109375 22.71875 58.640625 \nQ 16.015625 51.171875 16.015625 36.375 \nQ 16.015625 21.625 22.71875 14.15625 \nQ 29.4375 6.6875 42.828125 6.6875 \nQ 48.046875 6.6875 52.140625 7.59375 \nQ 56.25 8.5 59.515625 10.40625 \nz\n\" id=\"DejaVuSans-71\"/>\n      <path d=\"M 19.671875 64.796875 \nL 19.671875 37.40625 \nL 32.078125 37.40625 \nQ 38.96875 37.40625 42.71875 40.96875 \nQ 46.484375 44.53125 46.484375 51.125 \nQ 46.484375 57.671875 42.71875 61.234375 \nQ 38.96875 64.796875 32.078125 64.796875 \nz\nM 9.8125 72.90625 \nL 32.078125 72.90625 \nQ 44.34375 72.90625 50.609375 67.359375 \nQ 56.890625 61.8125 56.890625 51.125 \nQ 56.890625 40.328125 50.609375 34.8125 \nQ 44.34375 29.296875 32.078125 29.296875 \nL 19.671875 29.296875 \nL 19.671875 0 \nL 9.8125 0 \nz\n\" id=\"DejaVuSans-80\"/>\n      <path d=\"M 8.6875 72.90625 \nL 18.609375 72.90625 \nL 18.609375 28.609375 \nQ 18.609375 16.890625 22.84375 11.734375 \nQ 27.09375 6.59375 36.625 6.59375 \nQ 46.09375 6.59375 50.34375 11.734375 \nQ 54.59375 16.890625 54.59375 28.609375 \nL 54.59375 72.90625 \nL 64.5 72.90625 \nL 64.5 27.390625 \nQ 64.5 13.140625 57.4375 5.859375 \nQ 50.390625 -1.421875 36.625 -1.421875 \nQ 22.796875 -1.421875 15.734375 5.859375 \nQ 8.6875 13.140625 8.6875 27.390625 \nz\n\" id=\"DejaVuSans-85\"/>\n      <path d=\"M 52 44.1875 \nQ 55.375 50.25 60.0625 53.125 \nQ 64.75 56 71.09375 56 \nQ 79.640625 56 84.28125 50.015625 \nQ 88.921875 44.046875 88.921875 33.015625 \nL 88.921875 0 \nL 79.890625 0 \nL 79.890625 32.71875 \nQ 79.890625 40.578125 77.09375 44.375 \nQ 74.3125 48.1875 68.609375 48.1875 \nQ 61.625 48.1875 57.5625 43.546875 \nQ 53.515625 38.921875 53.515625 30.90625 \nL 53.515625 0 \nL 44.484375 0 \nL 44.484375 32.71875 \nQ 44.484375 40.625 41.703125 44.40625 \nQ 38.921875 48.1875 33.109375 48.1875 \nQ 26.21875 48.1875 22.15625 43.53125 \nQ 18.109375 38.875 18.109375 30.90625 \nL 18.109375 0 \nL 9.078125 0 \nL 9.078125 54.6875 \nL 18.109375 54.6875 \nL 18.109375 46.1875 \nQ 21.1875 51.21875 25.484375 53.609375 \nQ 29.78125 56 35.6875 56 \nQ 41.65625 56 45.828125 52.96875 \nQ 50 49.953125 52 44.1875 \nz\n\" id=\"DejaVuSans-109\"/>\n      <path d=\"M 30.609375 48.390625 \nQ 23.390625 48.390625 19.1875 42.75 \nQ 14.984375 37.109375 14.984375 27.296875 \nQ 14.984375 17.484375 19.15625 11.84375 \nQ 23.34375 6.203125 30.609375 6.203125 \nQ 37.796875 6.203125 41.984375 11.859375 \nQ 46.1875 17.53125 46.1875 27.296875 \nQ 46.1875 37.015625 41.984375 42.703125 \nQ 37.796875 48.390625 30.609375 48.390625 \nz\nM 30.609375 56 \nQ 42.328125 56 49.015625 48.375 \nQ 55.71875 40.765625 55.71875 27.296875 \nQ 55.71875 13.875 49.015625 6.21875 \nQ 42.328125 -1.421875 30.609375 -1.421875 \nQ 18.84375 -1.421875 12.171875 6.21875 \nQ 5.515625 13.875 5.515625 27.296875 \nQ 5.515625 40.765625 12.171875 48.375 \nQ 18.84375 56 30.609375 56 \nz\n\" id=\"DejaVuSans-111\"/>\n      <path d=\"M 41.109375 46.296875 \nQ 39.59375 47.171875 37.8125 47.578125 \nQ 36.03125 48 33.890625 48 \nQ 26.265625 48 22.1875 43.046875 \nQ 18.109375 38.09375 18.109375 28.8125 \nL 18.109375 0 \nL 9.078125 0 \nL 9.078125 54.6875 \nL 18.109375 54.6875 \nL 18.109375 46.1875 \nQ 20.953125 51.171875 25.484375 53.578125 \nQ 30.03125 56 36.53125 56 \nQ 37.453125 56 38.578125 55.875 \nQ 39.703125 55.765625 41.0625 55.515625 \nz\n\" id=\"DejaVuSans-114\"/>\n      <path d=\"M 32.171875 -5.078125 \nQ 28.375 -14.84375 24.75 -17.8125 \nQ 21.140625 -20.796875 15.09375 -20.796875 \nL 7.90625 -20.796875 \nL 7.90625 -13.28125 \nL 13.1875 -13.28125 \nQ 16.890625 -13.28125 18.9375 -11.515625 \nQ 21 -9.765625 23.484375 -3.21875 \nL 25.09375 0.875 \nL 2.984375 54.6875 \nL 12.5 54.6875 \nL 29.59375 11.921875 \nL 46.6875 54.6875 \nL 56.203125 54.6875 \nz\n\" id=\"DejaVuSans-121\"/>\n      <path d=\"M 8.5 21.578125 \nL 8.5 54.6875 \nL 17.484375 54.6875 \nL 17.484375 21.921875 \nQ 17.484375 14.15625 20.5 10.265625 \nQ 23.53125 6.390625 29.59375 6.390625 \nQ 36.859375 6.390625 41.078125 11.03125 \nQ 45.3125 15.671875 45.3125 23.6875 \nL 45.3125 54.6875 \nL 54.296875 54.6875 \nL 54.296875 0 \nL 45.3125 0 \nL 45.3125 8.40625 \nQ 42.046875 3.421875 37.71875 1 \nQ 33.40625 -1.421875 27.6875 -1.421875 \nQ 18.265625 -1.421875 13.375 4.4375 \nQ 8.5 10.296875 8.5 21.578125 \nz\nM 31.109375 56 \nz\n\" id=\"DejaVuSans-117\"/>\n      <path d=\"M 45.40625 27.984375 \nQ 45.40625 37.75 41.375 43.109375 \nQ 37.359375 48.484375 30.078125 48.484375 \nQ 22.859375 48.484375 18.828125 43.109375 \nQ 14.796875 37.75 14.796875 27.984375 \nQ 14.796875 18.265625 18.828125 12.890625 \nQ 22.859375 7.515625 30.078125 7.515625 \nQ 37.359375 7.515625 41.375 12.890625 \nQ 45.40625 18.265625 45.40625 27.984375 \nz\nM 54.390625 6.78125 \nQ 54.390625 -7.171875 48.1875 -13.984375 \nQ 42 -20.796875 29.203125 -20.796875 \nQ 24.46875 -20.796875 20.265625 -20.09375 \nQ 16.0625 -19.390625 12.109375 -17.921875 \nL 12.109375 -9.1875 \nQ 16.0625 -11.328125 19.921875 -12.34375 \nQ 23.78125 -13.375 27.78125 -13.375 \nQ 36.625 -13.375 41.015625 -8.765625 \nQ 45.40625 -4.15625 45.40625 5.171875 \nL 45.40625 9.625 \nQ 42.625 4.78125 38.28125 2.390625 \nQ 33.9375 0 27.875 0 \nQ 17.828125 0 11.671875 7.65625 \nQ 5.515625 15.328125 5.515625 27.984375 \nQ 5.515625 40.671875 11.671875 48.328125 \nQ 17.828125 56 27.875 56 \nQ 33.9375 56 38.28125 53.609375 \nQ 42.625 51.21875 45.40625 46.390625 \nL 45.40625 54.6875 \nL 54.390625 54.6875 \nz\n\" id=\"DejaVuSans-103\"/>\n      <path d=\"M 18.109375 8.203125 \nL 18.109375 -20.796875 \nL 9.078125 -20.796875 \nL 9.078125 54.6875 \nL 18.109375 54.6875 \nL 18.109375 46.390625 \nQ 20.953125 51.265625 25.265625 53.625 \nQ 29.59375 56 35.59375 56 \nQ 45.5625 56 51.78125 48.09375 \nQ 58.015625 40.1875 58.015625 27.296875 \nQ 58.015625 14.40625 51.78125 6.484375 \nQ 45.5625 -1.421875 35.59375 -1.421875 \nQ 29.59375 -1.421875 25.265625 0.953125 \nQ 20.953125 3.328125 18.109375 8.203125 \nz\nM 48.6875 27.296875 \nQ 48.6875 37.203125 44.609375 42.84375 \nQ 40.53125 48.484375 33.40625 48.484375 \nQ 26.265625 48.484375 22.1875 42.84375 \nQ 18.109375 37.203125 18.109375 27.296875 \nQ 18.109375 17.390625 22.1875 11.75 \nQ 26.265625 6.109375 33.40625 6.109375 \nQ 40.53125 6.109375 44.609375 11.75 \nQ 48.6875 17.390625 48.6875 27.296875 \nz\n\" id=\"DejaVuSans-112\"/>\n      <path d=\"M 8.59375 75.984375 \nL 29.296875 75.984375 \nL 29.296875 69 \nL 17.578125 69 \nL 17.578125 -6.203125 \nL 29.296875 -6.203125 \nL 29.296875 -13.1875 \nL 8.59375 -13.1875 \nz\n\" id=\"DejaVuSans-91\"/>\n      <path d=\"M 30.421875 75.984375 \nL 30.421875 -13.1875 \nL 9.71875 -13.1875 \nL 9.71875 -6.203125 \nL 21.390625 -6.203125 \nL 21.390625 69 \nL 9.71875 69 \nL 9.71875 75.984375 \nz\n\" id=\"DejaVuSans-93\"/>\n     </defs>\n     <g transform=\"translate(14.798438 223.329375)rotate(-90)scale(0.1 -0.1)\">\n      <use xlink:href=\"#DejaVuSans-77\"/>\n      <use x=\"86.279297\" xlink:href=\"#DejaVuSans-101\"/>\n      <use x=\"147.802734\" xlink:href=\"#DejaVuSans-97\"/>\n      <use x=\"209.082031\" xlink:href=\"#DejaVuSans-110\"/>\n      <use x=\"272.460938\" xlink:href=\"#DejaVuSans-32\"/>\n      <use x=\"304.248047\" xlink:href=\"#DejaVuSans-71\"/>\n      <use x=\"381.738281\" xlink:href=\"#DejaVuSans-80\"/>\n      <use x=\"442.041016\" xlink:href=\"#DejaVuSans-85\"/>\n      <use x=\"515.234375\" xlink:href=\"#DejaVuSans-32\"/>\n      <use x=\"547.021484\" xlink:href=\"#DejaVuSans-109\"/>\n      <use x=\"644.433594\" xlink:href=\"#DejaVuSans-101\"/>\n      <use x=\"705.957031\" xlink:href=\"#DejaVuSans-109\"/>\n      <use x=\"803.369141\" xlink:href=\"#DejaVuSans-111\"/>\n      <use x=\"864.550781\" xlink:href=\"#DejaVuSans-114\"/>\n      <use x=\"905.664062\" xlink:href=\"#DejaVuSans-121\"/>\n      <use x=\"964.84375\" xlink:href=\"#DejaVuSans-32\"/>\n      <use x=\"996.630859\" xlink:href=\"#DejaVuSans-117\"/>\n      <use x=\"1060.009766\" xlink:href=\"#DejaVuSans-115\"/>\n      <use x=\"1112.109375\" xlink:href=\"#DejaVuSans-97\"/>\n      <use x=\"1173.388672\" xlink:href=\"#DejaVuSans-103\"/>\n      <use x=\"1236.865234\" xlink:href=\"#DejaVuSans-101\"/>\n      <use x=\"1298.388672\" xlink:href=\"#DejaVuSans-32\"/>\n      <use x=\"1330.175781\" xlink:href=\"#DejaVuSans-112\"/>\n      <use x=\"1393.652344\" xlink:href=\"#DejaVuSans-101\"/>\n      <use x=\"1455.175781\" xlink:href=\"#DejaVuSans-114\"/>\n      <use x=\"1496.289062\" xlink:href=\"#DejaVuSans-32\"/>\n      <use x=\"1528.076172\" xlink:href=\"#DejaVuSans-112\"/>\n      <use x=\"1591.552734\" xlink:href=\"#DejaVuSans-114\"/>\n      <use x=\"1630.416016\" xlink:href=\"#DejaVuSans-111\"/>\n      <use x=\"1691.597656\" xlink:href=\"#DejaVuSans-116\"/>\n      <use x=\"1730.806641\" xlink:href=\"#DejaVuSans-101\"/>\n      <use x=\"1792.330078\" xlink:href=\"#DejaVuSans-105\"/>\n      <use x=\"1820.113281\" xlink:href=\"#DejaVuSans-110\"/>\n      <use x=\"1883.492188\" xlink:href=\"#DejaVuSans-32\"/>\n      <use x=\"1915.279297\" xlink:href=\"#DejaVuSans-91\"/>\n      <use x=\"1954.292969\" xlink:href=\"#DejaVuSans-77\"/>\n      <use x=\"2040.572266\" xlink:href=\"#DejaVuSans-66\"/>\n      <use x=\"2109.175781\" xlink:href=\"#DejaVuSans-93\"/>\n     </g>\n    </g>\n   </g>\n   <g id=\"line2d_32\">\n    <path clip-path=\"url(#p987c25f011)\" d=\"M 60.696307 17.083636 \nL 104.176826 53.541368 \nL 147.657346 89.883322 \nL 191.137865 124.506244 \nL 234.618385 155.078439 \nL 278.098904 183.317456 \nL 321.579424 202.875186 \nL 365.059943 214.756364 \n\" style=\"fill:none;stroke:#1f77b4;stroke-linecap:square;stroke-width:1.5;\"/>\n   </g>\n   <g id=\"patch_3\">\n    <path d=\"M 45.478125 224.64 \nL 45.478125 7.2 \n\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;stroke-width:0.8;\"/>\n   </g>\n   <g id=\"patch_4\">\n    <path d=\"M 380.278125 224.64 \nL 380.278125 7.2 \n\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;stroke-width:0.8;\"/>\n   </g>\n   <g id=\"patch_5\">\n    <path d=\"M 45.478125 224.64 \nL 380.278125 224.64 \n\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;stroke-width:0.8;\"/>\n   </g>\n   <g id=\"patch_6\">\n    <path d=\"M 45.478125 7.2 \nL 380.278125 7.2 \n\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;stroke-width:0.8;\"/>\n   </g>\n  </g>\n </g>\n <defs>\n  <clipPath id=\"p987c25f011\">\n   <rect height=\"217.44\" width=\"334.8\" x=\"45.478125\" y=\"7.2\"/>\n  </clipPath>\n </defs>\n</svg>\n", "image/png": "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\n"}, "metadata": {"needs_background": "light"}}], "source": ["plt.plot(batch_sizes,memory_means)\n", "plt.xlabel('Batch size')\n", "plt.ylabel('Mean GPU memory usage per protein [MB]')\n", "plt.xscale('log')\n", "plt.yscale('log')"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [{"output_type": "display_data", "data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"error_y": {"array": [0.09661842517474258, 0.06524675196323244, 0.050105717083475367, 0.041534353552926, 0.03441623031242543, 0.024559428491832888, 0.020793476199996448, 0.0187925904966869], "type": "data", "visible": true}, "marker": {"size": 12, "symbol": "star"}, "name": "Time", "type": "scatter", "x": [1, 2, 4, 8, 16, 32, 64, 128], "xaxis": "x", "y": [0.45163814779518985, 0.24425267262211378, 0.14348937780316984, 0.09967912195515734, 0.08389908946316008, 0.07489164349649494, 0.07219395963414238, 0.07024341275858338], "yaxis": "y"}, {"error_y": {"array": [74.97409732852165, 38.10489460753547, 21.12448649347617, 12.978408364333657, 14.526095781510266, 3.534258518508427, 3.1682868928673944, 4.103064352455909], "type": "data", "visible": true}, "marker": {"size": 10, "symbol": "square"}, "name": "Memory", "type": "scatter", "x": [1, 2, 4, 8, 16, 32, 64, 128], "xaxis": "x", "y": [235.51553588420123, 141.17300474126367, 84.75973699929428, 52.13240810155147, 33.94076232112676, 22.832835124917384, 17.35099339607843, 14.68553105370844], "yaxis": "y2"}], "layout": {"autosize": false, "font": {"size": 18}, "height": 800, "shapes": [{"line": {"color": "Blue", "dash": "dot"}, "type": "line", "x0": 1, "x1": 128, "y0": 6.11, "y1": 6.11}], "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "width": 1000, "xaxis": {"anchor": "y", "domain": [0, 0.94], "tickvals": [1, 2, 4, 6, 8, 10, 20, 40, 60, 80, 100, 200, 400], "title": {"text": "Batch size"}, "type": "log"}, "yaxis": {"anchor": "x", "color": "blue", "domain": [0, 1], "range": [-1.5, 1], "tickvals": [0.04, 0.06, 0.08, 0.1, 0.2, 0.4, 0.6, 0.8, 1, 2, 4, 6, 8, 10], "title": {"text": "Mean surface generation time per protein [s]"}, "type": "log"}, "yaxis2": {"anchor": "x", "color": "red", "overlaying": "y", "side": "right", "tickvals": [10, 20, 40, 60, 80, 100, 200, 400], "title": {"text": "Mean GPU memory usage per protein [MB]"}, "type": "log"}}}, "text/html": "<div>                            <div id=\"96bbbcbf-1dfb-4818-b53a-9e6650b75feb\" class=\"plotly-graph-div\" style=\"height:800px; width:1000px;\"></div>            <script type=\"text/javascript\">                require([\"plotly\"], function(Plotly) {                    window.PLOTLYENV=window.PLOTLYENV || {};                                    if (document.getElementById(\"96bbbcbf-1dfb-4818-b53a-9e6650b75feb\")) {                    Plotly.newPlot(                        \"96bbbcbf-1dfb-4818-b53a-9e6650b75feb\",                        [{\"error_y\": {\"array\": [0.09661842517474258, 0.06524675196323244, 0.050105717083475367, 0.041534353552926, 0.03441623031242543, 0.024559428491832888, 0.020793476199996448, 0.0187925904966869], \"type\": \"data\", \"visible\": true}, \"marker\": {\"size\": 12, \"symbol\": \"star\"}, \"name\": \"Time\", \"type\": \"scatter\", \"x\": [1, 2, 4, 8, 16, 32, 64, 128], \"xaxis\": \"x\", \"y\": [0.45163814779518985, 0.24425267262211378, 0.14348937780316984, 0.09967912195515734, 0.08389908946316008, 0.07489164349649494, 0.07219395963414238, 0.07024341275858338], \"yaxis\": \"y\"}, {\"error_y\": {\"array\": [74.97409732852165, 38.10489460753547, 21.12448649347617, 12.978408364333657, 14.526095781510266, 3.534258518508427, 3.1682868928673944, 4.103064352455909], \"type\": \"data\", \"visible\": true}, \"marker\": {\"size\": 10, \"symbol\": \"square\"}, \"name\": \"Memory\", \"type\": \"scatter\", \"x\": [1, 2, 4, 8, 16, 32, 64, 128], \"xaxis\": \"x\", \"y\": [235.51553588420123, 141.17300474126367, 84.75973699929428, 52.13240810155147, 33.94076232112676, 22.832835124917384, 17.35099339607843, 14.68553105370844], \"yaxis\": \"y2\"}],                        {\"autosize\": false, \"font\": {\"size\": 18}, \"height\": 800, \"shapes\": [{\"line\": {\"color\": \"Blue\", \"dash\": \"dot\"}, \"type\": \"line\", \"x0\": 1, \"x1\": 128, \"y0\": 6.11, \"y1\": 6.11}], \"template\": {\"data\": {\"bar\": [{\"error_x\": {\"color\": \"#2a3f5f\"}, \"error_y\": {\"color\": \"#2a3f5f\"}, \"marker\": {\"line\": {\"color\": \"#E5ECF6\", \"width\": 0.5}}, \"type\": \"bar\"}], \"barpolar\": [{\"marker\": {\"line\": {\"color\": \"#E5ECF6\", \"width\": 0.5}}, \"type\": \"barpolar\"}], \"carpet\": [{\"aaxis\": {\"endlinecolor\": \"#2a3f5f\", \"gridcolor\": \"white\", \"linecolor\": \"white\", \"minorgridcolor\": \"white\", \"startlinecolor\": \"#2a3f5f\"}, \"baxis\": {\"endlinecolor\": \"#2a3f5f\", \"gridcolor\": \"white\", \"linecolor\": \"white\", \"minorgridcolor\": \"white\", \"startlinecolor\": \"#2a3f5f\"}, \"type\": \"carpet\"}], \"choropleth\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"type\": \"choropleth\"}], \"contour\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"colorscale\": [[0.0, \"#0d0887\"], [0.1111111111111111, \"#46039f\"], [0.2222222222222222, \"#7201a8\"], [0.3333333333333333, \"#9c179e\"], [0.4444444444444444, \"#bd3786\"], [0.5555555555555556, \"#d8576b\"], [0.6666666666666666, \"#ed7953\"], [0.7777777777777778, \"#fb9f3a\"], [0.8888888888888888, \"#fdca26\"], [1.0, \"#f0f921\"]], \"type\": \"contour\"}], \"contourcarpet\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"type\": \"contourcarpet\"}], \"heatmap\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"colorscale\": [[0.0, \"#0d0887\"], [0.1111111111111111, \"#46039f\"], [0.2222222222222222, \"#7201a8\"], [0.3333333333333333, \"#9c179e\"], [0.4444444444444444, \"#bd3786\"], [0.5555555555555556, \"#d8576b\"], [0.6666666666666666, \"#ed7953\"], [0.7777777777777778, \"#fb9f3a\"], [0.8888888888888888, \"#fdca26\"], [1.0, \"#f0f921\"]], \"type\": \"heatmap\"}], \"heatmapgl\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"colorscale\": [[0.0, \"#0d0887\"], [0.1111111111111111, \"#46039f\"], [0.2222222222222222, \"#7201a8\"], [0.3333333333333333, \"#9c179e\"], [0.4444444444444444, \"#bd3786\"], [0.5555555555555556, \"#d8576b\"], [0.6666666666666666, \"#ed7953\"], [0.7777777777777778, \"#fb9f3a\"], [0.8888888888888888, \"#fdca26\"], [1.0, \"#f0f921\"]], \"type\": \"heatmapgl\"}], \"histogram\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"histogram\"}], \"histogram2d\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"colorscale\": [[0.0, \"#0d0887\"], [0.1111111111111111, \"#46039f\"], [0.2222222222222222, \"#7201a8\"], [0.3333333333333333, \"#9c179e\"], [0.4444444444444444, \"#bd3786\"], [0.5555555555555556, \"#d8576b\"], [0.6666666666666666, \"#ed7953\"], [0.7777777777777778, \"#fb9f3a\"], [0.8888888888888888, \"#fdca26\"], [1.0, \"#f0f921\"]], \"type\": \"histogram2d\"}], \"histogram2dcontour\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"colorscale\": [[0.0, \"#0d0887\"], [0.1111111111111111, \"#46039f\"], [0.2222222222222222, \"#7201a8\"], [0.3333333333333333, \"#9c179e\"], [0.4444444444444444, \"#bd3786\"], [0.5555555555555556, \"#d8576b\"], [0.6666666666666666, \"#ed7953\"], [0.7777777777777778, \"#fb9f3a\"], [0.8888888888888888, \"#fdca26\"], [1.0, \"#f0f921\"]], \"type\": \"histogram2dcontour\"}], \"mesh3d\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"type\": \"mesh3d\"}], \"parcoords\": [{\"line\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"parcoords\"}], \"pie\": [{\"automargin\": true, \"type\": \"pie\"}], \"scatter\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scatter\"}], \"scatter3d\": [{\"line\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scatter3d\"}], \"scattercarpet\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scattercarpet\"}], \"scattergeo\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scattergeo\"}], \"scattergl\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scattergl\"}], \"scattermapbox\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scattermapbox\"}], \"scatterpolar\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scatterpolar\"}], \"scatterpolargl\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scatterpolargl\"}], \"scatterternary\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scatterternary\"}], \"surface\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"colorscale\": [[0.0, \"#0d0887\"], [0.1111111111111111, \"#46039f\"], [0.2222222222222222, \"#7201a8\"], [0.3333333333333333, \"#9c179e\"], [0.4444444444444444, \"#bd3786\"], [0.5555555555555556, \"#d8576b\"], [0.6666666666666666, \"#ed7953\"], [0.7777777777777778, \"#fb9f3a\"], [0.8888888888888888, \"#fdca26\"], [1.0, \"#f0f921\"]], \"type\": \"surface\"}], \"table\": [{\"cells\": {\"fill\": {\"color\": \"#EBF0F8\"}, \"line\": {\"color\": \"white\"}}, \"header\": {\"fill\": {\"color\": \"#C8D4E3\"}, \"line\": {\"color\": \"white\"}}, \"type\": \"table\"}]}, \"layout\": {\"annotationdefaults\": {\"arrowcolor\": \"#2a3f5f\", \"arrowhead\": 0, \"arrowwidth\": 1}, \"coloraxis\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"colorscale\": {\"diverging\": [[0, \"#8e0152\"], [0.1, \"#c51b7d\"], [0.2, \"#de77ae\"], [0.3, \"#f1b6da\"], [0.4, \"#fde0ef\"], [0.5, \"#f7f7f7\"], [0.6, \"#e6f5d0\"], [0.7, \"#b8e186\"], [0.8, \"#7fbc41\"], [0.9, \"#4d9221\"], [1, \"#276419\"]], \"sequential\": [[0.0, \"#0d0887\"], [0.1111111111111111, \"#46039f\"], [0.2222222222222222, \"#7201a8\"], [0.3333333333333333, \"#9c179e\"], [0.4444444444444444, \"#bd3786\"], [0.5555555555555556, \"#d8576b\"], [0.6666666666666666, \"#ed7953\"], [0.7777777777777778, \"#fb9f3a\"], [0.8888888888888888, \"#fdca26\"], [1.0, \"#f0f921\"]], \"sequentialminus\": [[0.0, \"#0d0887\"], [0.1111111111111111, \"#46039f\"], [0.2222222222222222, \"#7201a8\"], [0.3333333333333333, \"#9c179e\"], [0.4444444444444444, \"#bd3786\"], [0.5555555555555556, \"#d8576b\"], [0.6666666666666666, \"#ed7953\"], [0.7777777777777778, \"#fb9f3a\"], [0.8888888888888888, \"#fdca26\"], [1.0, \"#f0f921\"]]}, \"colorway\": [\"#636efa\", \"#EF553B\", \"#00cc96\", \"#ab63fa\", \"#FFA15A\", \"#19d3f3\", \"#FF6692\", \"#B6E880\", \"#FF97FF\", \"#FECB52\"], \"font\": {\"color\": \"#2a3f5f\"}, \"geo\": {\"bgcolor\": \"white\", \"lakecolor\": \"white\", \"landcolor\": \"#E5ECF6\", \"showlakes\": true, \"showland\": true, \"subunitcolor\": \"white\"}, \"hoverlabel\": {\"align\": \"left\"}, \"hovermode\": \"closest\", \"mapbox\": {\"style\": \"light\"}, \"paper_bgcolor\": \"white\", \"plot_bgcolor\": \"#E5ECF6\", \"polar\": {\"angularaxis\": {\"gridcolor\": \"white\", \"linecolor\": \"white\", \"ticks\": \"\"}, \"bgcolor\": \"#E5ECF6\", \"radialaxis\": {\"gridcolor\": \"white\", \"linecolor\": \"white\", \"ticks\": \"\"}}, \"scene\": {\"xaxis\": {\"backgroundcolor\": \"#E5ECF6\", \"gridcolor\": \"white\", \"gridwidth\": 2, \"linecolor\": \"white\", \"showbackground\": true, \"ticks\": \"\", \"zerolinecolor\": \"white\"}, \"yaxis\": {\"backgroundcolor\": \"#E5ECF6\", \"gridcolor\": \"white\", \"gridwidth\": 2, \"linecolor\": \"white\", \"showbackground\": true, \"ticks\": \"\", \"zerolinecolor\": \"white\"}, \"zaxis\": {\"backgroundcolor\": \"#E5ECF6\", \"gridcolor\": \"white\", \"gridwidth\": 2, \"linecolor\": \"white\", \"showbackground\": true, \"ticks\": \"\", \"zerolinecolor\": \"white\"}}, \"shapedefaults\": {\"line\": {\"color\": \"#2a3f5f\"}}, \"ternary\": {\"aaxis\": {\"gridcolor\": \"white\", \"linecolor\": \"white\", \"ticks\": \"\"}, \"baxis\": {\"gridcolor\": \"white\", \"linecolor\": \"white\", \"ticks\": \"\"}, \"bgcolor\": \"#E5ECF6\", \"caxis\": {\"gridcolor\": \"white\", \"linecolor\": \"white\", \"ticks\": \"\"}}, \"title\": {\"x\": 0.05}, \"xaxis\": {\"automargin\": true, \"gridcolor\": \"white\", \"linecolor\": \"white\", \"ticks\": \"\", \"title\": {\"standoff\": 15}, \"zerolinecolor\": \"white\", \"zerolinewidth\": 2}, \"yaxis\": {\"automargin\": true, \"gridcolor\": \"white\", \"linecolor\": \"white\", \"ticks\": \"\", \"title\": {\"standoff\": 15}, \"zerolinecolor\": \"white\", \"zerolinewidth\": 2}}}, \"width\": 1000, \"xaxis\": {\"anchor\": \"y\", \"domain\": [0.0, 0.94], \"tickvals\": [1, 2, 4, 6, 8, 10, 20, 40, 60, 80, 100, 200, 400], \"title\": {\"text\": \"Batch size\"}, \"type\": \"log\"}, \"yaxis\": {\"anchor\": \"x\", \"color\": \"blue\", \"domain\": [0.0, 1.0], \"range\": [-1.5, 1], \"tickvals\": [0.04, 0.06, 0.08, 0.1, 0.2, 0.4, 0.6, 0.8, 1, 2, 4, 6, 8, 10], \"title\": {\"text\": \"Mean surface generation time per protein [s]\"}, \"type\": \"log\"}, \"yaxis2\": {\"anchor\": \"x\", \"color\": \"red\", \"overlaying\": \"y\", \"side\": \"right\", \"tickvals\": [10, 20, 40, 60, 80, 100, 200, 400], \"title\": {\"text\": \"Mean GPU memory usage per protein [MB]\"}, \"type\": \"log\"}},                        {\"responsive\": true}                    ).then(function(){\n                            \nvar gd = document.getElementById('96bbbcbf-1dfb-4818-b53a-9e6650b75feb');\nvar x = new MutationObserver(function (mutations, observer) {{\n        var display = window.getComputedStyle(gd).display;\n        if (!display || display === 'none') {{\n            console.log([gd, 'removed!']);\n            Plotly.purge(gd);\n            observer.disconnect();\n        }}\n}});\n\n// Listen for the removal of the full notebook cells\nvar notebookContainer = gd.closest('#notebook-container');\nif (notebookContainer) {{\n    x.observe(notebookContainer, {childList: true});\n}}\n\n// Listen for the clearing of the current output cell\nvar outputEl = gd.closest('.output');\nif (outputEl) {{\n    x.observe(outputEl, {childList: true});\n}}\n\n                        })                };                });            </script>        </div>"}, "metadata": {}}], "source": ["fig = make_subplots(specs=[[{\"secondary_y\": True}]])\n", "fig.add_trace(\n", "    go.Scatter(x=batch_sizes, y=time_means, name=\"Time\", marker_size=12, marker_symbol='star', error_y=dict(type='data',array=time_std,visible=True)),\n", "    secondary_y=False,\n", ")\n", "\n", "fig.add_trace(\n", "    go.Scatter(x=batch_sizes, y=memory_means, name=\"Memory\", marker_symbol='square', marker_size=10,error_y=dict(type='data',array=memory_std,visible=True)),\n", "    secondary_y=True,\n", ")\n", "\n", "fig.add_shape(type='line',\n", "              x0=1,\n", "              y0=6.11,\n", "              x1=128,\n", "              y1=6.11,\n", "              line=dict(color='Blue',dash = 'dot'))\n", "\n", "fig.update_xaxes(title_text=\"Batch size\",tickvals = [1,2,4,6,8,10,20,40,60,80,100,200,400])\n", "fig.update_yaxes(title_text=\"Mean surface generation time per protein [s]\", secondary_y=False,color='blue')\n", "fig.update_yaxes(title_text=\"Mean GPU memory usage per protein [MB]\", secondary_y=True,color='red',tickvals = [10,20,40,60,80,100,200,400])\n", "\n", "\n", "fig.update_xaxes(type=\"log\")\n", "fig.update_yaxes(type=\"log\")\n", "\n", "fig.update_yaxes(secondary_y=False,tickvals = [0.04,0.06,0.08,0.1,0.2,0.4,0.6,0.8,1,2,4,6,8,10])\n", "fig.update_yaxes(secondary_y=False,range=[-1.5,1])\n", "\n", "\n", "\n", "fig.update_layout(\n", "    autosize=False,\n", "    width=1000,\n", "    height=800,\n", "    font=dict(\n", "        size=18,\n", "    ))\n", "\n", "\n", "\n", "fig.show()\n", "fig.write_image(\"figures/surfgen_profile.pdf\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["keys = ['surface', 'features', 'coordinates']\n", "batch_size = 1\n", "resolutions = [0.5,1.0,2.0,4.0]\n", "sup_samplings = [40,20,10,5]\n", "N = len(resolutions)\n", "\n", "time_means = []\n", "time_std = []\n", "memory_means = []\n", "memory_std = []\n", "for i in range(N):\n", "    times = np.load(f'../timings/step_profiling_{keys[0]}_times_batch_{batch_size}_res_{resolutions[i]}_B_{sup_samplings[i]}.npy')\n", "    memory = np.load(f'../timings/step_profiling_{keys[0]}_memory_batch_{batch_size}_res_{resolutions[i]}_B_{sup_samplings[i]}.npy')\n", "    memory = memory*1e-6\n", "    time_means.append(times.mean())\n", "    memory_means.append(memory.mean())\n", "    time_std.append(times.std())\n", "    memory_std.append(memory.std())"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"output_type": "display_data", "data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"error_y": {"array": [0.18533073533808336, 0.09680264243368539, 0.04991310328653822, 0.028676412920893138], "type": "data", "visible": true}, "marker": {"size": 12, "symbol": "star"}, "name": "Time", "type": "scatter", "x": [0.5, 1, 2, 4], "xaxis": "x", "y": [0.1790069231814446, 0.12131211667174485, 0.09353480621028136, 0.08197236860734851], "yaxis": "y"}, {"error_y": {"array": [150.13888736862992, 74.29559611714842, 37.561338753611665, 19.194383313465114], "type": "data", "visible": true}, "marker": {"size": 10, "symbol": "square"}, "name": "Memory", "type": "scatter", "x": [0.5, 1, 2, 4], "xaxis": "x", "y": [499.09659836893195, 249.11686125472195, 125.12769170414828, 63.120192169461596], "yaxis": "y2"}], "layout": {"autosize": false, "font": {"size": 18}, "height": 800, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "width": 1000, "xaxis": {"anchor": "y", "domain": [0, 0.94], "title": {"text": "Resolution"}}, "yaxis": {"anchor": "x", "color": "blue", "domain": [0, 1], "title": {"text": "Mean time per protein [s]"}}, "yaxis2": {"anchor": "x", "color": "red", "overlaying": "y", "side": "right", "title": {"text": "Mean GPU memory usage per protein [MB]"}}}}, "text/html": "<div>                            <div id=\"c1525a75-7b16-4585-8d64-a25ca762f863\" class=\"plotly-graph-div\" style=\"height:800px; width:1000px;\"></div>            <script type=\"text/javascript\">                require([\"plotly\"], function(Plotly) {                    window.PLOTLYENV=window.PLOTLYENV || {};                                    if (document.getElementById(\"c1525a75-7b16-4585-8d64-a25ca762f863\")) {                    Plotly.newPlot(                        \"c1525a75-7b16-4585-8d64-a25ca762f863\",                        [{\"error_y\": {\"array\": [0.18533073533808336, 0.09680264243368539, 0.04991310328653822, 0.028676412920893138], \"type\": \"data\", \"visible\": true}, \"marker\": {\"size\": 12, \"symbol\": \"star\"}, \"name\": \"Time\", \"type\": \"scatter\", \"x\": [0.5, 1.0, 2.0, 4.0], \"xaxis\": \"x\", \"y\": [0.1790069231814446, 0.12131211667174485, 0.09353480621028136, 0.08197236860734851], \"yaxis\": \"y\"}, {\"error_y\": {\"array\": [150.13888736862992, 74.29559611714842, 37.561338753611665, 19.194383313465114], \"type\": \"data\", \"visible\": true}, \"marker\": {\"size\": 10, \"symbol\": \"square\"}, \"name\": \"Memory\", \"type\": \"scatter\", \"x\": [0.5, 1.0, 2.0, 4.0], \"xaxis\": \"x\", \"y\": [499.09659836893195, 249.11686125472195, 125.12769170414828, 63.120192169461596], \"yaxis\": \"y2\"}],                        {\"autosize\": false, \"font\": {\"size\": 18}, \"height\": 800, \"template\": {\"data\": {\"bar\": [{\"error_x\": {\"color\": \"#2a3f5f\"}, \"error_y\": {\"color\": \"#2a3f5f\"}, \"marker\": {\"line\": {\"color\": \"#E5ECF6\", \"width\": 0.5}}, \"type\": \"bar\"}], \"barpolar\": [{\"marker\": {\"line\": {\"color\": \"#E5ECF6\", \"width\": 0.5}}, \"type\": \"barpolar\"}], \"carpet\": [{\"aaxis\": {\"endlinecolor\": \"#2a3f5f\", \"gridcolor\": \"white\", \"linecolor\": \"white\", \"minorgridcolor\": \"white\", \"startlinecolor\": \"#2a3f5f\"}, \"baxis\": {\"endlinecolor\": \"#2a3f5f\", \"gridcolor\": \"white\", \"linecolor\": \"white\", \"minorgridcolor\": \"white\", \"startlinecolor\": \"#2a3f5f\"}, \"type\": \"carpet\"}], \"choropleth\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"type\": \"choropleth\"}], \"contour\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"colorscale\": [[0.0, \"#0d0887\"], [0.1111111111111111, \"#46039f\"], [0.2222222222222222, \"#7201a8\"], [0.3333333333333333, \"#9c179e\"], [0.4444444444444444, \"#bd3786\"], [0.5555555555555556, \"#d8576b\"], [0.6666666666666666, \"#ed7953\"], [0.7777777777777778, \"#fb9f3a\"], [0.8888888888888888, \"#fdca26\"], [1.0, \"#f0f921\"]], \"type\": \"contour\"}], \"contourcarpet\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"type\": \"contourcarpet\"}], \"heatmap\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"colorscale\": [[0.0, \"#0d0887\"], [0.1111111111111111, \"#46039f\"], [0.2222222222222222, \"#7201a8\"], [0.3333333333333333, \"#9c179e\"], [0.4444444444444444, \"#bd3786\"], [0.5555555555555556, \"#d8576b\"], [0.6666666666666666, \"#ed7953\"], [0.7777777777777778, \"#fb9f3a\"], [0.8888888888888888, \"#fdca26\"], [1.0, \"#f0f921\"]], \"type\": \"heatmap\"}], \"heatmapgl\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"colorscale\": [[0.0, \"#0d0887\"], [0.1111111111111111, \"#46039f\"], [0.2222222222222222, \"#7201a8\"], [0.3333333333333333, \"#9c179e\"], [0.4444444444444444, \"#bd3786\"], [0.5555555555555556, \"#d8576b\"], [0.6666666666666666, \"#ed7953\"], [0.7777777777777778, \"#fb9f3a\"], [0.8888888888888888, \"#fdca26\"], [1.0, \"#f0f921\"]], \"type\": \"heatmapgl\"}], \"histogram\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"histogram\"}], \"histogram2d\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"colorscale\": [[0.0, \"#0d0887\"], [0.1111111111111111, \"#46039f\"], [0.2222222222222222, \"#7201a8\"], [0.3333333333333333, \"#9c179e\"], [0.4444444444444444, \"#bd3786\"], [0.5555555555555556, \"#d8576b\"], [0.6666666666666666, \"#ed7953\"], [0.7777777777777778, \"#fb9f3a\"], [0.8888888888888888, \"#fdca26\"], [1.0, \"#f0f921\"]], \"type\": \"histogram2d\"}], \"histogram2dcontour\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"colorscale\": [[0.0, \"#0d0887\"], [0.1111111111111111, \"#46039f\"], [0.2222222222222222, \"#7201a8\"], [0.3333333333333333, \"#9c179e\"], [0.4444444444444444, \"#bd3786\"], [0.5555555555555556, \"#d8576b\"], [0.6666666666666666, \"#ed7953\"], [0.7777777777777778, \"#fb9f3a\"], [0.8888888888888888, \"#fdca26\"], [1.0, \"#f0f921\"]], \"type\": \"histogram2dcontour\"}], \"mesh3d\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"type\": \"mesh3d\"}], \"parcoords\": [{\"line\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"parcoords\"}], \"pie\": [{\"automargin\": true, \"type\": \"pie\"}], \"scatter\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scatter\"}], \"scatter3d\": [{\"line\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scatter3d\"}], \"scattercarpet\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scattercarpet\"}], \"scattergeo\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scattergeo\"}], \"scattergl\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scattergl\"}], \"scattermapbox\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scattermapbox\"}], \"scatterpolar\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scatterpolar\"}], \"scatterpolargl\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scatterpolargl\"}], \"scatterternary\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scatterternary\"}], \"surface\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"colorscale\": [[0.0, \"#0d0887\"], [0.1111111111111111, \"#46039f\"], [0.2222222222222222, \"#7201a8\"], [0.3333333333333333, \"#9c179e\"], [0.4444444444444444, \"#bd3786\"], [0.5555555555555556, \"#d8576b\"], [0.6666666666666666, \"#ed7953\"], [0.7777777777777778, \"#fb9f3a\"], [0.8888888888888888, \"#fdca26\"], [1.0, \"#f0f921\"]], \"type\": \"surface\"}], \"table\": [{\"cells\": {\"fill\": {\"color\": \"#EBF0F8\"}, \"line\": {\"color\": \"white\"}}, \"header\": {\"fill\": {\"color\": \"#C8D4E3\"}, \"line\": {\"color\": \"white\"}}, \"type\": \"table\"}]}, \"layout\": {\"annotationdefaults\": {\"arrowcolor\": \"#2a3f5f\", \"arrowhead\": 0, \"arrowwidth\": 1}, \"coloraxis\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"colorscale\": {\"diverging\": [[0, \"#8e0152\"], [0.1, \"#c51b7d\"], [0.2, \"#de77ae\"], [0.3, \"#f1b6da\"], [0.4, \"#fde0ef\"], [0.5, \"#f7f7f7\"], [0.6, \"#e6f5d0\"], [0.7, \"#b8e186\"], [0.8, \"#7fbc41\"], [0.9, \"#4d9221\"], [1, \"#276419\"]], \"sequential\": [[0.0, \"#0d0887\"], [0.1111111111111111, \"#46039f\"], [0.2222222222222222, \"#7201a8\"], [0.3333333333333333, \"#9c179e\"], [0.4444444444444444, \"#bd3786\"], [0.5555555555555556, \"#d8576b\"], [0.6666666666666666, \"#ed7953\"], [0.7777777777777778, \"#fb9f3a\"], [0.8888888888888888, \"#fdca26\"], [1.0, \"#f0f921\"]], \"sequentialminus\": [[0.0, \"#0d0887\"], [0.1111111111111111, \"#46039f\"], [0.2222222222222222, \"#7201a8\"], [0.3333333333333333, \"#9c179e\"], [0.4444444444444444, \"#bd3786\"], [0.5555555555555556, \"#d8576b\"], [0.6666666666666666, \"#ed7953\"], [0.7777777777777778, \"#fb9f3a\"], [0.8888888888888888, \"#fdca26\"], [1.0, \"#f0f921\"]]}, \"colorway\": [\"#636efa\", \"#EF553B\", \"#00cc96\", \"#ab63fa\", \"#FFA15A\", \"#19d3f3\", \"#FF6692\", \"#B6E880\", \"#FF97FF\", \"#FECB52\"], \"font\": {\"color\": \"#2a3f5f\"}, \"geo\": {\"bgcolor\": \"white\", \"lakecolor\": \"white\", \"landcolor\": \"#E5ECF6\", \"showlakes\": true, \"showland\": true, \"subunitcolor\": \"white\"}, \"hoverlabel\": {\"align\": \"left\"}, \"hovermode\": \"closest\", \"mapbox\": {\"style\": \"light\"}, \"paper_bgcolor\": \"white\", \"plot_bgcolor\": \"#E5ECF6\", \"polar\": {\"angularaxis\": {\"gridcolor\": \"white\", \"linecolor\": \"white\", \"ticks\": \"\"}, \"bgcolor\": \"#E5ECF6\", \"radialaxis\": {\"gridcolor\": \"white\", \"linecolor\": \"white\", \"ticks\": \"\"}}, \"scene\": {\"xaxis\": {\"backgroundcolor\": \"#E5ECF6\", \"gridcolor\": \"white\", \"gridwidth\": 2, \"linecolor\": \"white\", \"showbackground\": true, \"ticks\": \"\", \"zerolinecolor\": \"white\"}, \"yaxis\": {\"backgroundcolor\": \"#E5ECF6\", \"gridcolor\": \"white\", \"gridwidth\": 2, \"linecolor\": \"white\", \"showbackground\": true, \"ticks\": \"\", \"zerolinecolor\": \"white\"}, \"zaxis\": {\"backgroundcolor\": \"#E5ECF6\", \"gridcolor\": \"white\", \"gridwidth\": 2, \"linecolor\": \"white\", \"showbackground\": true, \"ticks\": \"\", \"zerolinecolor\": \"white\"}}, \"shapedefaults\": {\"line\": {\"color\": \"#2a3f5f\"}}, \"ternary\": {\"aaxis\": {\"gridcolor\": \"white\", \"linecolor\": \"white\", \"ticks\": \"\"}, \"baxis\": {\"gridcolor\": \"white\", \"linecolor\": \"white\", \"ticks\": \"\"}, \"bgcolor\": \"#E5ECF6\", \"caxis\": {\"gridcolor\": \"white\", \"linecolor\": \"white\", \"ticks\": \"\"}}, \"title\": {\"x\": 0.05}, \"xaxis\": {\"automargin\": true, \"gridcolor\": \"white\", \"linecolor\": \"white\", \"ticks\": \"\", \"title\": {\"standoff\": 15}, \"zerolinecolor\": \"white\", \"zerolinewidth\": 2}, \"yaxis\": {\"automargin\": true, \"gridcolor\": \"white\", \"linecolor\": \"white\", \"ticks\": \"\", \"title\": {\"standoff\": 15}, \"zerolinecolor\": \"white\", \"zerolinewidth\": 2}}}, \"width\": 1000, \"xaxis\": {\"anchor\": \"y\", \"domain\": [0.0, 0.94], \"title\": {\"text\": \"Resolution\"}}, \"yaxis\": {\"anchor\": \"x\", \"color\": \"blue\", \"domain\": [0.0, 1.0], \"title\": {\"text\": \"Mean time per protein [s]\"}}, \"yaxis2\": {\"anchor\": \"x\", \"color\": \"red\", \"overlaying\": \"y\", \"side\": \"right\", \"title\": {\"text\": \"Mean GPU memory usage per protein [MB]\"}}},                        {\"responsive\": true}                    ).then(function(){\n                            \nvar gd = document.getElementById('c1525a75-7b16-4585-8d64-a25ca762f863');\nvar x = new MutationObserver(function (mutations, observer) {{\n        var display = window.getComputedStyle(gd).display;\n        if (!display || display === 'none') {{\n            console.log([gd, 'removed!']);\n            Plotly.purge(gd);\n            observer.disconnect();\n        }}\n}});\n\n// Listen for the removal of the full notebook cells\nvar notebookContainer = gd.closest('#notebook-container');\nif (notebookContainer) {{\n    x.observe(notebookContainer, {childList: true});\n}}\n\n// Listen for the clearing of the current output cell\nvar outputEl = gd.closest('.output');\nif (outputEl) {{\n    x.observe(outputEl, {childList: true});\n}}\n\n                        })                };                });            </script>        </div>"}, "metadata": {}}, {"output_type": "display_data", "data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"error_y": {"array": [0.043876299933383066, 0.007938229059877963, 0.0028496638923647473, 0.002479871190179921], "type": "data", "visible": true}, "marker": {"size": 12, "symbol": "star"}, "name": "Time", "type": "scatter", "x": [0.5, 1, 2, 4], "xaxis": "x", "y": [0.06476160570887055, 0.028156291123311916, 0.019225212902698702, 0.01614426036693307], "yaxis": "y"}, {"error_y": {"array": [15.748010034470598, 5.800258135139271, 2.2001731549839607, 1.083678960105017], "type": "data", "visible": true}, "marker": {"size": 10, "symbol": "square"}, "name": "Memory", "type": "scatter", "x": [0.5, 1, 2, 4], "xaxis": "x", "y": [33.39329525154457, 12.603112410944396, 4.8790290301853485, 2.3700945454545455], "yaxis": "y2"}], "layout": {"autosize": false, "font": {"size": 18}, "height": 800, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "width": 1000, "xaxis": {"anchor": "y", "domain": [0, 0.94], "title": {"text": "Resolution"}}, "yaxis": {"anchor": "x", "color": "blue", "domain": [0, 1], "title": {"text": "Mean time per protein [s]"}}, "yaxis2": {"anchor": "x", "color": "red", "overlaying": "y", "side": "right", "title": {"text": "Mean GPU memory usage per protein [MB]"}}}}, "text/html": "<div>                            <div id=\"25dd8b0c-ebe0-4d50-9006-c3acf19582dd\" class=\"plotly-graph-div\" style=\"height:800px; width:1000px;\"></div>            <script type=\"text/javascript\">                require([\"plotly\"], function(Plotly) {                    window.PLOTLYENV=window.PLOTLYENV || {};                                    if (document.getElementById(\"25dd8b0c-ebe0-4d50-9006-c3acf19582dd\")) {                    Plotly.newPlot(                        \"25dd8b0c-ebe0-4d50-9006-c3acf19582dd\",                        [{\"error_y\": {\"array\": [0.043876299933383066, 0.007938229059877963, 0.0028496638923647473, 0.002479871190179921], \"type\": \"data\", \"visible\": true}, \"marker\": {\"size\": 12, \"symbol\": \"star\"}, \"name\": \"Time\", \"type\": \"scatter\", \"x\": [0.5, 1.0, 2.0, 4.0], \"xaxis\": \"x\", \"y\": [0.06476160570887055, 0.028156291123311916, 0.019225212902698702, 0.01614426036693307], \"yaxis\": \"y\"}, {\"error_y\": {\"array\": [15.748010034470598, 5.800258135139271, 2.2001731549839607, 1.083678960105017], \"type\": \"data\", \"visible\": true}, \"marker\": {\"size\": 10, \"symbol\": \"square\"}, \"name\": \"Memory\", \"type\": \"scatter\", \"x\": [0.5, 1.0, 2.0, 4.0], \"xaxis\": \"x\", \"y\": [33.39329525154457, 12.603112410944396, 4.8790290301853485, 2.3700945454545455], \"yaxis\": \"y2\"}],                        {\"autosize\": false, \"font\": {\"size\": 18}, \"height\": 800, \"template\": {\"data\": {\"bar\": [{\"error_x\": {\"color\": \"#2a3f5f\"}, \"error_y\": {\"color\": \"#2a3f5f\"}, \"marker\": {\"line\": {\"color\": \"#E5ECF6\", \"width\": 0.5}}, \"type\": \"bar\"}], \"barpolar\": [{\"marker\": {\"line\": {\"color\": \"#E5ECF6\", \"width\": 0.5}}, \"type\": \"barpolar\"}], \"carpet\": [{\"aaxis\": {\"endlinecolor\": \"#2a3f5f\", \"gridcolor\": \"white\", \"linecolor\": \"white\", \"minorgridcolor\": \"white\", \"startlinecolor\": \"#2a3f5f\"}, \"baxis\": {\"endlinecolor\": \"#2a3f5f\", \"gridcolor\": \"white\", \"linecolor\": \"white\", \"minorgridcolor\": \"white\", \"startlinecolor\": \"#2a3f5f\"}, \"type\": \"carpet\"}], \"choropleth\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"type\": \"choropleth\"}], \"contour\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"colorscale\": [[0.0, \"#0d0887\"], [0.1111111111111111, \"#46039f\"], [0.2222222222222222, \"#7201a8\"], [0.3333333333333333, \"#9c179e\"], [0.4444444444444444, \"#bd3786\"], [0.5555555555555556, \"#d8576b\"], [0.6666666666666666, \"#ed7953\"], [0.7777777777777778, \"#fb9f3a\"], [0.8888888888888888, \"#fdca26\"], [1.0, \"#f0f921\"]], \"type\": \"contour\"}], \"contourcarpet\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"type\": \"contourcarpet\"}], \"heatmap\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"colorscale\": [[0.0, \"#0d0887\"], [0.1111111111111111, \"#46039f\"], [0.2222222222222222, \"#7201a8\"], [0.3333333333333333, \"#9c179e\"], [0.4444444444444444, \"#bd3786\"], [0.5555555555555556, \"#d8576b\"], [0.6666666666666666, \"#ed7953\"], [0.7777777777777778, \"#fb9f3a\"], [0.8888888888888888, \"#fdca26\"], [1.0, \"#f0f921\"]], \"type\": \"heatmap\"}], \"heatmapgl\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"colorscale\": [[0.0, \"#0d0887\"], [0.1111111111111111, \"#46039f\"], [0.2222222222222222, \"#7201a8\"], [0.3333333333333333, \"#9c179e\"], [0.4444444444444444, \"#bd3786\"], [0.5555555555555556, \"#d8576b\"], [0.6666666666666666, \"#ed7953\"], [0.7777777777777778, \"#fb9f3a\"], [0.8888888888888888, \"#fdca26\"], [1.0, \"#f0f921\"]], \"type\": \"heatmapgl\"}], \"histogram\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"histogram\"}], \"histogram2d\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"colorscale\": [[0.0, \"#0d0887\"], [0.1111111111111111, \"#46039f\"], [0.2222222222222222, \"#7201a8\"], [0.3333333333333333, \"#9c179e\"], [0.4444444444444444, \"#bd3786\"], [0.5555555555555556, \"#d8576b\"], [0.6666666666666666, \"#ed7953\"], [0.7777777777777778, \"#fb9f3a\"], [0.8888888888888888, \"#fdca26\"], [1.0, \"#f0f921\"]], \"type\": \"histogram2d\"}], \"histogram2dcontour\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"colorscale\": [[0.0, \"#0d0887\"], [0.1111111111111111, \"#46039f\"], [0.2222222222222222, \"#7201a8\"], [0.3333333333333333, \"#9c179e\"], [0.4444444444444444, \"#bd3786\"], [0.5555555555555556, \"#d8576b\"], [0.6666666666666666, \"#ed7953\"], [0.7777777777777778, \"#fb9f3a\"], [0.8888888888888888, \"#fdca26\"], [1.0, \"#f0f921\"]], \"type\": \"histogram2dcontour\"}], \"mesh3d\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"type\": \"mesh3d\"}], \"parcoords\": [{\"line\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"parcoords\"}], \"pie\": [{\"automargin\": true, \"type\": \"pie\"}], \"scatter\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scatter\"}], \"scatter3d\": [{\"line\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scatter3d\"}], \"scattercarpet\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scattercarpet\"}], \"scattergeo\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scattergeo\"}], \"scattergl\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scattergl\"}], \"scattermapbox\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scattermapbox\"}], \"scatterpolar\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scatterpolar\"}], \"scatterpolargl\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scatterpolargl\"}], \"scatterternary\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scatterternary\"}], \"surface\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"colorscale\": [[0.0, \"#0d0887\"], [0.1111111111111111, \"#46039f\"], [0.2222222222222222, \"#7201a8\"], [0.3333333333333333, \"#9c179e\"], [0.4444444444444444, \"#bd3786\"], [0.5555555555555556, \"#d8576b\"], [0.6666666666666666, \"#ed7953\"], [0.7777777777777778, \"#fb9f3a\"], [0.8888888888888888, \"#fdca26\"], [1.0, \"#f0f921\"]], \"type\": \"surface\"}], \"table\": [{\"cells\": {\"fill\": {\"color\": \"#EBF0F8\"}, \"line\": {\"color\": \"white\"}}, \"header\": {\"fill\": {\"color\": \"#C8D4E3\"}, \"line\": {\"color\": \"white\"}}, \"type\": \"table\"}]}, \"layout\": {\"annotationdefaults\": {\"arrowcolor\": \"#2a3f5f\", \"arrowhead\": 0, \"arrowwidth\": 1}, \"coloraxis\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"colorscale\": {\"diverging\": [[0, \"#8e0152\"], [0.1, \"#c51b7d\"], [0.2, \"#de77ae\"], [0.3, \"#f1b6da\"], [0.4, \"#fde0ef\"], [0.5, \"#f7f7f7\"], [0.6, \"#e6f5d0\"], [0.7, \"#b8e186\"], [0.8, \"#7fbc41\"], [0.9, \"#4d9221\"], [1, \"#276419\"]], \"sequential\": [[0.0, \"#0d0887\"], [0.1111111111111111, \"#46039f\"], [0.2222222222222222, \"#7201a8\"], [0.3333333333333333, \"#9c179e\"], [0.4444444444444444, \"#bd3786\"], [0.5555555555555556, \"#d8576b\"], [0.6666666666666666, \"#ed7953\"], [0.7777777777777778, \"#fb9f3a\"], [0.8888888888888888, \"#fdca26\"], [1.0, \"#f0f921\"]], \"sequentialminus\": [[0.0, \"#0d0887\"], [0.1111111111111111, \"#46039f\"], [0.2222222222222222, \"#7201a8\"], [0.3333333333333333, \"#9c179e\"], [0.4444444444444444, \"#bd3786\"], [0.5555555555555556, \"#d8576b\"], [0.6666666666666666, \"#ed7953\"], [0.7777777777777778, \"#fb9f3a\"], [0.8888888888888888, \"#fdca26\"], [1.0, \"#f0f921\"]]}, \"colorway\": [\"#636efa\", \"#EF553B\", \"#00cc96\", \"#ab63fa\", \"#FFA15A\", \"#19d3f3\", \"#FF6692\", \"#B6E880\", \"#FF97FF\", \"#FECB52\"], \"font\": {\"color\": \"#2a3f5f\"}, \"geo\": {\"bgcolor\": \"white\", \"lakecolor\": \"white\", \"landcolor\": \"#E5ECF6\", \"showlakes\": true, \"showland\": true, \"subunitcolor\": \"white\"}, \"hoverlabel\": {\"align\": \"left\"}, \"hovermode\": \"closest\", \"mapbox\": {\"style\": \"light\"}, \"paper_bgcolor\": \"white\", \"plot_bgcolor\": \"#E5ECF6\", \"polar\": {\"angularaxis\": {\"gridcolor\": \"white\", \"linecolor\": \"white\", \"ticks\": \"\"}, \"bgcolor\": \"#E5ECF6\", \"radialaxis\": {\"gridcolor\": \"white\", \"linecolor\": \"white\", \"ticks\": \"\"}}, \"scene\": {\"xaxis\": {\"backgroundcolor\": \"#E5ECF6\", \"gridcolor\": \"white\", \"gridwidth\": 2, \"linecolor\": \"white\", \"showbackground\": true, \"ticks\": \"\", \"zerolinecolor\": \"white\"}, \"yaxis\": {\"backgroundcolor\": \"#E5ECF6\", \"gridcolor\": \"white\", \"gridwidth\": 2, \"linecolor\": \"white\", \"showbackground\": true, \"ticks\": \"\", \"zerolinecolor\": \"white\"}, \"zaxis\": {\"backgroundcolor\": \"#E5ECF6\", \"gridcolor\": \"white\", \"gridwidth\": 2, \"linecolor\": \"white\", \"showbackground\": true, \"ticks\": \"\", \"zerolinecolor\": \"white\"}}, \"shapedefaults\": {\"line\": {\"color\": \"#2a3f5f\"}}, \"ternary\": {\"aaxis\": {\"gridcolor\": \"white\", \"linecolor\": \"white\", \"ticks\": \"\"}, \"baxis\": {\"gridcolor\": \"white\", \"linecolor\": \"white\", \"ticks\": \"\"}, \"bgcolor\": \"#E5ECF6\", \"caxis\": {\"gridcolor\": \"white\", \"linecolor\": \"white\", \"ticks\": \"\"}}, \"title\": {\"x\": 0.05}, \"xaxis\": {\"automargin\": true, \"gridcolor\": \"white\", \"linecolor\": \"white\", \"ticks\": \"\", \"title\": {\"standoff\": 15}, \"zerolinecolor\": \"white\", \"zerolinewidth\": 2}, \"yaxis\": {\"automargin\": true, \"gridcolor\": \"white\", \"linecolor\": \"white\", \"ticks\": \"\", \"title\": {\"standoff\": 15}, \"zerolinecolor\": \"white\", \"zerolinewidth\": 2}}}, \"width\": 1000, \"xaxis\": {\"anchor\": \"y\", \"domain\": [0.0, 0.94], \"title\": {\"text\": \"Resolution\"}}, \"yaxis\": {\"anchor\": \"x\", \"color\": \"blue\", \"domain\": [0.0, 1.0], \"title\": {\"text\": \"Mean time per protein [s]\"}}, \"yaxis2\": {\"anchor\": \"x\", \"color\": \"red\", \"overlaying\": \"y\", \"side\": \"right\", \"title\": {\"text\": \"Mean GPU memory usage per protein [MB]\"}}},                        {\"responsive\": true}                    ).then(function(){\n                            \nvar gd = document.getElementById('25dd8b0c-ebe0-4d50-9006-c3acf19582dd');\nvar x = new MutationObserver(function (mutations, observer) {{\n        var display = window.getComputedStyle(gd).display;\n        if (!display || display === 'none') {{\n            console.log([gd, 'removed!']);\n            Plotly.purge(gd);\n            observer.disconnect();\n        }}\n}});\n\n// Listen for the removal of the full notebook cells\nvar notebookContainer = gd.closest('#notebook-container');\nif (notebookContainer) {{\n    x.observe(notebookContainer, {childList: true});\n}}\n\n// Listen for the clearing of the current output cell\nvar outputEl = gd.closest('.output');\nif (outputEl) {{\n    x.observe(outputEl, {childList: true});\n}}\n\n                        })                };                });            </script>        </div>"}, "metadata": {}}, {"output_type": "display_data", "data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"error_y": {"array": [0.0034462911768476357, 0.0006948794730123882, 0.0007856585140201795, 0.00046908884903664025], "type": "data", "visible": true}, "marker": {"size": 12, "symbol": "star"}, "name": "Time", "type": "scatter", "x": [0.5, 1, 2, 4], "xaxis": "x", "y": [0.006644930422884747, 0.00391388593894238, 0.003369788416486237, 0.0031363786266424545], "yaxis": "y"}, {"error_y": {"array": [4.3085336393399185, 1.8445861098663745, 0.9905885268813438, 0.7236619627005592], "type": "data", "visible": true}, "marker": {"size": 10, "symbol": "square"}, "name": "Memory", "type": "scatter", "x": [0.5, 1, 2, 4], "xaxis": "x", "y": [10.0491348956752, 4.3401819735216245, 2.244365918446602, 1.5685022842012357], "yaxis": "y2"}], "layout": {"autosize": false, "font": {"size": 18}, "height": 800, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "width": 1000, "xaxis": {"anchor": "y", "domain": [0, 0.94], "title": {"text": "Resolution"}}, "yaxis": {"anchor": "x", "color": "blue", "domain": [0, 1], "title": {"text": "Mean time per protein [s]"}}, "yaxis2": {"anchor": "x", "color": "red", "overlaying": "y", "side": "right", "title": {"text": "Mean GPU memory usage per protein [MB]"}}}}, "text/html": "<div>                            <div id=\"0c7c4562-0c64-48da-ab77-0ffece60526a\" class=\"plotly-graph-div\" style=\"height:800px; width:1000px;\"></div>            <script type=\"text/javascript\">                require([\"plotly\"], function(Plotly) {                    window.PLOTLYENV=window.PLOTLYENV || {};                                    if (document.getElementById(\"0c7c4562-0c64-48da-ab77-0ffece60526a\")) {                    Plotly.newPlot(                        \"0c7c4562-0c64-48da-ab77-0ffece60526a\",                        [{\"error_y\": {\"array\": [0.0034462911768476357, 0.0006948794730123882, 0.0007856585140201795, 0.00046908884903664025], \"type\": \"data\", \"visible\": true}, \"marker\": {\"size\": 12, \"symbol\": \"star\"}, \"name\": \"Time\", \"type\": \"scatter\", \"x\": [0.5, 1.0, 2.0, 4.0], \"xaxis\": \"x\", \"y\": [0.006644930422884747, 0.00391388593894238, 0.003369788416486237, 0.0031363786266424545], \"yaxis\": \"y\"}, {\"error_y\": {\"array\": [4.3085336393399185, 1.8445861098663745, 0.9905885268813438, 0.7236619627005592], \"type\": \"data\", \"visible\": true}, \"marker\": {\"size\": 10, \"symbol\": \"square\"}, \"name\": \"Memory\", \"type\": \"scatter\", \"x\": [0.5, 1.0, 2.0, 4.0], \"xaxis\": \"x\", \"y\": [10.0491348956752, 4.3401819735216245, 2.244365918446602, 1.5685022842012357], \"yaxis\": \"y2\"}],                        {\"autosize\": false, \"font\": {\"size\": 18}, \"height\": 800, \"template\": {\"data\": {\"bar\": [{\"error_x\": {\"color\": \"#2a3f5f\"}, \"error_y\": {\"color\": \"#2a3f5f\"}, \"marker\": {\"line\": {\"color\": \"#E5ECF6\", \"width\": 0.5}}, \"type\": \"bar\"}], \"barpolar\": [{\"marker\": {\"line\": {\"color\": \"#E5ECF6\", \"width\": 0.5}}, \"type\": \"barpolar\"}], \"carpet\": [{\"aaxis\": {\"endlinecolor\": \"#2a3f5f\", \"gridcolor\": \"white\", \"linecolor\": \"white\", \"minorgridcolor\": \"white\", \"startlinecolor\": \"#2a3f5f\"}, \"baxis\": {\"endlinecolor\": \"#2a3f5f\", \"gridcolor\": \"white\", \"linecolor\": \"white\", \"minorgridcolor\": \"white\", \"startlinecolor\": \"#2a3f5f\"}, \"type\": \"carpet\"}], \"choropleth\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"type\": \"choropleth\"}], \"contour\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"colorscale\": [[0.0, \"#0d0887\"], [0.1111111111111111, \"#46039f\"], [0.2222222222222222, \"#7201a8\"], [0.3333333333333333, \"#9c179e\"], [0.4444444444444444, \"#bd3786\"], [0.5555555555555556, \"#d8576b\"], [0.6666666666666666, \"#ed7953\"], [0.7777777777777778, \"#fb9f3a\"], [0.8888888888888888, \"#fdca26\"], [1.0, \"#f0f921\"]], \"type\": \"contour\"}], \"contourcarpet\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"type\": \"contourcarpet\"}], \"heatmap\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"colorscale\": [[0.0, \"#0d0887\"], [0.1111111111111111, \"#46039f\"], [0.2222222222222222, \"#7201a8\"], [0.3333333333333333, \"#9c179e\"], [0.4444444444444444, \"#bd3786\"], [0.5555555555555556, \"#d8576b\"], [0.6666666666666666, \"#ed7953\"], [0.7777777777777778, \"#fb9f3a\"], [0.8888888888888888, \"#fdca26\"], [1.0, \"#f0f921\"]], \"type\": \"heatmap\"}], \"heatmapgl\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"colorscale\": [[0.0, \"#0d0887\"], [0.1111111111111111, \"#46039f\"], [0.2222222222222222, \"#7201a8\"], [0.3333333333333333, \"#9c179e\"], [0.4444444444444444, \"#bd3786\"], [0.5555555555555556, \"#d8576b\"], [0.6666666666666666, \"#ed7953\"], [0.7777777777777778, \"#fb9f3a\"], [0.8888888888888888, \"#fdca26\"], [1.0, \"#f0f921\"]], \"type\": \"heatmapgl\"}], \"histogram\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"histogram\"}], \"histogram2d\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"colorscale\": [[0.0, \"#0d0887\"], [0.1111111111111111, \"#46039f\"], [0.2222222222222222, \"#7201a8\"], [0.3333333333333333, \"#9c179e\"], [0.4444444444444444, \"#bd3786\"], [0.5555555555555556, \"#d8576b\"], [0.6666666666666666, \"#ed7953\"], [0.7777777777777778, \"#fb9f3a\"], [0.8888888888888888, \"#fdca26\"], [1.0, \"#f0f921\"]], \"type\": \"histogram2d\"}], \"histogram2dcontour\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"colorscale\": [[0.0, \"#0d0887\"], [0.1111111111111111, \"#46039f\"], [0.2222222222222222, \"#7201a8\"], [0.3333333333333333, \"#9c179e\"], [0.4444444444444444, \"#bd3786\"], [0.5555555555555556, \"#d8576b\"], [0.6666666666666666, \"#ed7953\"], [0.7777777777777778, \"#fb9f3a\"], [0.8888888888888888, \"#fdca26\"], [1.0, \"#f0f921\"]], \"type\": \"histogram2dcontour\"}], \"mesh3d\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"type\": \"mesh3d\"}], \"parcoords\": [{\"line\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"parcoords\"}], \"pie\": [{\"automargin\": true, \"type\": \"pie\"}], \"scatter\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scatter\"}], \"scatter3d\": [{\"line\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scatter3d\"}], \"scattercarpet\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scattercarpet\"}], \"scattergeo\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scattergeo\"}], \"scattergl\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scattergl\"}], \"scattermapbox\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scattermapbox\"}], \"scatterpolar\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scatterpolar\"}], \"scatterpolargl\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scatterpolargl\"}], \"scatterternary\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scatterternary\"}], \"surface\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"colorscale\": [[0.0, \"#0d0887\"], [0.1111111111111111, \"#46039f\"], [0.2222222222222222, \"#7201a8\"], [0.3333333333333333, \"#9c179e\"], [0.4444444444444444, \"#bd3786\"], [0.5555555555555556, \"#d8576b\"], [0.6666666666666666, \"#ed7953\"], [0.7777777777777778, \"#fb9f3a\"], [0.8888888888888888, \"#fdca26\"], [1.0, \"#f0f921\"]], \"type\": \"surface\"}], \"table\": [{\"cells\": {\"fill\": {\"color\": \"#EBF0F8\"}, \"line\": {\"color\": \"white\"}}, \"header\": {\"fill\": {\"color\": \"#C8D4E3\"}, \"line\": {\"color\": \"white\"}}, \"type\": \"table\"}]}, \"layout\": {\"annotationdefaults\": {\"arrowcolor\": \"#2a3f5f\", \"arrowhead\": 0, \"arrowwidth\": 1}, \"coloraxis\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"colorscale\": {\"diverging\": [[0, \"#8e0152\"], [0.1, \"#c51b7d\"], [0.2, \"#de77ae\"], [0.3, \"#f1b6da\"], [0.4, \"#fde0ef\"], [0.5, \"#f7f7f7\"], [0.6, \"#e6f5d0\"], [0.7, \"#b8e186\"], [0.8, \"#7fbc41\"], [0.9, \"#4d9221\"], [1, \"#276419\"]], \"sequential\": [[0.0, \"#0d0887\"], [0.1111111111111111, \"#46039f\"], [0.2222222222222222, \"#7201a8\"], [0.3333333333333333, \"#9c179e\"], [0.4444444444444444, \"#bd3786\"], [0.5555555555555556, \"#d8576b\"], [0.6666666666666666, \"#ed7953\"], [0.7777777777777778, \"#fb9f3a\"], [0.8888888888888888, \"#fdca26\"], [1.0, \"#f0f921\"]], \"sequentialminus\": [[0.0, \"#0d0887\"], [0.1111111111111111, \"#46039f\"], [0.2222222222222222, \"#7201a8\"], [0.3333333333333333, \"#9c179e\"], [0.4444444444444444, \"#bd3786\"], [0.5555555555555556, \"#d8576b\"], [0.6666666666666666, \"#ed7953\"], [0.7777777777777778, \"#fb9f3a\"], [0.8888888888888888, \"#fdca26\"], [1.0, \"#f0f921\"]]}, \"colorway\": [\"#636efa\", \"#EF553B\", \"#00cc96\", \"#ab63fa\", \"#FFA15A\", \"#19d3f3\", \"#FF6692\", \"#B6E880\", \"#FF97FF\", \"#FECB52\"], \"font\": {\"color\": \"#2a3f5f\"}, \"geo\": {\"bgcolor\": \"white\", \"lakecolor\": \"white\", \"landcolor\": \"#E5ECF6\", \"showlakes\": true, \"showland\": true, \"subunitcolor\": \"white\"}, \"hoverlabel\": {\"align\": \"left\"}, \"hovermode\": \"closest\", \"mapbox\": {\"style\": \"light\"}, \"paper_bgcolor\": \"white\", \"plot_bgcolor\": \"#E5ECF6\", \"polar\": {\"angularaxis\": {\"gridcolor\": \"white\", \"linecolor\": \"white\", \"ticks\": \"\"}, \"bgcolor\": \"#E5ECF6\", \"radialaxis\": {\"gridcolor\": \"white\", \"linecolor\": \"white\", \"ticks\": \"\"}}, \"scene\": {\"xaxis\": {\"backgroundcolor\": \"#E5ECF6\", \"gridcolor\": \"white\", \"gridwidth\": 2, \"linecolor\": \"white\", \"showbackground\": true, \"ticks\": \"\", \"zerolinecolor\": \"white\"}, \"yaxis\": {\"backgroundcolor\": \"#E5ECF6\", \"gridcolor\": \"white\", \"gridwidth\": 2, \"linecolor\": \"white\", \"showbackground\": true, \"ticks\": \"\", \"zerolinecolor\": \"white\"}, \"zaxis\": {\"backgroundcolor\": \"#E5ECF6\", \"gridcolor\": \"white\", \"gridwidth\": 2, \"linecolor\": \"white\", \"showbackground\": true, \"ticks\": \"\", \"zerolinecolor\": \"white\"}}, \"shapedefaults\": {\"line\": {\"color\": \"#2a3f5f\"}}, \"ternary\": {\"aaxis\": {\"gridcolor\": \"white\", \"linecolor\": \"white\", \"ticks\": \"\"}, \"baxis\": {\"gridcolor\": \"white\", \"linecolor\": \"white\", \"ticks\": \"\"}, \"bgcolor\": \"#E5ECF6\", \"caxis\": {\"gridcolor\": \"white\", \"linecolor\": \"white\", \"ticks\": \"\"}}, \"title\": {\"x\": 0.05}, \"xaxis\": {\"automargin\": true, \"gridcolor\": \"white\", \"linecolor\": \"white\", \"ticks\": \"\", \"title\": {\"standoff\": 15}, \"zerolinecolor\": \"white\", \"zerolinewidth\": 2}, \"yaxis\": {\"automargin\": true, \"gridcolor\": \"white\", \"linecolor\": \"white\", \"ticks\": \"\", \"title\": {\"standoff\": 15}, \"zerolinecolor\": \"white\", \"zerolinewidth\": 2}}}, \"width\": 1000, \"xaxis\": {\"anchor\": \"y\", \"domain\": [0.0, 0.94], \"title\": {\"text\": \"Resolution\"}}, \"yaxis\": {\"anchor\": \"x\", \"color\": \"blue\", \"domain\": [0.0, 1.0], \"title\": {\"text\": \"Mean time per protein [s]\"}}, \"yaxis2\": {\"anchor\": \"x\", \"color\": \"red\", \"overlaying\": \"y\", \"side\": \"right\", \"title\": {\"text\": \"Mean GPU memory usage per protein [MB]\"}}},                        {\"responsive\": true}                    ).then(function(){\n                            \nvar gd = document.getElementById('0c7c4562-0c64-48da-ab77-0ffece60526a');\nvar x = new MutationObserver(function (mutations, observer) {{\n        var display = window.getComputedStyle(gd).display;\n        if (!display || display === 'none') {{\n            console.log([gd, 'removed!']);\n            Plotly.purge(gd);\n            observer.disconnect();\n        }}\n}});\n\n// Listen for the removal of the full notebook cells\nvar notebookContainer = gd.closest('#notebook-container');\nif (notebookContainer) {{\n    x.observe(notebookContainer, {childList: true});\n}}\n\n// Listen for the clearing of the current output cell\nvar outputEl = gd.closest('.output');\nif (outputEl) {{\n    x.observe(outputEl, {childList: true});\n}}\n\n                        })                };                });            </script>        </div>"}, "metadata": {}}], "source": ["keys = ['surface', 'features', 'coordinates']\n", "for key in keys:\n", "    batch_size = 1\n", "    resolutions = [0.5,1.0,2.0,4.0]\n", "    sup_samplings = [40,20,10,5]\n", "    N = len(resolutions)\n", "\n", "    time_means = []\n", "    time_std = []\n", "    memory_means = []\n", "    memory_std = []\n", "    for i in range(N):\n", "        times = np.load(f'../timings/step_profiling_{key}_times_batch_{batch_size}_res_{resolutions[i]}_B_{sup_samplings[i]}.npy')\n", "        memory = np.load(f'../timings/step_profiling_{key}_memory_batch_{batch_size}_res_{resolutions[i]}_B_{sup_samplings[i]}.npy')\n", "        memory = memory*1e-6\n", "        time_means.append(times.mean())\n", "        memory_means.append(memory.mean())\n", "        time_std.append(times.std())\n", "        memory_std.append(memory.std())\n", "\n", "\n", "    fig = make_subplots(specs=[[{\"secondary_y\": True}]])\n", "    fig.add_trace(\n", "        go.Scatter(x=resolutions, y=time_means, name=\"Time\", marker_size=12, marker_symbol='star', error_y=dict(type='data',array=time_std,visible=True)),\n", "        secondary_y=False,\n", "    )\n", "\n", "    fig.add_trace(\n", "        go.Scatter(x=resolutions, y=memory_means, name=\"Memory\", marker_symbol='square', marker_size=10,error_y=dict(type='data',array=memory_std,visible=True)),\n", "        secondary_y=True,\n", "    )\n", "\n", "\n", "\n", "    fig.update_xaxes(title_text=\"Resolution\")\n", "    fig.update_yaxes(title_text=\"Mean time per protein [s]\", secondary_y=False,color='blue')\n", "    fig.update_yaxes(title_text=\"Mean GPU memory usage per protein [MB]\", secondary_y=True,color='red')\n", "\n", "\n", "\n", "    #fig.update_yaxes(secondary_y=False,tickvals = [0.04,0.06,0.08,0.1,0.2,0.4,0.6,0.8,1,2,4,6,8,10])\n", "    #fig.update_yaxes(secondary_y=False,range=[-1.5,1])\n", "\n", "\n", "\n", "    fig.update_layout(\n", "        autosize=False,\n", "        width=1000,\n", "        height=800,\n", "        font=dict(\n", "            size=18,\n", "        ))\n", "\n", "\n", "\n", "    fig.show()\n", "    fig.write_image(f'../figures/resolution_profiling_{key}.pdf')"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"output_type": "display_data", "data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"error_y": {"array": [0.09680264243368539, 0.06738993484603377, 0.0473130424679464, 0.034063817690196785, 0.02547246718384354, 0.019620533496723443, 0.016807760176632967, 0.015233961971048775], "type": "data", "visible": true}, "marker": {"size": 12, "symbol": "star"}, "name": "Time", "type": "scatter", "x": [1, 2, 4, 8, 16, 32, 64, 128], "xaxis": "x", "y": [0.12131211667174485, 0.09242735506997611, 0.07528613803247036, 0.06658043688207822, 0.062034064174537926, 0.059370067822503786, 0.058075338027446105, 0.05898624869739957], "yaxis": "y"}, {"error_y": {"array": [74.29559611714842, 37.54999437002707, 20.930336140004194, 13.567087319706069, 14.138378829235164, 3.6299869360967123, 3.2017728039015765, 4.3052114138489035], "type": "data", "visible": true}, "marker": {"size": 10, "symbol": "square"}, "name": "Memory", "type": "scatter", "x": [1, 2, 4, 8, 16, 32, 64, 128], "xaxis": "x", "y": [249.11686125472195, 148.49957338228026, 88.60460750317571, 53.76497624823695, 34.880394997183096, 23.698022699272965, 17.860115529411765, 15.111303670076724], "yaxis": "y2"}], "layout": {"autosize": false, "font": {"size": 18}, "height": 800, "shapes": [{"line": {"color": "Blue", "dash": "dot"}, "type": "line", "x0": 1, "x1": 128, "y0": 6.11, "y1": 6.11}], "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "width": 1000, "xaxis": {"anchor": "y", "domain": [0, 0.94], "tickvals": [1, 2, 4, 6, 8, 10, 20, 40, 60, 80, 100, 200], "title": {"text": "Batch size (log)"}, "type": "log"}, "yaxis": {"anchor": "x", "color": "blue", "domain": [0, 1], "range": [-1.5, 1], "tickvals": [0.04, 0.06, 0.08, 0.1, 0.2, 0.4, 0.6, 0.8, 1, 2, 4, 6, 8, 10], "title": {"text": "Mean time per protein [s] (log)"}, "type": "log"}, "yaxis2": {"anchor": "x", "color": "red", "overlaying": "y", "side": "right", "title": {"text": "Mean GPU memory usage per protein [MB]"}}}}, "text/html": "<div>                            <div id=\"b5212801-b3cf-4665-9b54-378339c2612e\" class=\"plotly-graph-div\" style=\"height:800px; width:1000px;\"></div>            <script type=\"text/javascript\">                require([\"plotly\"], function(Plotly) {                    window.PLOTLYENV=window.PLOTLYENV || {};                                    if (document.getElementById(\"b5212801-b3cf-4665-9b54-378339c2612e\")) {                    Plotly.newPlot(                        \"b5212801-b3cf-4665-9b54-378339c2612e\",                        [{\"error_y\": {\"array\": [0.09680264243368539, 0.06738993484603377, 0.0473130424679464, 0.034063817690196785, 0.02547246718384354, 0.019620533496723443, 0.016807760176632967, 0.015233961971048775], \"type\": \"data\", \"visible\": true}, \"marker\": {\"size\": 12, \"symbol\": \"star\"}, \"name\": \"Time\", \"type\": \"scatter\", \"x\": [1, 2, 4, 8, 16, 32, 64, 128], \"xaxis\": \"x\", \"y\": [0.12131211667174485, 0.09242735506997611, 0.07528613803247036, 0.06658043688207822, 0.062034064174537926, 0.059370067822503786, 0.058075338027446105, 0.05898624869739957], \"yaxis\": \"y\"}, {\"error_y\": {\"array\": [74.29559611714842, 37.54999437002707, 20.930336140004194, 13.567087319706069, 14.138378829235164, 3.6299869360967123, 3.2017728039015765, 4.3052114138489035], \"type\": \"data\", \"visible\": true}, \"marker\": {\"size\": 10, \"symbol\": \"square\"}, \"name\": \"Memory\", \"type\": \"scatter\", \"x\": [1, 2, 4, 8, 16, 32, 64, 128], \"xaxis\": \"x\", \"y\": [249.11686125472195, 148.49957338228026, 88.60460750317571, 53.76497624823695, 34.880394997183096, 23.698022699272965, 17.860115529411765, 15.111303670076724], \"yaxis\": \"y2\"}],                        {\"autosize\": false, \"font\": {\"size\": 18}, \"height\": 800, \"shapes\": [{\"line\": {\"color\": \"Blue\", \"dash\": \"dot\"}, \"type\": \"line\", \"x0\": 1, \"x1\": 128, \"y0\": 6.11, \"y1\": 6.11}], \"template\": {\"data\": {\"bar\": [{\"error_x\": {\"color\": \"#2a3f5f\"}, \"error_y\": {\"color\": \"#2a3f5f\"}, \"marker\": {\"line\": {\"color\": \"#E5ECF6\", \"width\": 0.5}}, \"type\": \"bar\"}], \"barpolar\": [{\"marker\": {\"line\": {\"color\": \"#E5ECF6\", \"width\": 0.5}}, \"type\": \"barpolar\"}], \"carpet\": [{\"aaxis\": {\"endlinecolor\": \"#2a3f5f\", \"gridcolor\": \"white\", \"linecolor\": \"white\", \"minorgridcolor\": \"white\", \"startlinecolor\": \"#2a3f5f\"}, \"baxis\": {\"endlinecolor\": \"#2a3f5f\", \"gridcolor\": \"white\", \"linecolor\": \"white\", \"minorgridcolor\": \"white\", \"startlinecolor\": \"#2a3f5f\"}, \"type\": \"carpet\"}], \"choropleth\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"type\": \"choropleth\"}], \"contour\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"colorscale\": [[0.0, \"#0d0887\"], [0.1111111111111111, \"#46039f\"], [0.2222222222222222, \"#7201a8\"], [0.3333333333333333, \"#9c179e\"], [0.4444444444444444, \"#bd3786\"], [0.5555555555555556, \"#d8576b\"], [0.6666666666666666, \"#ed7953\"], [0.7777777777777778, \"#fb9f3a\"], [0.8888888888888888, \"#fdca26\"], [1.0, \"#f0f921\"]], \"type\": \"contour\"}], \"contourcarpet\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"type\": \"contourcarpet\"}], \"heatmap\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"colorscale\": [[0.0, \"#0d0887\"], [0.1111111111111111, \"#46039f\"], [0.2222222222222222, \"#7201a8\"], [0.3333333333333333, \"#9c179e\"], [0.4444444444444444, \"#bd3786\"], [0.5555555555555556, \"#d8576b\"], [0.6666666666666666, \"#ed7953\"], [0.7777777777777778, \"#fb9f3a\"], [0.8888888888888888, \"#fdca26\"], [1.0, \"#f0f921\"]], \"type\": \"heatmap\"}], \"heatmapgl\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"colorscale\": [[0.0, \"#0d0887\"], [0.1111111111111111, \"#46039f\"], [0.2222222222222222, \"#7201a8\"], [0.3333333333333333, \"#9c179e\"], [0.4444444444444444, \"#bd3786\"], [0.5555555555555556, \"#d8576b\"], [0.6666666666666666, \"#ed7953\"], [0.7777777777777778, \"#fb9f3a\"], [0.8888888888888888, \"#fdca26\"], [1.0, \"#f0f921\"]], \"type\": \"heatmapgl\"}], \"histogram\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"histogram\"}], \"histogram2d\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"colorscale\": [[0.0, \"#0d0887\"], [0.1111111111111111, \"#46039f\"], [0.2222222222222222, \"#7201a8\"], [0.3333333333333333, \"#9c179e\"], [0.4444444444444444, \"#bd3786\"], [0.5555555555555556, \"#d8576b\"], [0.6666666666666666, \"#ed7953\"], [0.7777777777777778, \"#fb9f3a\"], [0.8888888888888888, \"#fdca26\"], [1.0, \"#f0f921\"]], \"type\": \"histogram2d\"}], \"histogram2dcontour\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"colorscale\": [[0.0, \"#0d0887\"], [0.1111111111111111, \"#46039f\"], [0.2222222222222222, \"#7201a8\"], [0.3333333333333333, \"#9c179e\"], [0.4444444444444444, \"#bd3786\"], [0.5555555555555556, \"#d8576b\"], [0.6666666666666666, \"#ed7953\"], [0.7777777777777778, \"#fb9f3a\"], [0.8888888888888888, \"#fdca26\"], [1.0, \"#f0f921\"]], \"type\": \"histogram2dcontour\"}], \"mesh3d\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"type\": \"mesh3d\"}], \"parcoords\": [{\"line\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"parcoords\"}], \"pie\": [{\"automargin\": true, \"type\": \"pie\"}], \"scatter\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scatter\"}], \"scatter3d\": [{\"line\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scatter3d\"}], \"scattercarpet\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scattercarpet\"}], \"scattergeo\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scattergeo\"}], \"scattergl\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scattergl\"}], \"scattermapbox\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scattermapbox\"}], \"scatterpolar\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scatterpolar\"}], \"scatterpolargl\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scatterpolargl\"}], \"scatterternary\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scatterternary\"}], \"surface\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"colorscale\": [[0.0, \"#0d0887\"], [0.1111111111111111, \"#46039f\"], [0.2222222222222222, \"#7201a8\"], [0.3333333333333333, \"#9c179e\"], [0.4444444444444444, \"#bd3786\"], [0.5555555555555556, \"#d8576b\"], [0.6666666666666666, \"#ed7953\"], [0.7777777777777778, \"#fb9f3a\"], [0.8888888888888888, \"#fdca26\"], [1.0, \"#f0f921\"]], \"type\": \"surface\"}], \"table\": [{\"cells\": {\"fill\": {\"color\": \"#EBF0F8\"}, \"line\": {\"color\": \"white\"}}, \"header\": {\"fill\": {\"color\": \"#C8D4E3\"}, \"line\": {\"color\": \"white\"}}, \"type\": \"table\"}]}, \"layout\": {\"annotationdefaults\": {\"arrowcolor\": \"#2a3f5f\", \"arrowhead\": 0, \"arrowwidth\": 1}, \"coloraxis\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"colorscale\": {\"diverging\": [[0, \"#8e0152\"], [0.1, \"#c51b7d\"], [0.2, \"#de77ae\"], [0.3, \"#f1b6da\"], [0.4, \"#fde0ef\"], [0.5, \"#f7f7f7\"], [0.6, \"#e6f5d0\"], [0.7, \"#b8e186\"], [0.8, \"#7fbc41\"], [0.9, \"#4d9221\"], [1, \"#276419\"]], \"sequential\": [[0.0, \"#0d0887\"], [0.1111111111111111, \"#46039f\"], [0.2222222222222222, \"#7201a8\"], [0.3333333333333333, \"#9c179e\"], [0.4444444444444444, \"#bd3786\"], [0.5555555555555556, \"#d8576b\"], [0.6666666666666666, \"#ed7953\"], [0.7777777777777778, \"#fb9f3a\"], [0.8888888888888888, \"#fdca26\"], [1.0, \"#f0f921\"]], \"sequentialminus\": [[0.0, \"#0d0887\"], [0.1111111111111111, \"#46039f\"], [0.2222222222222222, \"#7201a8\"], [0.3333333333333333, \"#9c179e\"], [0.4444444444444444, \"#bd3786\"], [0.5555555555555556, \"#d8576b\"], [0.6666666666666666, \"#ed7953\"], [0.7777777777777778, \"#fb9f3a\"], [0.8888888888888888, \"#fdca26\"], [1.0, \"#f0f921\"]]}, \"colorway\": [\"#636efa\", \"#EF553B\", \"#00cc96\", \"#ab63fa\", \"#FFA15A\", \"#19d3f3\", \"#FF6692\", \"#B6E880\", \"#FF97FF\", \"#FECB52\"], \"font\": {\"color\": \"#2a3f5f\"}, \"geo\": {\"bgcolor\": \"white\", \"lakecolor\": \"white\", \"landcolor\": \"#E5ECF6\", \"showlakes\": true, \"showland\": true, \"subunitcolor\": \"white\"}, \"hoverlabel\": {\"align\": \"left\"}, \"hovermode\": \"closest\", \"mapbox\": {\"style\": \"light\"}, \"paper_bgcolor\": \"white\", \"plot_bgcolor\": \"#E5ECF6\", \"polar\": {\"angularaxis\": {\"gridcolor\": \"white\", \"linecolor\": \"white\", \"ticks\": \"\"}, \"bgcolor\": \"#E5ECF6\", \"radialaxis\": {\"gridcolor\": \"white\", \"linecolor\": \"white\", \"ticks\": \"\"}}, \"scene\": {\"xaxis\": {\"backgroundcolor\": \"#E5ECF6\", \"gridcolor\": \"white\", \"gridwidth\": 2, \"linecolor\": \"white\", \"showbackground\": true, \"ticks\": \"\", \"zerolinecolor\": \"white\"}, \"yaxis\": {\"backgroundcolor\": \"#E5ECF6\", \"gridcolor\": \"white\", \"gridwidth\": 2, \"linecolor\": \"white\", \"showbackground\": true, \"ticks\": \"\", \"zerolinecolor\": \"white\"}, \"zaxis\": {\"backgroundcolor\": \"#E5ECF6\", \"gridcolor\": \"white\", \"gridwidth\": 2, \"linecolor\": \"white\", \"showbackground\": true, \"ticks\": \"\", \"zerolinecolor\": \"white\"}}, \"shapedefaults\": {\"line\": {\"color\": \"#2a3f5f\"}}, \"ternary\": {\"aaxis\": {\"gridcolor\": \"white\", \"linecolor\": \"white\", \"ticks\": \"\"}, \"baxis\": {\"gridcolor\": \"white\", \"linecolor\": \"white\", \"ticks\": \"\"}, \"bgcolor\": \"#E5ECF6\", \"caxis\": {\"gridcolor\": \"white\", \"linecolor\": \"white\", \"ticks\": \"\"}}, \"title\": {\"x\": 0.05}, \"xaxis\": {\"automargin\": true, \"gridcolor\": \"white\", \"linecolor\": \"white\", \"ticks\": \"\", \"title\": {\"standoff\": 15}, \"zerolinecolor\": \"white\", \"zerolinewidth\": 2}, \"yaxis\": {\"automargin\": true, \"gridcolor\": \"white\", \"linecolor\": \"white\", \"ticks\": \"\", \"title\": {\"standoff\": 15}, \"zerolinecolor\": \"white\", \"zerolinewidth\": 2}}}, \"width\": 1000, \"xaxis\": {\"anchor\": \"y\", \"domain\": [0.0, 0.94], \"tickvals\": [1, 2, 4, 6, 8, 10, 20, 40, 60, 80, 100, 200], \"title\": {\"text\": \"Batch size (log)\"}, \"type\": \"log\"}, \"yaxis\": {\"anchor\": \"x\", \"color\": \"blue\", \"domain\": [0.0, 1.0], \"range\": [-1.5, 1], \"tickvals\": [0.04, 0.06, 0.08, 0.1, 0.2, 0.4, 0.6, 0.8, 1, 2, 4, 6, 8, 10], \"title\": {\"text\": \"Mean time per protein [s] (log)\"}, \"type\": \"log\"}, \"yaxis2\": {\"anchor\": \"x\", \"color\": \"red\", \"overlaying\": \"y\", \"side\": \"right\", \"title\": {\"text\": \"Mean GPU memory usage per protein [MB]\"}}},                        {\"responsive\": true}                    ).then(function(){\n                            \nvar gd = document.getElementById('b5212801-b3cf-4665-9b54-378339c2612e');\nvar x = new MutationObserver(function (mutations, observer) {{\n        var display = window.getComputedStyle(gd).display;\n        if (!display || display === 'none') {{\n            console.log([gd, 'removed!']);\n            Plotly.purge(gd);\n            observer.disconnect();\n        }}\n}});\n\n// Listen for the removal of the full notebook cells\nvar notebookContainer = gd.closest('#notebook-container');\nif (notebookContainer) {{\n    x.observe(notebookContainer, {childList: true});\n}}\n\n// Listen for the clearing of the current output cell\nvar outputEl = gd.closest('.output');\nif (outputEl) {{\n    x.observe(outputEl, {childList: true});\n}}\n\n                        })                };                });            </script>        </div>"}, "metadata": {}}, {"output_type": "display_data", "data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"error_y": {"array": [0.007938229059877963, 0.005599115237226402, 0.003436053099386169, 0.0026162141417493227, 0.0024595882701286694, 0.001510256168281658, 0.0012896325307049882, 0.001218444426710108], "type": "data", "visible": true}, "marker": {"size": 12, "symbol": "star"}, "name": "Time", "type": "scatter", "x": [1, 2, 4, 8, 16, 32, 64, 128], "xaxis": "x", "y": [0.028156291123311916, 0.01728601553311333, 0.011362970001527603, 0.008698523864255132, 0.00775169205497688, 0.006921940354503548, 0.006502017933948367, 0.006592512766704383], "yaxis": "y"}, {"error_y": {"array": [5.800258135139271, 4.154069059070635, 3.0990891079110425, 2.3478156483136976, 1.9802106738241176, 1.4667302779393956, 1.3583250352936223, 1.4001488541791103], "type": "data", "visible": true}, "marker": {"size": 10, "symbol": "square"}, "name": "Memory", "type": "scatter", "x": [1, 2, 4, 8, 16, 32, 64, 128], "xaxis": "x", "y": [12.603112410944396, 12.579492552064947, 12.66682982074806, 12.659586888575458, 12.751314569014083, 12.672998281559813, 12.600580789542482, 12.661068301790282], "yaxis": "y2"}], "layout": {"autosize": false, "font": {"size": 18}, "height": 800, "shapes": [{"line": {"color": "Blue", "dash": "dot"}, "type": "line", "x0": 1, "x1": 128, "y0": 19.69, "y1": 19.69}], "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "width": 1000, "xaxis": {"anchor": "y", "domain": [0, 0.94], "tickvals": [1, 2, 4, 6, 8, 10, 20, 40, 60, 80, 100, 200], "title": {"text": "Batch size (log)"}, "type": "log"}, "yaxis": {"anchor": "x", "color": "blue", "domain": [0, 1], "range": [-2.5, 1.5], "tickvals": [0.005, 0.01, 0.02, 0.05, 0.1, 0.2, 0.5, 1, 2, 5, 10, 20], "title": {"text": "Mean time per protein [s] (log)"}, "type": "log"}, "yaxis2": {"anchor": "x", "color": "red", "overlaying": "y", "side": "right", "title": {"text": "Mean GPU memory usage per protein [MB]"}}}}, "text/html": "<div>                            <div id=\"ee10df92-60c7-45c4-8ef5-7297d9c9fa98\" class=\"plotly-graph-div\" style=\"height:800px; width:1000px;\"></div>            <script type=\"text/javascript\">                require([\"plotly\"], function(Plotly) {                    window.PLOTLYENV=window.PLOTLYENV || {};                                    if (document.getElementById(\"ee10df92-60c7-45c4-8ef5-7297d9c9fa98\")) {                    Plotly.newPlot(                        \"ee10df92-60c7-45c4-8ef5-7297d9c9fa98\",                        [{\"error_y\": {\"array\": [0.007938229059877963, 0.005599115237226402, 0.003436053099386169, 0.0026162141417493227, 0.0024595882701286694, 0.001510256168281658, 0.0012896325307049882, 0.001218444426710108], \"type\": \"data\", \"visible\": true}, \"marker\": {\"size\": 12, \"symbol\": \"star\"}, \"name\": \"Time\", \"type\": \"scatter\", \"x\": [1, 2, 4, 8, 16, 32, 64, 128], \"xaxis\": \"x\", \"y\": [0.028156291123311916, 0.01728601553311333, 0.011362970001527603, 0.008698523864255132, 0.00775169205497688, 0.006921940354503548, 0.006502017933948367, 0.006592512766704383], \"yaxis\": \"y\"}, {\"error_y\": {\"array\": [5.800258135139271, 4.154069059070635, 3.0990891079110425, 2.3478156483136976, 1.9802106738241176, 1.4667302779393956, 1.3583250352936223, 1.4001488541791103], \"type\": \"data\", \"visible\": true}, \"marker\": {\"size\": 10, \"symbol\": \"square\"}, \"name\": \"Memory\", \"type\": \"scatter\", \"x\": [1, 2, 4, 8, 16, 32, 64, 128], \"xaxis\": \"x\", \"y\": [12.603112410944396, 12.579492552064947, 12.66682982074806, 12.659586888575458, 12.751314569014083, 12.672998281559813, 12.600580789542482, 12.661068301790282], \"yaxis\": \"y2\"}],                        {\"autosize\": false, \"font\": {\"size\": 18}, \"height\": 800, \"shapes\": [{\"line\": {\"color\": \"Blue\", \"dash\": \"dot\"}, \"type\": \"line\", \"x0\": 1, \"x1\": 128, \"y0\": 19.69, \"y1\": 19.69}], \"template\": {\"data\": {\"bar\": [{\"error_x\": {\"color\": \"#2a3f5f\"}, \"error_y\": {\"color\": \"#2a3f5f\"}, \"marker\": {\"line\": {\"color\": \"#E5ECF6\", \"width\": 0.5}}, \"type\": \"bar\"}], \"barpolar\": [{\"marker\": {\"line\": {\"color\": \"#E5ECF6\", \"width\": 0.5}}, \"type\": \"barpolar\"}], \"carpet\": [{\"aaxis\": {\"endlinecolor\": \"#2a3f5f\", \"gridcolor\": \"white\", \"linecolor\": \"white\", \"minorgridcolor\": \"white\", \"startlinecolor\": \"#2a3f5f\"}, \"baxis\": {\"endlinecolor\": \"#2a3f5f\", \"gridcolor\": \"white\", \"linecolor\": \"white\", \"minorgridcolor\": \"white\", \"startlinecolor\": \"#2a3f5f\"}, \"type\": \"carpet\"}], \"choropleth\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"type\": \"choropleth\"}], \"contour\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"colorscale\": [[0.0, \"#0d0887\"], [0.1111111111111111, \"#46039f\"], [0.2222222222222222, \"#7201a8\"], [0.3333333333333333, \"#9c179e\"], [0.4444444444444444, \"#bd3786\"], [0.5555555555555556, \"#d8576b\"], [0.6666666666666666, \"#ed7953\"], [0.7777777777777778, \"#fb9f3a\"], [0.8888888888888888, \"#fdca26\"], [1.0, \"#f0f921\"]], \"type\": \"contour\"}], \"contourcarpet\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"type\": \"contourcarpet\"}], \"heatmap\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"colorscale\": [[0.0, \"#0d0887\"], [0.1111111111111111, \"#46039f\"], [0.2222222222222222, \"#7201a8\"], [0.3333333333333333, \"#9c179e\"], [0.4444444444444444, \"#bd3786\"], [0.5555555555555556, \"#d8576b\"], [0.6666666666666666, \"#ed7953\"], [0.7777777777777778, \"#fb9f3a\"], [0.8888888888888888, \"#fdca26\"], [1.0, \"#f0f921\"]], \"type\": \"heatmap\"}], \"heatmapgl\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"colorscale\": [[0.0, \"#0d0887\"], [0.1111111111111111, \"#46039f\"], [0.2222222222222222, \"#7201a8\"], [0.3333333333333333, \"#9c179e\"], [0.4444444444444444, \"#bd3786\"], [0.5555555555555556, \"#d8576b\"], [0.6666666666666666, \"#ed7953\"], [0.7777777777777778, \"#fb9f3a\"], [0.8888888888888888, \"#fdca26\"], [1.0, \"#f0f921\"]], \"type\": \"heatmapgl\"}], \"histogram\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"histogram\"}], \"histogram2d\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"colorscale\": [[0.0, \"#0d0887\"], [0.1111111111111111, \"#46039f\"], [0.2222222222222222, \"#7201a8\"], [0.3333333333333333, \"#9c179e\"], [0.4444444444444444, \"#bd3786\"], [0.5555555555555556, \"#d8576b\"], [0.6666666666666666, \"#ed7953\"], [0.7777777777777778, \"#fb9f3a\"], [0.8888888888888888, \"#fdca26\"], [1.0, \"#f0f921\"]], \"type\": \"histogram2d\"}], \"histogram2dcontour\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"colorscale\": [[0.0, \"#0d0887\"], [0.1111111111111111, \"#46039f\"], [0.2222222222222222, \"#7201a8\"], [0.3333333333333333, \"#9c179e\"], [0.4444444444444444, \"#bd3786\"], [0.5555555555555556, \"#d8576b\"], [0.6666666666666666, \"#ed7953\"], [0.7777777777777778, \"#fb9f3a\"], [0.8888888888888888, \"#fdca26\"], [1.0, \"#f0f921\"]], \"type\": \"histogram2dcontour\"}], \"mesh3d\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"type\": \"mesh3d\"}], \"parcoords\": [{\"line\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"parcoords\"}], \"pie\": [{\"automargin\": true, \"type\": \"pie\"}], \"scatter\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scatter\"}], \"scatter3d\": [{\"line\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scatter3d\"}], \"scattercarpet\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scattercarpet\"}], \"scattergeo\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scattergeo\"}], \"scattergl\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scattergl\"}], \"scattermapbox\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scattermapbox\"}], \"scatterpolar\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scatterpolar\"}], \"scatterpolargl\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scatterpolargl\"}], \"scatterternary\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scatterternary\"}], \"surface\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"colorscale\": [[0.0, \"#0d0887\"], [0.1111111111111111, \"#46039f\"], [0.2222222222222222, \"#7201a8\"], [0.3333333333333333, \"#9c179e\"], [0.4444444444444444, \"#bd3786\"], [0.5555555555555556, \"#d8576b\"], [0.6666666666666666, \"#ed7953\"], [0.7777777777777778, \"#fb9f3a\"], [0.8888888888888888, \"#fdca26\"], [1.0, \"#f0f921\"]], \"type\": \"surface\"}], \"table\": [{\"cells\": {\"fill\": {\"color\": \"#EBF0F8\"}, \"line\": {\"color\": \"white\"}}, \"header\": {\"fill\": {\"color\": \"#C8D4E3\"}, \"line\": {\"color\": \"white\"}}, \"type\": \"table\"}]}, \"layout\": {\"annotationdefaults\": {\"arrowcolor\": \"#2a3f5f\", \"arrowhead\": 0, \"arrowwidth\": 1}, \"coloraxis\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"colorscale\": {\"diverging\": [[0, \"#8e0152\"], [0.1, \"#c51b7d\"], [0.2, \"#de77ae\"], [0.3, \"#f1b6da\"], [0.4, \"#fde0ef\"], [0.5, \"#f7f7f7\"], [0.6, \"#e6f5d0\"], [0.7, \"#b8e186\"], [0.8, \"#7fbc41\"], [0.9, \"#4d9221\"], [1, \"#276419\"]], \"sequential\": [[0.0, \"#0d0887\"], [0.1111111111111111, \"#46039f\"], [0.2222222222222222, \"#7201a8\"], [0.3333333333333333, \"#9c179e\"], [0.4444444444444444, \"#bd3786\"], [0.5555555555555556, \"#d8576b\"], [0.6666666666666666, \"#ed7953\"], [0.7777777777777778, \"#fb9f3a\"], [0.8888888888888888, \"#fdca26\"], [1.0, \"#f0f921\"]], \"sequentialminus\": [[0.0, \"#0d0887\"], [0.1111111111111111, \"#46039f\"], [0.2222222222222222, \"#7201a8\"], [0.3333333333333333, \"#9c179e\"], [0.4444444444444444, \"#bd3786\"], [0.5555555555555556, \"#d8576b\"], [0.6666666666666666, \"#ed7953\"], [0.7777777777777778, \"#fb9f3a\"], [0.8888888888888888, \"#fdca26\"], [1.0, \"#f0f921\"]]}, \"colorway\": [\"#636efa\", \"#EF553B\", \"#00cc96\", \"#ab63fa\", \"#FFA15A\", \"#19d3f3\", \"#FF6692\", \"#B6E880\", \"#FF97FF\", \"#FECB52\"], \"font\": {\"color\": \"#2a3f5f\"}, \"geo\": {\"bgcolor\": \"white\", \"lakecolor\": \"white\", \"landcolor\": \"#E5ECF6\", \"showlakes\": true, \"showland\": true, \"subunitcolor\": \"white\"}, \"hoverlabel\": {\"align\": \"left\"}, \"hovermode\": \"closest\", \"mapbox\": {\"style\": \"light\"}, \"paper_bgcolor\": \"white\", \"plot_bgcolor\": \"#E5ECF6\", \"polar\": {\"angularaxis\": {\"gridcolor\": \"white\", \"linecolor\": \"white\", \"ticks\": \"\"}, \"bgcolor\": \"#E5ECF6\", \"radialaxis\": {\"gridcolor\": \"white\", \"linecolor\": \"white\", \"ticks\": \"\"}}, \"scene\": {\"xaxis\": {\"backgroundcolor\": \"#E5ECF6\", \"gridcolor\": \"white\", \"gridwidth\": 2, \"linecolor\": \"white\", \"showbackground\": true, \"ticks\": \"\", \"zerolinecolor\": \"white\"}, \"yaxis\": {\"backgroundcolor\": \"#E5ECF6\", \"gridcolor\": \"white\", \"gridwidth\": 2, \"linecolor\": \"white\", \"showbackground\": true, \"ticks\": \"\", \"zerolinecolor\": \"white\"}, \"zaxis\": {\"backgroundcolor\": \"#E5ECF6\", \"gridcolor\": \"white\", \"gridwidth\": 2, \"linecolor\": \"white\", \"showbackground\": true, \"ticks\": \"\", \"zerolinecolor\": \"white\"}}, \"shapedefaults\": {\"line\": {\"color\": \"#2a3f5f\"}}, \"ternary\": {\"aaxis\": {\"gridcolor\": \"white\", \"linecolor\": \"white\", \"ticks\": \"\"}, \"baxis\": {\"gridcolor\": \"white\", \"linecolor\": \"white\", \"ticks\": \"\"}, \"bgcolor\": \"#E5ECF6\", \"caxis\": {\"gridcolor\": \"white\", \"linecolor\": \"white\", \"ticks\": \"\"}}, \"title\": {\"x\": 0.05}, \"xaxis\": {\"automargin\": true, \"gridcolor\": \"white\", \"linecolor\": \"white\", \"ticks\": \"\", \"title\": {\"standoff\": 15}, \"zerolinecolor\": \"white\", \"zerolinewidth\": 2}, \"yaxis\": {\"automargin\": true, \"gridcolor\": \"white\", \"linecolor\": \"white\", \"ticks\": \"\", \"title\": {\"standoff\": 15}, \"zerolinecolor\": \"white\", \"zerolinewidth\": 2}}}, \"width\": 1000, \"xaxis\": {\"anchor\": \"y\", \"domain\": [0.0, 0.94], \"tickvals\": [1, 2, 4, 6, 8, 10, 20, 40, 60, 80, 100, 200], \"title\": {\"text\": \"Batch size (log)\"}, \"type\": \"log\"}, \"yaxis\": {\"anchor\": \"x\", \"color\": \"blue\", \"domain\": [0.0, 1.0], \"range\": [-2.5, 1.5], \"tickvals\": [0.005, 0.01, 0.02, 0.05, 0.1, 0.2, 0.5, 1, 2, 5, 10, 20], \"title\": {\"text\": \"Mean time per protein [s] (log)\"}, \"type\": \"log\"}, \"yaxis2\": {\"anchor\": \"x\", \"color\": \"red\", \"overlaying\": \"y\", \"side\": \"right\", \"title\": {\"text\": \"Mean GPU memory usage per protein [MB]\"}}},                        {\"responsive\": true}                    ).then(function(){\n                            \nvar gd = document.getElementById('ee10df92-60c7-45c4-8ef5-7297d9c9fa98');\nvar x = new MutationObserver(function (mutations, observer) {{\n        var display = window.getComputedStyle(gd).display;\n        if (!display || display === 'none') {{\n            console.log([gd, 'removed!']);\n            Plotly.purge(gd);\n            observer.disconnect();\n        }}\n}});\n\n// Listen for the removal of the full notebook cells\nvar notebookContainer = gd.closest('#notebook-container');\nif (notebookContainer) {{\n    x.observe(notebookContainer, {childList: true});\n}}\n\n// Listen for the clearing of the current output cell\nvar outputEl = gd.closest('.output');\nif (outputEl) {{\n    x.observe(outputEl, {childList: true});\n}}\n\n                        })                };                });            </script>        </div>"}, "metadata": {}}, {"output_type": "display_data", "data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"error_y": {"array": [0.0006948794730123882, 0.00041587214364136103, 0.00036377932091529743, 0.0002134561690764115, 0.0002536135187275087, 0.00015024880768163085, 9.095317204787343e-05, 8.709739761340829e-05], "type": "data", "visible": true}, "marker": {"size": 12, "symbol": "star"}, "name": "Time", "type": "scatter", "x": [1, 2, 4, 8, 16, 32, 64, 128], "xaxis": "x", "y": [0.00391388593894238, 0.002163549004786014, 0.001249552614350679, 0.0008347560723846823, 0.0006608473373131013, 0.0005315618241120866, 0.0004603021869472429, 0.00045832309781399834], "yaxis": "y"}, {"error_y": {"array": [1.8445861098663745, 1.326871259668138, 1.0156415521208961, 0.7785751314064158, 0.7269693020078153, 0.4891041190314188, 0.4796491370767668, 0.6993207818661091], "type": "data", "visible": true}, "marker": {"size": 10, "symbol": "square"}, "name": "Memory", "type": "scatter", "x": [1, 2, 4, 8, 16, 32, 64, 128], "xaxis": "x", "y": [4.3401819735216245, 4.314389777620896, 4.388101194071983, 4.390260084626234, 4.443850952112675, 4.402911825512227, 4.33979808366013, 4.359717557544757], "yaxis": "y2"}], "layout": {"autosize": false, "font": {"size": 18}, "height": 800, "shapes": [{"line": {"color": "Blue", "dash": "dot"}, "type": "line", "x0": 1, "x1": 128, "y0": 50.65, "y1": 50.65}], "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "width": 1000, "xaxis": {"anchor": "y", "domain": [0, 0.94], "tickvals": [1, 2, 4, 6, 8, 10, 20, 40, 60, 80, 100, 200], "title": {"text": "Batch size (log)"}, "type": "log"}, "yaxis": {"anchor": "x", "color": "blue", "domain": [0, 1], "range": [-3.5, 2], "tickvals": [0.0005, 0.001, 0.002, 0.005, 0.01, 0.02, 0.05, 0.1, 0.2, 0.5, 1, 2, 5, 10, 20, 50], "title": {"text": "Mean time per protein [s] (log)"}, "type": "log"}, "yaxis2": {"anchor": "x", "color": "red", "overlaying": "y", "side": "right", "title": {"text": "Mean GPU memory usage per protein [MB]"}}}}, "text/html": "<div>                            <div id=\"4ce742e6-c745-4371-9036-d80b41a47345\" class=\"plotly-graph-div\" style=\"height:800px; width:1000px;\"></div>            <script type=\"text/javascript\">                require([\"plotly\"], function(Plotly) {                    window.PLOTLYENV=window.PLOTLYENV || {};                                    if (document.getElementById(\"4ce742e6-c745-4371-9036-d80b41a47345\")) {                    Plotly.newPlot(                        \"4ce742e6-c745-4371-9036-d80b41a47345\",                        [{\"error_y\": {\"array\": [0.0006948794730123882, 0.00041587214364136103, 0.00036377932091529743, 0.0002134561690764115, 0.0002536135187275087, 0.00015024880768163085, 9.095317204787343e-05, 8.709739761340829e-05], \"type\": \"data\", \"visible\": true}, \"marker\": {\"size\": 12, \"symbol\": \"star\"}, \"name\": \"Time\", \"type\": \"scatter\", \"x\": [1, 2, 4, 8, 16, 32, 64, 128], \"xaxis\": \"x\", \"y\": [0.00391388593894238, 0.002163549004786014, 0.001249552614350679, 0.0008347560723846823, 0.0006608473373131013, 0.0005315618241120866, 0.0004603021869472429, 0.00045832309781399834], \"yaxis\": \"y\"}, {\"error_y\": {\"array\": [1.8445861098663745, 1.326871259668138, 1.0156415521208961, 0.7785751314064158, 0.7269693020078153, 0.4891041190314188, 0.4796491370767668, 0.6993207818661091], \"type\": \"data\", \"visible\": true}, \"marker\": {\"size\": 10, \"symbol\": \"square\"}, \"name\": \"Memory\", \"type\": \"scatter\", \"x\": [1, 2, 4, 8, 16, 32, 64, 128], \"xaxis\": \"x\", \"y\": [4.3401819735216245, 4.314389777620896, 4.388101194071983, 4.390260084626234, 4.443850952112675, 4.402911825512227, 4.33979808366013, 4.359717557544757], \"yaxis\": \"y2\"}],                        {\"autosize\": false, \"font\": {\"size\": 18}, \"height\": 800, \"shapes\": [{\"line\": {\"color\": \"Blue\", \"dash\": \"dot\"}, \"type\": \"line\", \"x0\": 1, \"x1\": 128, \"y0\": 50.65, \"y1\": 50.65}], \"template\": {\"data\": {\"bar\": [{\"error_x\": {\"color\": \"#2a3f5f\"}, \"error_y\": {\"color\": \"#2a3f5f\"}, \"marker\": {\"line\": {\"color\": \"#E5ECF6\", \"width\": 0.5}}, \"type\": \"bar\"}], \"barpolar\": [{\"marker\": {\"line\": {\"color\": \"#E5ECF6\", \"width\": 0.5}}, \"type\": \"barpolar\"}], \"carpet\": [{\"aaxis\": {\"endlinecolor\": \"#2a3f5f\", \"gridcolor\": \"white\", \"linecolor\": \"white\", \"minorgridcolor\": \"white\", \"startlinecolor\": \"#2a3f5f\"}, \"baxis\": {\"endlinecolor\": \"#2a3f5f\", \"gridcolor\": \"white\", \"linecolor\": \"white\", \"minorgridcolor\": \"white\", \"startlinecolor\": \"#2a3f5f\"}, \"type\": \"carpet\"}], \"choropleth\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"type\": \"choropleth\"}], \"contour\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"colorscale\": [[0.0, \"#0d0887\"], [0.1111111111111111, \"#46039f\"], [0.2222222222222222, \"#7201a8\"], [0.3333333333333333, \"#9c179e\"], [0.4444444444444444, \"#bd3786\"], [0.5555555555555556, \"#d8576b\"], [0.6666666666666666, \"#ed7953\"], [0.7777777777777778, \"#fb9f3a\"], [0.8888888888888888, \"#fdca26\"], [1.0, \"#f0f921\"]], \"type\": \"contour\"}], \"contourcarpet\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"type\": \"contourcarpet\"}], \"heatmap\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"colorscale\": [[0.0, \"#0d0887\"], [0.1111111111111111, \"#46039f\"], [0.2222222222222222, \"#7201a8\"], [0.3333333333333333, \"#9c179e\"], [0.4444444444444444, \"#bd3786\"], [0.5555555555555556, \"#d8576b\"], [0.6666666666666666, \"#ed7953\"], [0.7777777777777778, \"#fb9f3a\"], [0.8888888888888888, \"#fdca26\"], [1.0, \"#f0f921\"]], \"type\": \"heatmap\"}], \"heatmapgl\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"colorscale\": [[0.0, \"#0d0887\"], [0.1111111111111111, \"#46039f\"], [0.2222222222222222, \"#7201a8\"], [0.3333333333333333, \"#9c179e\"], [0.4444444444444444, \"#bd3786\"], [0.5555555555555556, \"#d8576b\"], [0.6666666666666666, \"#ed7953\"], [0.7777777777777778, \"#fb9f3a\"], [0.8888888888888888, \"#fdca26\"], [1.0, \"#f0f921\"]], \"type\": \"heatmapgl\"}], \"histogram\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"histogram\"}], \"histogram2d\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"colorscale\": [[0.0, \"#0d0887\"], [0.1111111111111111, \"#46039f\"], [0.2222222222222222, \"#7201a8\"], [0.3333333333333333, \"#9c179e\"], [0.4444444444444444, \"#bd3786\"], [0.5555555555555556, \"#d8576b\"], [0.6666666666666666, \"#ed7953\"], [0.7777777777777778, \"#fb9f3a\"], [0.8888888888888888, \"#fdca26\"], [1.0, \"#f0f921\"]], \"type\": \"histogram2d\"}], \"histogram2dcontour\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"colorscale\": [[0.0, \"#0d0887\"], [0.1111111111111111, \"#46039f\"], [0.2222222222222222, \"#7201a8\"], [0.3333333333333333, \"#9c179e\"], [0.4444444444444444, \"#bd3786\"], [0.5555555555555556, \"#d8576b\"], [0.6666666666666666, \"#ed7953\"], [0.7777777777777778, \"#fb9f3a\"], [0.8888888888888888, \"#fdca26\"], [1.0, \"#f0f921\"]], \"type\": \"histogram2dcontour\"}], \"mesh3d\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"type\": \"mesh3d\"}], \"parcoords\": [{\"line\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"parcoords\"}], \"pie\": [{\"automargin\": true, \"type\": \"pie\"}], \"scatter\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scatter\"}], \"scatter3d\": [{\"line\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scatter3d\"}], \"scattercarpet\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scattercarpet\"}], \"scattergeo\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scattergeo\"}], \"scattergl\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scattergl\"}], \"scattermapbox\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scattermapbox\"}], \"scatterpolar\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scatterpolar\"}], \"scatterpolargl\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scatterpolargl\"}], \"scatterternary\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scatterternary\"}], \"surface\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"colorscale\": [[0.0, \"#0d0887\"], [0.1111111111111111, \"#46039f\"], [0.2222222222222222, \"#7201a8\"], [0.3333333333333333, \"#9c179e\"], [0.4444444444444444, \"#bd3786\"], [0.5555555555555556, \"#d8576b\"], [0.6666666666666666, \"#ed7953\"], [0.7777777777777778, \"#fb9f3a\"], [0.8888888888888888, \"#fdca26\"], [1.0, \"#f0f921\"]], \"type\": \"surface\"}], \"table\": [{\"cells\": {\"fill\": {\"color\": \"#EBF0F8\"}, \"line\": {\"color\": \"white\"}}, \"header\": {\"fill\": {\"color\": \"#C8D4E3\"}, \"line\": {\"color\": \"white\"}}, \"type\": \"table\"}]}, \"layout\": {\"annotationdefaults\": {\"arrowcolor\": \"#2a3f5f\", \"arrowhead\": 0, \"arrowwidth\": 1}, \"coloraxis\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"colorscale\": {\"diverging\": [[0, \"#8e0152\"], [0.1, \"#c51b7d\"], [0.2, \"#de77ae\"], [0.3, \"#f1b6da\"], [0.4, \"#fde0ef\"], [0.5, \"#f7f7f7\"], [0.6, \"#e6f5d0\"], [0.7, \"#b8e186\"], [0.8, \"#7fbc41\"], [0.9, \"#4d9221\"], [1, \"#276419\"]], \"sequential\": [[0.0, \"#0d0887\"], [0.1111111111111111, \"#46039f\"], [0.2222222222222222, \"#7201a8\"], [0.3333333333333333, \"#9c179e\"], [0.4444444444444444, \"#bd3786\"], [0.5555555555555556, \"#d8576b\"], [0.6666666666666666, \"#ed7953\"], [0.7777777777777778, \"#fb9f3a\"], [0.8888888888888888, \"#fdca26\"], [1.0, \"#f0f921\"]], \"sequentialminus\": [[0.0, \"#0d0887\"], [0.1111111111111111, \"#46039f\"], [0.2222222222222222, \"#7201a8\"], [0.3333333333333333, \"#9c179e\"], [0.4444444444444444, \"#bd3786\"], [0.5555555555555556, \"#d8576b\"], [0.6666666666666666, \"#ed7953\"], [0.7777777777777778, \"#fb9f3a\"], [0.8888888888888888, \"#fdca26\"], [1.0, \"#f0f921\"]]}, \"colorway\": [\"#636efa\", \"#EF553B\", \"#00cc96\", \"#ab63fa\", \"#FFA15A\", \"#19d3f3\", \"#FF6692\", \"#B6E880\", \"#FF97FF\", \"#FECB52\"], \"font\": {\"color\": \"#2a3f5f\"}, \"geo\": {\"bgcolor\": \"white\", \"lakecolor\": \"white\", \"landcolor\": \"#E5ECF6\", \"showlakes\": true, \"showland\": true, \"subunitcolor\": \"white\"}, \"hoverlabel\": {\"align\": \"left\"}, \"hovermode\": \"closest\", \"mapbox\": {\"style\": \"light\"}, \"paper_bgcolor\": \"white\", \"plot_bgcolor\": \"#E5ECF6\", \"polar\": {\"angularaxis\": {\"gridcolor\": \"white\", \"linecolor\": \"white\", \"ticks\": \"\"}, \"bgcolor\": \"#E5ECF6\", \"radialaxis\": {\"gridcolor\": \"white\", \"linecolor\": \"white\", \"ticks\": \"\"}}, \"scene\": {\"xaxis\": {\"backgroundcolor\": \"#E5ECF6\", \"gridcolor\": \"white\", \"gridwidth\": 2, \"linecolor\": \"white\", \"showbackground\": true, \"ticks\": \"\", \"zerolinecolor\": \"white\"}, \"yaxis\": {\"backgroundcolor\": \"#E5ECF6\", \"gridcolor\": \"white\", \"gridwidth\": 2, \"linecolor\": \"white\", \"showbackground\": true, \"ticks\": \"\", \"zerolinecolor\": \"white\"}, \"zaxis\": {\"backgroundcolor\": \"#E5ECF6\", \"gridcolor\": \"white\", \"gridwidth\": 2, \"linecolor\": \"white\", \"showbackground\": true, \"ticks\": \"\", \"zerolinecolor\": \"white\"}}, \"shapedefaults\": {\"line\": {\"color\": \"#2a3f5f\"}}, \"ternary\": {\"aaxis\": {\"gridcolor\": \"white\", \"linecolor\": \"white\", \"ticks\": \"\"}, \"baxis\": {\"gridcolor\": \"white\", \"linecolor\": \"white\", \"ticks\": \"\"}, \"bgcolor\": \"#E5ECF6\", \"caxis\": {\"gridcolor\": \"white\", \"linecolor\": \"white\", \"ticks\": \"\"}}, \"title\": {\"x\": 0.05}, \"xaxis\": {\"automargin\": true, \"gridcolor\": \"white\", \"linecolor\": \"white\", \"ticks\": \"\", \"title\": {\"standoff\": 15}, \"zerolinecolor\": \"white\", \"zerolinewidth\": 2}, \"yaxis\": {\"automargin\": true, \"gridcolor\": \"white\", \"linecolor\": \"white\", \"ticks\": \"\", \"title\": {\"standoff\": 15}, \"zerolinecolor\": \"white\", \"zerolinewidth\": 2}}}, \"width\": 1000, \"xaxis\": {\"anchor\": \"y\", \"domain\": [0.0, 0.94], \"tickvals\": [1, 2, 4, 6, 8, 10, 20, 40, 60, 80, 100, 200], \"title\": {\"text\": \"Batch size (log)\"}, \"type\": \"log\"}, \"yaxis\": {\"anchor\": \"x\", \"color\": \"blue\", \"domain\": [0.0, 1.0], \"range\": [-3.5, 2], \"tickvals\": [0.0005, 0.001, 0.002, 0.005, 0.01, 0.02, 0.05, 0.1, 0.2, 0.5, 1, 2, 5, 10, 20, 50], \"title\": {\"text\": \"Mean time per protein [s] (log)\"}, \"type\": \"log\"}, \"yaxis2\": {\"anchor\": \"x\", \"color\": \"red\", \"overlaying\": \"y\", \"side\": \"right\", \"title\": {\"text\": \"Mean GPU memory usage per protein [MB]\"}}},                        {\"responsive\": true}                    ).then(function(){\n                            \nvar gd = document.getElementById('4ce742e6-c745-4371-9036-d80b41a47345');\nvar x = new MutationObserver(function (mutations, observer) {{\n        var display = window.getComputedStyle(gd).display;\n        if (!display || display === 'none') {{\n            console.log([gd, 'removed!']);\n            Plotly.purge(gd);\n            observer.disconnect();\n        }}\n}});\n\n// Listen for the removal of the full notebook cells\nvar notebookContainer = gd.closest('#notebook-container');\nif (notebookContainer) {{\n    x.observe(notebookContainer, {childList: true});\n}}\n\n// Listen for the clearing of the current output cell\nvar outputEl = gd.closest('.output');\nif (outputEl) {{\n    x.observe(outputEl, {childList: true});\n}}\n\n                        })                };                });            </script>        </div>"}, "metadata": {}}], "source": ["keys = ['surface', 'features', 'coordinates']\n", "for key in keys:\n", "    batch_sizes = [1,2,4,8,16,32,64,128]\n", "    resolution = 1.0\n", "    sup_sampling = 20\n", "    N = len(batch_sizes)\n", "\n", "    time_means = []\n", "    time_std = []\n", "    memory_means = []\n", "    memory_std = []\n", "    for i in range(N):\n", "        times = np.load(f'../timings/step_profiling_{key}_times_batch_{batch_sizes[i]}_res_{resolution}_B_{sup_sampling}.npy')\n", "        memory = np.load(f'../timings/step_profiling_{key}_memory_batch_{batch_sizes[i]}_res_{resolution}_B_{sup_sampling}.npy')\n", "        memory = memory*1e-6\n", "        time_means.append(times.mean())\n", "        memory_means.append(memory.mean())\n", "        time_std.append(times.std())\n", "        memory_std.append(memory.std())\n", "\n", "\n", "    fig = make_subplots(specs=[[{\"secondary_y\": True}]])\n", "    fig.add_trace(\n", "        go.Scatter(x=batch_sizes, y=time_means, name=\"Time\", marker_size=12, marker_symbol='star', error_y=dict(type='data',array=time_std,visible=True)),\n", "        secondary_y=False,\n", "    )\n", "\n", "    fig.add_trace(\n", "        go.Scatter(x=batch_sizes, y=memory_means, name=\"Memory\", marker_symbol='square', marker_size=10,error_y=dict(type='data',array=memory_std,visible=True)),\n", "        secondary_y=True,\n", "    )\n", "\n", "    if key=='surface':\n", "        fig.add_shape(type='line',\n", "                x0=1,\n", "                y0=6.11,\n", "                x1=128,\n", "                y1=6.11,\n", "                line=dict(color='Blue',dash = 'dot'))\n", "    elif key=='features':\n", "        fig.add_shape(type='line',\n", "                x0=1,\n", "                y0=19.69,\n", "                x1=128,\n", "                y1=19.69,\n", "                line=dict(color='Blue',dash = 'dot'))\n", "    elif key=='coordinates':\n", "        fig.add_shape(type='line',\n", "                x0=1,\n", "                y0=50.65,\n", "                x1=128,\n", "                y1=50.65,\n", "                line=dict(color='Blue',dash = 'dot'))\n", "\n", "\n", "\n", "    fig.update_xaxes(title_text=\"Batch size (log)\",tickvals = [1,2,4,6,8,10,20,40,60,80,100,200])\n", "    fig.update_yaxes(title_text=\"Mean time per protein [s] (log)\", secondary_y=False,color='blue')\n", "    fig.update_yaxes(title_text=\"Mean GPU memory usage per protein [MB]\", secondary_y=True,color='red')\n", "\n", "    fig.update_xaxes(type=\"log\")\n", "    fig.update_yaxes(secondary_y=False,type=\"log\")\n", "    if key == 'surface':\n", "        fig.update_yaxes(secondary_y=False,tickvals = [0.04,0.06,0.08,0.1,0.2,0.4,0.6,0.8,1,2,4,6,8,10])\n", "        fig.update_yaxes(secondary_y=False,range=[-1.5,1])\n", "    elif key=='features':\n", "        fig.update_yaxes(secondary_y=False,tickvals = [0.005,0.01,0.02,0.05, 0.1, 0.2, 0.5, 1, 2, 5, 10, 20])\n", "        fig.update_yaxes(secondary_y=False,range=[-2.5,1.5]) \n", "    elif key == 'coordinates':\n", "        #fig.update_yaxes(secondary_y=False,tickvals = [4e-4,6e-4,8e-4,10e-4,2e-3,4e-3])\n", "        fig.update_yaxes(secondary_y=False,tickvals = [0.0005,0.001,0.002,0.005,0.01,0.02,0.05, 0.1, 0.2, 0.5, 1, 2, 5, 10, 20,50])\n", "        fig.update_yaxes(secondary_y=False,range=[-3.5,2]) \n", "\n", "    #fig.update_yaxes(secondary_y=False,range=[-1.5,1])\n", "\n", "\n", "\n", "    fig.update_layout(\n", "        autosize=False,\n", "        width=1000,\n", "        height=800,\n", "        font=dict(\n", "            size=18,\n", "        ))\n", "\n", "\n", "\n", "    fig.show()\n", "    fig.write_image(f'../figures/batchsize_profiling_{key}_izar.pdf')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}]}