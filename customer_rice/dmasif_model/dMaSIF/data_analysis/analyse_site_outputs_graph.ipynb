{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "from pathlib import Path\n", "import matplotlib.pyplot as plt\n", "import plotly.graph_objects as go\n", "from tqdm import tqdm\n", "from scipy.spatial.distance import cdist\n", "from sklearn.metrics import roc_curve, roc_auc_score"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["timings = Path('timings/')\n", "raw_data = Path('surface_data/raw/protein_surfaces/01-benchmark_surfaces_npy')\n", "\n", "experiment_names = ['TangentConv_site_1layer_5A_epoch49', 'TangentConv_site_1layer_9A_epoch49', 'TangentConv_site_1layer_15A_epoch49','TangentConv_site_3layer_5A_epoch49','TangentConv_site_3layer_9A_epoch46', 'TangentConv_site_3layer_15A_epoch17','PointNet_site_1layer_5A_epoch30','PointNet_site_1layer_9A_epoch30','PointNet_site_3layer_5A_epoch46', 'PointNet_site_3layer_9A_epoch37', 'DGCNN_site_1layer_k40_epoch46','DGCNN_site_1layer_k100_epoch32','DGCNN_site_3layer_k40_epoch33']\n", "\n", "experiment_names_short = ['Ours 1L 5A', 'Ours 1L 9A', 'Ours 1L 15A','Ours 3L 5A','Ours 3L 9A', 'Ours 3L 15A','PN++ 1L 5A','PN++ 1L 9A','PN++ 3L 5A', 'PN++ 3L 9A', 'DGCNN 1L K40','DGCNN 1L K100','DGCNN 3L K40']"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["performance = []\n", "times = []\n", "time_errors = []\n", "memory = []\n", "memory_errors = []\n", "for experiment_name in experiment_names:\n", "\n", "    predpoints_preds = np.load(timings/f'{experiment_name}_predpoints_preds.npy')\n", "    predpoints_labels = np.load(timings/f'{experiment_name}_predpoints_labels.npy')\n", "\n", "    rocauc = roc_auc_score(predpoints_labels,predpoints_preds)\n", "    conv_times = np.load(timings/f'{experiment_name}_convtime.npy')\n", "    memoryusage = np.load(timings/f'{experiment_name}_memoryusage.npy')\n", "    memoryusage = memoryusage\n", "    conv_times = conv_times\n", "\n", "    performance.append(rocauc)\n", "    times.append(conv_times.mean())\n", "    time_errors.append(conv_times.std())\n", "    memory.append(memoryusage.mean())\n", "    memory_errors.append(memoryusage.std())\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'colors' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "\u001b[0;32m<ipython-input-4-86a51af4ab21>\u001b[0m in \u001b[0;36m<module>\u001b[0;34m\u001b[0m\n\u001b[1;32m      5\u001b[0m \u001b[0mmemory_errors\u001b[0m \u001b[0;34m+=\u001b[0m \u001b[0;34m[\u001b[0m\u001b[0;36m125881554.73354617\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      6\u001b[0m \u001b[0mexperiment_names_short\u001b[0m \u001b[0;34m+=\u001b[0m \u001b[0;34m[\u001b[0m\u001b[0;34m'MaSIF 3L 9A'\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m----> 7\u001b[0;31m \u001b[0mcolors\u001b[0m \u001b[0;34m+=\u001b[0m \u001b[0;34m[\u001b[0m\u001b[0;36m40\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[0;31mNameError\u001b[0m: name 'colors' is not defined"]}], "source": ["performance += [0.849]\n", "times += [0.16402676922934395]\n", "time_errors += [0.04377787154914341]\n", "memory += [1491945956.9371428]\n", "memory_errors += [125881554.73354617]\n", "experiment_names_short += ['MaSIF 3L 9A']\n", "colors += [40]"]}, {"cell_type": "code", "execution_count": 151, "metadata": {}, "outputs": [], "source": ["experiment_names_short = [f'{i+1}) {experiment_names_short[i]}' for i in range(len(experiment_names_short))]"]}, {"cell_type": "code", "execution_count": 152, "metadata": {}, "outputs": [], "source": ["times = np.array(times)*1e3\n", "time_errors = np.array(time_errors)*1e3\n", "memory = np.array(memory)*1e-6\n", "memory_errors = np.array(memory_errors)*1e-6"]}, {"cell_type": "code", "execution_count": 153, "metadata": {}, "outputs": [], "source": ["colors = [f'hsl(240,100,{25+i*10.83})' for i in range(6)]+[f'hsl(116,100,{25+i*16.25})' for i in range(4)] + [f'hsl(300,100,{25+i*21.66})' for i in range(3)] + [f'hsl(0,100,50)']"]}, {"cell_type": "code", "execution_count": 154, "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"error_x": {"array": [5.528649900345416], "symmetric": true, "type": "data"}, "marker": {"color": "hsl(240,100,25.0)"}, "mode": "markers", "name": "1) Ours 1L 5A", "type": "scatter", "x": [17.535469505224334], "y": [0.8225412991271486]}, {"error_x": {"array": [5.350953992665304], "symmetric": true, "type": "data"}, "marker": {"color": "hsl(240,100,35.83)"}, "mode": "markers", "name": "2) Ours 1L 9A", "type": "scatter", "x": [17.492798607001145], "y": [0.8367986600715068]}, {"error_x": {"array": [5.059301312807847], "symmetric": true, "type": "data"}, "marker": {"color": "hsl(240,100,46.66)"}, "mode": "markers", "name": "3) Ours 1L 15A", "type": "scatter", "x": [16.42768771460887], "y": [0.8482431749127568]}, {"error_x": {"array": [11.514541370139163], "symmetric": true, "type": "data"}, "marker": {"color": "hsl(240,100,57.49)"}, "mode": "markers", "name": "4) Ours 3L 5A", "type": "scatter", "x": [37.07474871967616], "y": [0.8597660324128116]}, {"error_x": {"array": [8.34115514119933], "symmetric": true, "type": "data"}, "marker": {"color": "hsl(240,100,68.32)"}, "mode": "markers", "name": "5) Ours 3L 9A", "type": "scatter", "x": [36.484644654091824], "y": [0.8650878613609656]}, {"error_x": {"array": [9.779105276725772], "symmetric": true, "type": "data"}, "marker": {"color": "hsl(240,100,79.15)"}, "mode": "markers", "name": "6) Ours 3L 15A", "type": "scatter", "x": [37.16418247544364], "y": [0.8652432682847265]}, {"error_x": {"array": [42.56794581171482], "symmetric": true, "type": "data"}, "marker": {"color": "hsl(116,100,25.0)"}, "mode": "markers", "name": "7) PN++ 1L 5A", "type": "scatter", "x": [119.55246094907268], "y": [0.7630142728601188]}, {"error_x": {"array": [46.116139703391454], "symmetric": true, "type": "data"}, "marker": {"color": "hsl(116,100,41.25)"}, "mode": "markers", "name": "8) PN++ 1L 9A", "type": "scatter", "x": [142.3990974265538], "y": [0.7813292855857769]}, {"error_x": {"array": [130.85731976553717], "symmetric": true, "type": "data"}, "marker": {"color": "hsl(116,100,57.5)"}, "mode": "markers", "name": "9) PN++ 3L 5A", "type": "scatter", "x": [408.70048490802895], "y": [0.8307825342234235]}, {"error_x": {"array": [145.10523903835377], "symmetric": true, "type": "data"}, "marker": {"color": "hsl(116,100,73.75)"}, "mode": "markers", "name": "10) PN++ 3L 9A", "type": "scatter", "x": [481.70145241062295], "y": [0.8362551391657013]}, {"error_x": {"array": [8.374631224369965], "symmetric": true, "type": "data"}, "marker": {"color": "hsl(300,100,25.0)"}, "mode": "markers", "name": "11) DGCNN 1L K40", "type": "scatter", "x": [50.077644626746014], "y": [0.7070177689304884]}, {"error_x": {"array": [76.62718877387721], "symmetric": true, "type": "data"}, "marker": {"color": "hsl(300,100,46.66)"}, "mode": "markers", "name": "12) DGCNN 1L K100", "type": "scatter", "x": [395.29052812061957], "y": [0.6838021020108864]}, {"error_x": {"array": [9.72896160688735], "symmetric": true, "type": "data"}, "marker": {"color": "hsl(300,100,68.32)"}, "mode": "markers", "name": "13) DGCNN 3L K40", "type": "scatter", "x": [71.05260417702493], "y": [0.7081438400632245]}, {"error_x": {"array": [43.777871549143406], "symmetric": true, "type": "data"}, "marker": {"color": "hsl(0,100,50)"}, "mode": "markers", "name": "14) MaSIF 3L 9A", "type": "scatter", "x": [164.02676922934396], "y": [0.849]}], "layout": {"legend": {"title": {"text": "Models"}}, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "xaxis": {"tickvals": [10, 20, 40, 60, 80, 100, 200, 400, 600], "title": {"text": "Forward pass time per protein [ms] (log)"}, "type": "log"}, "yaxis": {"title": {"text": "Site identification ROC-AUC"}}}}, "text/html": ["<div>                            <div id=\"c8cc62da-9244-4e53-a925-0047f28ef5c7\" class=\"plotly-graph-div\" style=\"height:525px; width:100%;\"></div>            <script type=\"text/javascript\">                require([\"plotly\"], function(Plotly) {                    window.PLOTLYENV=window.PLOTLYENV || {};                                    if (document.getElementById(\"c8cc62da-9244-4e53-a925-0047f28ef5c7\")) {                    Plotly.newPlot(                        \"c8cc62da-9244-4e53-a925-0047f28ef5c7\",                        [{\"error_x\": {\"array\": [5.528649900345416], \"symmetric\": true, \"type\": \"data\"}, \"marker\": {\"color\": \"hsl(240,100,25.0)\"}, \"mode\": \"markers\", \"name\": \"1) Ours 1L 5A\", \"type\": \"scatter\", \"x\": [17.535469505224334], \"y\": [0.8225412991271486]}, {\"error_x\": {\"array\": [5.350953992665304], \"symmetric\": true, \"type\": \"data\"}, \"marker\": {\"color\": \"hsl(240,100,35.83)\"}, \"mode\": \"markers\", \"name\": \"2) Ours 1L 9A\", \"type\": \"scatter\", \"x\": [17.492798607001145], \"y\": [0.8367986600715068]}, {\"error_x\": {\"array\": [5.059301312807847], \"symmetric\": true, \"type\": \"data\"}, \"marker\": {\"color\": \"hsl(240,100,46.66)\"}, \"mode\": \"markers\", \"name\": \"3) Ours 1L 15A\", \"type\": \"scatter\", \"x\": [16.42768771460887], \"y\": [0.8482431749127568]}, {\"error_x\": {\"array\": [11.514541370139163], \"symmetric\": true, \"type\": \"data\"}, \"marker\": {\"color\": \"hsl(240,100,57.49)\"}, \"mode\": \"markers\", \"name\": \"4) Ours 3L 5A\", \"type\": \"scatter\", \"x\": [37.07474871967616], \"y\": [0.8597660324128116]}, {\"error_x\": {\"array\": [8.34115514119933], \"symmetric\": true, \"type\": \"data\"}, \"marker\": {\"color\": \"hsl(240,100,68.32)\"}, \"mode\": \"markers\", \"name\": \"5) Ours 3L 9A\", \"type\": \"scatter\", \"x\": [36.484644654091824], \"y\": [0.8650878613609656]}, {\"error_x\": {\"array\": [9.779105276725772], \"symmetric\": true, \"type\": \"data\"}, \"marker\": {\"color\": \"hsl(240,100,79.15)\"}, \"mode\": \"markers\", \"name\": \"6) Ours 3L 15A\", \"type\": \"scatter\", \"x\": [37.16418247544364], \"y\": [0.8652432682847265]}, {\"error_x\": {\"array\": [42.56794581171482], \"symmetric\": true, \"type\": \"data\"}, \"marker\": {\"color\": \"hsl(116,100,25.0)\"}, \"mode\": \"markers\", \"name\": \"7) PN++ 1L 5A\", \"type\": \"scatter\", \"x\": [119.55246094907268], \"y\": [0.7630142728601188]}, {\"error_x\": {\"array\": [46.116139703391454], \"symmetric\": true, \"type\": \"data\"}, \"marker\": {\"color\": \"hsl(116,100,41.25)\"}, \"mode\": \"markers\", \"name\": \"8) PN++ 1L 9A\", \"type\": \"scatter\", \"x\": [142.3990974265538], \"y\": [0.7813292855857769]}, {\"error_x\": {\"array\": [130.85731976553717], \"symmetric\": true, \"type\": \"data\"}, \"marker\": {\"color\": \"hsl(116,100,57.5)\"}, \"mode\": \"markers\", \"name\": \"9) PN++ 3L 5A\", \"type\": \"scatter\", \"x\": [408.70048490802895], \"y\": [0.8307825342234235]}, {\"error_x\": {\"array\": [145.10523903835377], \"symmetric\": true, \"type\": \"data\"}, \"marker\": {\"color\": \"hsl(116,100,73.75)\"}, \"mode\": \"markers\", \"name\": \"10) PN++ 3L 9A\", \"type\": \"scatter\", \"x\": [481.70145241062295], \"y\": [0.8362551391657013]}, {\"error_x\": {\"array\": [8.374631224369965], \"symmetric\": true, \"type\": \"data\"}, \"marker\": {\"color\": \"hsl(300,100,25.0)\"}, \"mode\": \"markers\", \"name\": \"11) DGCNN 1L K40\", \"type\": \"scatter\", \"x\": [50.077644626746014], \"y\": [0.7070177689304884]}, {\"error_x\": {\"array\": [76.62718877387721], \"symmetric\": true, \"type\": \"data\"}, \"marker\": {\"color\": \"hsl(300,100,46.66)\"}, \"mode\": \"markers\", \"name\": \"12) DGCNN 1L K100\", \"type\": \"scatter\", \"x\": [395.29052812061957], \"y\": [0.6838021020108864]}, {\"error_x\": {\"array\": [9.72896160688735], \"symmetric\": true, \"type\": \"data\"}, \"marker\": {\"color\": \"hsl(300,100,68.32)\"}, \"mode\": \"markers\", \"name\": \"13) DGCNN 3L K40\", \"type\": \"scatter\", \"x\": [71.05260417702493], \"y\": [0.7081438400632245]}, {\"error_x\": {\"array\": [43.777871549143406], \"symmetric\": true, \"type\": \"data\"}, \"marker\": {\"color\": \"hsl(0,100,50)\"}, \"mode\": \"markers\", \"name\": \"14) MaSIF 3L 9A\", \"type\": \"scatter\", \"x\": [164.02676922934396], \"y\": [0.849]}],                        {\"legend\": {\"title\": {\"text\": \"Models\"}}, \"template\": {\"data\": {\"bar\": [{\"error_x\": {\"color\": \"#2a3f5f\"}, \"error_y\": {\"color\": \"#2a3f5f\"}, \"marker\": {\"line\": {\"color\": \"#E5ECF6\", \"width\": 0.5}}, \"type\": \"bar\"}], \"barpolar\": [{\"marker\": {\"line\": {\"color\": \"#E5ECF6\", \"width\": 0.5}}, \"type\": \"barpolar\"}], \"carpet\": [{\"aaxis\": {\"endlinecolor\": \"#2a3f5f\", \"gridcolor\": \"white\", \"linecolor\": \"white\", \"minorgridcolor\": \"white\", \"startlinecolor\": \"#2a3f5f\"}, \"baxis\": {\"endlinecolor\": \"#2a3f5f\", \"gridcolor\": \"white\", \"linecolor\": \"white\", \"minorgridcolor\": \"white\", \"startlinecolor\": \"#2a3f5f\"}, \"type\": \"carpet\"}], \"choropleth\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"type\": \"choropleth\"}], \"contour\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"colorscale\": [[0.0, \"#0d0887\"], [0.1111111111111111, \"#46039f\"], [0.2222222222222222, \"#7201a8\"], [0.3333333333333333, \"#9c179e\"], [0.4444444444444444, \"#bd3786\"], [0.5555555555555556, \"#d8576b\"], [0.6666666666666666, \"#ed7953\"], [0.7777777777777778, \"#fb9f3a\"], [0.8888888888888888, \"#fdca26\"], [1.0, \"#f0f921\"]], \"type\": \"contour\"}], \"contourcarpet\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"type\": \"contourcarpet\"}], \"heatmap\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"colorscale\": [[0.0, \"#0d0887\"], [0.1111111111111111, \"#46039f\"], [0.2222222222222222, \"#7201a8\"], [0.3333333333333333, \"#9c179e\"], [0.4444444444444444, \"#bd3786\"], [0.5555555555555556, \"#d8576b\"], [0.6666666666666666, \"#ed7953\"], [0.7777777777777778, \"#fb9f3a\"], [0.8888888888888888, \"#fdca26\"], [1.0, \"#f0f921\"]], \"type\": \"heatmap\"}], \"heatmapgl\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"colorscale\": [[0.0, \"#0d0887\"], [0.1111111111111111, \"#46039f\"], [0.2222222222222222, \"#7201a8\"], [0.3333333333333333, \"#9c179e\"], [0.4444444444444444, \"#bd3786\"], [0.5555555555555556, \"#d8576b\"], [0.6666666666666666, \"#ed7953\"], [0.7777777777777778, \"#fb9f3a\"], [0.8888888888888888, \"#fdca26\"], [1.0, \"#f0f921\"]], \"type\": \"heatmapgl\"}], \"histogram\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"histogram\"}], \"histogram2d\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"colorscale\": [[0.0, \"#0d0887\"], [0.1111111111111111, \"#46039f\"], [0.2222222222222222, \"#7201a8\"], [0.3333333333333333, \"#9c179e\"], [0.4444444444444444, \"#bd3786\"], [0.5555555555555556, \"#d8576b\"], [0.6666666666666666, \"#ed7953\"], [0.7777777777777778, \"#fb9f3a\"], [0.8888888888888888, \"#fdca26\"], [1.0, \"#f0f921\"]], \"type\": \"histogram2d\"}], \"histogram2dcontour\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"colorscale\": [[0.0, \"#0d0887\"], [0.1111111111111111, \"#46039f\"], [0.2222222222222222, \"#7201a8\"], [0.3333333333333333, \"#9c179e\"], [0.4444444444444444, \"#bd3786\"], [0.5555555555555556, \"#d8576b\"], [0.6666666666666666, \"#ed7953\"], [0.7777777777777778, \"#fb9f3a\"], [0.8888888888888888, \"#fdca26\"], [1.0, \"#f0f921\"]], \"type\": \"histogram2dcontour\"}], \"mesh3d\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"type\": \"mesh3d\"}], \"parcoords\": [{\"line\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"parcoords\"}], \"pie\": [{\"automargin\": true, \"type\": \"pie\"}], \"scatter\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scatter\"}], \"scatter3d\": [{\"line\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scatter3d\"}], \"scattercarpet\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scattercarpet\"}], \"scattergeo\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scattergeo\"}], \"scattergl\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scattergl\"}], \"scattermapbox\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scattermapbox\"}], \"scatterpolar\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scatterpolar\"}], \"scatterpolargl\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scatterpolargl\"}], \"scatterternary\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scatterternary\"}], \"surface\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"colorscale\": [[0.0, \"#0d0887\"], [0.1111111111111111, \"#46039f\"], [0.2222222222222222, \"#7201a8\"], [0.3333333333333333, \"#9c179e\"], [0.4444444444444444, \"#bd3786\"], [0.5555555555555556, \"#d8576b\"], [0.6666666666666666, \"#ed7953\"], [0.7777777777777778, \"#fb9f3a\"], [0.8888888888888888, \"#fdca26\"], [1.0, \"#f0f921\"]], \"type\": \"surface\"}], \"table\": [{\"cells\": {\"fill\": {\"color\": \"#EBF0F8\"}, \"line\": {\"color\": \"white\"}}, \"header\": {\"fill\": {\"color\": \"#C8D4E3\"}, \"line\": {\"color\": \"white\"}}, \"type\": \"table\"}]}, \"layout\": {\"annotationdefaults\": {\"arrowcolor\": \"#2a3f5f\", \"arrowhead\": 0, \"arrowwidth\": 1}, \"coloraxis\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"colorscale\": {\"diverging\": [[0, \"#8e0152\"], [0.1, \"#c51b7d\"], [0.2, \"#de77ae\"], [0.3, \"#f1b6da\"], [0.4, \"#fde0ef\"], [0.5, \"#f7f7f7\"], [0.6, \"#e6f5d0\"], [0.7, \"#b8e186\"], [0.8, \"#7fbc41\"], [0.9, \"#4d9221\"], [1, \"#276419\"]], \"sequential\": [[0.0, \"#0d0887\"], [0.1111111111111111, \"#46039f\"], [0.2222222222222222, \"#7201a8\"], [0.3333333333333333, \"#9c179e\"], [0.4444444444444444, \"#bd3786\"], [0.5555555555555556, \"#d8576b\"], [0.6666666666666666, \"#ed7953\"], [0.7777777777777778, \"#fb9f3a\"], [0.8888888888888888, \"#fdca26\"], [1.0, \"#f0f921\"]], \"sequentialminus\": [[0.0, \"#0d0887\"], [0.1111111111111111, \"#46039f\"], [0.2222222222222222, \"#7201a8\"], [0.3333333333333333, \"#9c179e\"], [0.4444444444444444, \"#bd3786\"], [0.5555555555555556, \"#d8576b\"], [0.6666666666666666, \"#ed7953\"], [0.7777777777777778, \"#fb9f3a\"], [0.8888888888888888, \"#fdca26\"], [1.0, \"#f0f921\"]]}, \"colorway\": [\"#636efa\", \"#EF553B\", \"#00cc96\", \"#ab63fa\", \"#FFA15A\", \"#19d3f3\", \"#FF6692\", \"#B6E880\", \"#FF97FF\", \"#FECB52\"], \"font\": {\"color\": \"#2a3f5f\"}, \"geo\": {\"bgcolor\": \"white\", \"lakecolor\": \"white\", \"landcolor\": \"#E5ECF6\", \"showlakes\": true, \"showland\": true, \"subunitcolor\": \"white\"}, \"hoverlabel\": {\"align\": \"left\"}, \"hovermode\": \"closest\", \"mapbox\": {\"style\": \"light\"}, \"paper_bgcolor\": \"white\", \"plot_bgcolor\": \"#E5ECF6\", \"polar\": {\"angularaxis\": {\"gridcolor\": \"white\", \"linecolor\": \"white\", \"ticks\": \"\"}, \"bgcolor\": \"#E5ECF6\", \"radialaxis\": {\"gridcolor\": \"white\", \"linecolor\": \"white\", \"ticks\": \"\"}}, \"scene\": {\"xaxis\": {\"backgroundcolor\": \"#E5ECF6\", \"gridcolor\": \"white\", \"gridwidth\": 2, \"linecolor\": \"white\", \"showbackground\": true, \"ticks\": \"\", \"zerolinecolor\": \"white\"}, \"yaxis\": {\"backgroundcolor\": \"#E5ECF6\", \"gridcolor\": \"white\", \"gridwidth\": 2, \"linecolor\": \"white\", \"showbackground\": true, \"ticks\": \"\", \"zerolinecolor\": \"white\"}, \"zaxis\": {\"backgroundcolor\": \"#E5ECF6\", \"gridcolor\": \"white\", \"gridwidth\": 2, \"linecolor\": \"white\", \"showbackground\": true, \"ticks\": \"\", \"zerolinecolor\": \"white\"}}, \"shapedefaults\": {\"line\": {\"color\": \"#2a3f5f\"}}, \"ternary\": {\"aaxis\": {\"gridcolor\": \"white\", \"linecolor\": \"white\", \"ticks\": \"\"}, \"baxis\": {\"gridcolor\": \"white\", \"linecolor\": \"white\", \"ticks\": \"\"}, \"bgcolor\": \"#E5ECF6\", \"caxis\": {\"gridcolor\": \"white\", \"linecolor\": \"white\", \"ticks\": \"\"}}, \"title\": {\"x\": 0.05}, \"xaxis\": {\"automargin\": true, \"gridcolor\": \"white\", \"linecolor\": \"white\", \"ticks\": \"\", \"title\": {\"standoff\": 15}, \"zerolinecolor\": \"white\", \"zerolinewidth\": 2}, \"yaxis\": {\"automargin\": true, \"gridcolor\": \"white\", \"linecolor\": \"white\", \"ticks\": \"\", \"title\": {\"standoff\": 15}, \"zerolinecolor\": \"white\", \"zerolinewidth\": 2}}}, \"xaxis\": {\"tickvals\": [10.0, 20.0, 40.0, 60.0, 80.0, 100.0, 200.0, 400.0, 600.0], \"title\": {\"text\": \"Forward pass time per protein [ms] (log)\"}, \"type\": \"log\"}, \"yaxis\": {\"title\": {\"text\": \"Site identification ROC-AUC\"}}},                        {\"responsive\": true}                    ).then(function(){\n", "                            \n", "var gd = document.getElementById('c8cc62da-9244-4e53-a925-0047f28ef5c7');\n", "var x = new MutationObserver(function (mutations, observer) {{\n", "        var display = window.getComputedStyle(gd).display;\n", "        if (!display || display === 'none') {{\n", "            console.log([gd, 'removed!']);\n", "            Plotly.purge(gd);\n", "            observer.disconnect();\n", "        }}\n", "}});\n", "\n", "// Listen for the removal of the full notebook cells\n", "var notebookContainer = gd.closest('#notebook-container');\n", "if (notebookContainer) {{\n", "    x.observe(<PERSON><PERSON><PERSON><PERSON>, {childList: true});\n", "}}\n", "\n", "// Listen for the clearing of the current output cell\n", "var outputEl = gd.closest('.output');\n", "if (outputEl) {{\n", "    x.observe(outputEl, {childList: true});\n", "}}\n", "\n", "                        })                };                });            </script>        </div>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig = go.Figure()\n", "for i in range(len(times)):\n", "    fig.add_trace(go.<PERSON>(\n", "            x=[times[i]],\n", "            y=[performance[i]],\n", "            mode='markers',\n", "            name=experiment_names_short[i],\n", "            marker = dict(color=colors[i]),\n", "            error_x=dict(\n", "                type='data',\n", "                symmetric=True,\n", "                array=[time_errors[i]])))\n", "\n", "\n", "fig.update_layout(\n", "    xaxis_title='Forward pass time per protein [ms] (log)',\n", "    yaxis_title='Site identification ROC-AUC',\n", "    legend_title=\"Models\",\n", ")\n", "fig.update_xaxes(type=\"log\")\n", "fig.update_layout(\n", "    xaxis = dict(\n", "        tickvals = [1e1,2e1,4e1,6e1,8e1,1e2,2e2,4e2,6e2],\n", "        #tickvals = [10, 20, 50, 100, 200, 500],\n", "    )\n", ")\n", "\n", "fig.show()\n", "fig.write_image('figures/time_vs_perf.pdf')"]}, {"cell_type": "code", "execution_count": 155, "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"error_x": {"array": [19.951238544987916], "symmetric": true, "type": "data"}, "marker": {"color": "hsl(240,100,25.0)"}, "mode": "markers", "name": "1) Ours 1L 5A", "type": "scatter", "x": [131.44750166292135], "y": [0.8225412991271486]}, {"error_x": {"array": [19.951238544987916], "symmetric": true, "type": "data"}, "marker": {"color": "hsl(240,100,35.83)"}, "mode": "markers", "name": "2) Ours 1L 9A", "type": "scatter", "x": [131.44750166292135], "y": [0.8367986600715068]}, {"error_x": {"array": [19.951238544987916], "symmetric": true, "type": "data"}, "marker": {"color": "hsl(240,100,46.66)"}, "mode": "markers", "name": "3) Ours 1L 15A", "type": "scatter", "x": [131.44750166292135], "y": [0.8482431749127568]}, {"error_x": {"array": [20.57705066924205], "symmetric": true, "type": "data"}, "marker": {"color": "hsl(240,100,57.49)"}, "mode": "markers", "name": "4) Ours 3L 5A", "type": "scatter", "x": [131.8553786966292], "y": [0.8597660324128116]}, {"error_x": {"array": [20.57705066924205], "symmetric": true, "type": "data"}, "marker": {"color": "hsl(240,100,68.32)"}, "mode": "markers", "name": "5) Ours 3L 9A", "type": "scatter", "x": [131.8553786966292], "y": [0.8650878613609656]}, {"error_x": {"array": [20.57705066924205], "symmetric": true, "type": "data"}, "marker": {"color": "hsl(240,100,79.15)"}, "mode": "markers", "name": "6) Ours 3L 15A", "type": "scatter", "x": [131.8553786966292], "y": [0.8652432682847265]}, {"error_x": {"array": [469.7109731645726], "symmetric": true, "type": "data"}, "marker": {"color": "hsl(116,100,25.0)"}, "mode": "markers", "name": "7) PN++ 1L 5A", "type": "scatter", "x": [3025.723793258427], "y": [0.7630142728601188]}, {"error_x": {"array": [477.6390960312726], "symmetric": true, "type": "data"}, "marker": {"color": "hsl(116,100,41.25)"}, "mode": "markers", "name": "8) PN++ 1L 9A", "type": "scatter", "x": [3068.2044663370784], "y": [0.7813292855857769]}, {"error_x": {"array": [619.4290980168416], "symmetric": true, "type": "data"}, "marker": {"color": "hsl(116,100,57.5)"}, "mode": "markers", "name": "9) PN++ 3L 5A", "type": "scatter", "x": [3952.2142719999997], "y": [0.8307825342234235]}, {"error_x": {"array": [627.3884255777202], "symmetric": true, "type": "data"}, "marker": {"color": "hsl(116,100,73.75)"}, "mode": "markers", "name": "10) PN++ 3L 9A", "type": "scatter", "x": [3995.110192898876], "y": [0.8362551391657013]}, {"error_x": {"array": [100.63408144310193], "symmetric": true, "type": "data"}, "marker": {"color": "hsl(300,100,25.0)"}, "mode": "markers", "name": "11) DGCNN 1L K40", "type": "scatter", "x": [641.8060785617977], "y": [0.7070177689304884]}, {"error_x": {"array": [253.22976173261787], "symmetric": true, "type": "data"}, "marker": {"color": "hsl(300,100,46.66)"}, "mode": "markers", "name": "12) DGCNN 1L K100", "type": "scatter", "x": [1680.5417391460674], "y": [0.6838021020108864]}, {"error_x": {"array": [100.32882494334173], "symmetric": true, "type": "data"}, "marker": {"color": "hsl(300,100,68.32)"}, "mode": "markers", "name": "13) DGCNN 3L K40", "type": "scatter", "x": [641.7386973483146], "y": [0.7081438400632245]}, {"error_x": {"array": [125.88155473354617], "symmetric": true, "type": "data"}, "marker": {"color": "hsl(0,100,50)"}, "mode": "markers", "name": "14) MaSIF 3L 9A", "type": "scatter", "x": [1491.9459569371427], "y": [0.849]}], "layout": {"legend": {"title": {"text": "Models"}}, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "xaxis": {"tickvals": [100, 200, 400, 600, 800, 1000, 2000, 4000], "title": {"text": "Memory usage per protein [MB] (log)"}, "type": "log"}, "yaxis": {"title": {"text": "Site identification ROC-AUC"}}}}, "text/html": ["<div>                            <div id=\"52c3e7a8-c75d-4b31-a7ed-1ccaf7a36311\" class=\"plotly-graph-div\" style=\"height:525px; width:100%;\"></div>            <script type=\"text/javascript\">                require([\"plotly\"], function(Plotly) {                    window.PLOTLYENV=window.PLOTLYENV || {};                                    if (document.getElementById(\"52c3e7a8-c75d-4b31-a7ed-1ccaf7a36311\")) {                    Plotly.newPlot(                        \"52c3e7a8-c75d-4b31-a7ed-1ccaf7a36311\",                        [{\"error_x\": {\"array\": [19.951238544987916], \"symmetric\": true, \"type\": \"data\"}, \"marker\": {\"color\": \"hsl(240,100,25.0)\"}, \"mode\": \"markers\", \"name\": \"1) Ours 1L 5A\", \"type\": \"scatter\", \"x\": [131.44750166292135], \"y\": [0.8225412991271486]}, {\"error_x\": {\"array\": [19.951238544987916], \"symmetric\": true, \"type\": \"data\"}, \"marker\": {\"color\": \"hsl(240,100,35.83)\"}, \"mode\": \"markers\", \"name\": \"2) Ours 1L 9A\", \"type\": \"scatter\", \"x\": [131.44750166292135], \"y\": [0.8367986600715068]}, {\"error_x\": {\"array\": [19.951238544987916], \"symmetric\": true, \"type\": \"data\"}, \"marker\": {\"color\": \"hsl(240,100,46.66)\"}, \"mode\": \"markers\", \"name\": \"3) Ours 1L 15A\", \"type\": \"scatter\", \"x\": [131.44750166292135], \"y\": [0.8482431749127568]}, {\"error_x\": {\"array\": [20.57705066924205], \"symmetric\": true, \"type\": \"data\"}, \"marker\": {\"color\": \"hsl(240,100,57.49)\"}, \"mode\": \"markers\", \"name\": \"4) Ours 3L 5A\", \"type\": \"scatter\", \"x\": [131.8553786966292], \"y\": [0.8597660324128116]}, {\"error_x\": {\"array\": [20.57705066924205], \"symmetric\": true, \"type\": \"data\"}, \"marker\": {\"color\": \"hsl(240,100,68.32)\"}, \"mode\": \"markers\", \"name\": \"5) Ours 3L 9A\", \"type\": \"scatter\", \"x\": [131.8553786966292], \"y\": [0.8650878613609656]}, {\"error_x\": {\"array\": [20.57705066924205], \"symmetric\": true, \"type\": \"data\"}, \"marker\": {\"color\": \"hsl(240,100,79.15)\"}, \"mode\": \"markers\", \"name\": \"6) Ours 3L 15A\", \"type\": \"scatter\", \"x\": [131.8553786966292], \"y\": [0.8652432682847265]}, {\"error_x\": {\"array\": [469.7109731645726], \"symmetric\": true, \"type\": \"data\"}, \"marker\": {\"color\": \"hsl(116,100,25.0)\"}, \"mode\": \"markers\", \"name\": \"7) PN++ 1L 5A\", \"type\": \"scatter\", \"x\": [3025.723793258427], \"y\": [0.7630142728601188]}, {\"error_x\": {\"array\": [477.6390960312726], \"symmetric\": true, \"type\": \"data\"}, \"marker\": {\"color\": \"hsl(116,100,41.25)\"}, \"mode\": \"markers\", \"name\": \"8) PN++ 1L 9A\", \"type\": \"scatter\", \"x\": [3068.2044663370784], \"y\": [0.7813292855857769]}, {\"error_x\": {\"array\": [619.4290980168416], \"symmetric\": true, \"type\": \"data\"}, \"marker\": {\"color\": \"hsl(116,100,57.5)\"}, \"mode\": \"markers\", \"name\": \"9) PN++ 3L 5A\", \"type\": \"scatter\", \"x\": [3952.2142719999997], \"y\": [0.8307825342234235]}, {\"error_x\": {\"array\": [627.3884255777202], \"symmetric\": true, \"type\": \"data\"}, \"marker\": {\"color\": \"hsl(116,100,73.75)\"}, \"mode\": \"markers\", \"name\": \"10) PN++ 3L 9A\", \"type\": \"scatter\", \"x\": [3995.110192898876], \"y\": [0.8362551391657013]}, {\"error_x\": {\"array\": [100.63408144310193], \"symmetric\": true, \"type\": \"data\"}, \"marker\": {\"color\": \"hsl(300,100,25.0)\"}, \"mode\": \"markers\", \"name\": \"11) DGCNN 1L K40\", \"type\": \"scatter\", \"x\": [641.8060785617977], \"y\": [0.7070177689304884]}, {\"error_x\": {\"array\": [253.22976173261787], \"symmetric\": true, \"type\": \"data\"}, \"marker\": {\"color\": \"hsl(300,100,46.66)\"}, \"mode\": \"markers\", \"name\": \"12) DGCNN 1L K100\", \"type\": \"scatter\", \"x\": [1680.5417391460674], \"y\": [0.6838021020108864]}, {\"error_x\": {\"array\": [100.32882494334173], \"symmetric\": true, \"type\": \"data\"}, \"marker\": {\"color\": \"hsl(300,100,68.32)\"}, \"mode\": \"markers\", \"name\": \"13) DGCNN 3L K40\", \"type\": \"scatter\", \"x\": [641.7386973483146], \"y\": [0.7081438400632245]}, {\"error_x\": {\"array\": [125.88155473354617], \"symmetric\": true, \"type\": \"data\"}, \"marker\": {\"color\": \"hsl(0,100,50)\"}, \"mode\": \"markers\", \"name\": \"14) MaSIF 3L 9A\", \"type\": \"scatter\", \"x\": [1491.9459569371427], \"y\": [0.849]}],                        {\"legend\": {\"title\": {\"text\": \"Models\"}}, \"template\": {\"data\": {\"bar\": [{\"error_x\": {\"color\": \"#2a3f5f\"}, \"error_y\": {\"color\": \"#2a3f5f\"}, \"marker\": {\"line\": {\"color\": \"#E5ECF6\", \"width\": 0.5}}, \"type\": \"bar\"}], \"barpolar\": [{\"marker\": {\"line\": {\"color\": \"#E5ECF6\", \"width\": 0.5}}, \"type\": \"barpolar\"}], \"carpet\": [{\"aaxis\": {\"endlinecolor\": \"#2a3f5f\", \"gridcolor\": \"white\", \"linecolor\": \"white\", \"minorgridcolor\": \"white\", \"startlinecolor\": \"#2a3f5f\"}, \"baxis\": {\"endlinecolor\": \"#2a3f5f\", \"gridcolor\": \"white\", \"linecolor\": \"white\", \"minorgridcolor\": \"white\", \"startlinecolor\": \"#2a3f5f\"}, \"type\": \"carpet\"}], \"choropleth\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"type\": \"choropleth\"}], \"contour\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"colorscale\": [[0.0, \"#0d0887\"], [0.1111111111111111, \"#46039f\"], [0.2222222222222222, \"#7201a8\"], [0.3333333333333333, \"#9c179e\"], [0.4444444444444444, \"#bd3786\"], [0.5555555555555556, \"#d8576b\"], [0.6666666666666666, \"#ed7953\"], [0.7777777777777778, \"#fb9f3a\"], [0.8888888888888888, \"#fdca26\"], [1.0, \"#f0f921\"]], \"type\": \"contour\"}], \"contourcarpet\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"type\": \"contourcarpet\"}], \"heatmap\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"colorscale\": [[0.0, \"#0d0887\"], [0.1111111111111111, \"#46039f\"], [0.2222222222222222, \"#7201a8\"], [0.3333333333333333, \"#9c179e\"], [0.4444444444444444, \"#bd3786\"], [0.5555555555555556, \"#d8576b\"], [0.6666666666666666, \"#ed7953\"], [0.7777777777777778, \"#fb9f3a\"], [0.8888888888888888, \"#fdca26\"], [1.0, \"#f0f921\"]], \"type\": \"heatmap\"}], \"heatmapgl\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"colorscale\": [[0.0, \"#0d0887\"], [0.1111111111111111, \"#46039f\"], [0.2222222222222222, \"#7201a8\"], [0.3333333333333333, \"#9c179e\"], [0.4444444444444444, \"#bd3786\"], [0.5555555555555556, \"#d8576b\"], [0.6666666666666666, \"#ed7953\"], [0.7777777777777778, \"#fb9f3a\"], [0.8888888888888888, \"#fdca26\"], [1.0, \"#f0f921\"]], \"type\": \"heatmapgl\"}], \"histogram\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"histogram\"}], \"histogram2d\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"colorscale\": [[0.0, \"#0d0887\"], [0.1111111111111111, \"#46039f\"], [0.2222222222222222, \"#7201a8\"], [0.3333333333333333, \"#9c179e\"], [0.4444444444444444, \"#bd3786\"], [0.5555555555555556, \"#d8576b\"], [0.6666666666666666, \"#ed7953\"], [0.7777777777777778, \"#fb9f3a\"], [0.8888888888888888, \"#fdca26\"], [1.0, \"#f0f921\"]], \"type\": \"histogram2d\"}], \"histogram2dcontour\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"colorscale\": [[0.0, \"#0d0887\"], [0.1111111111111111, \"#46039f\"], [0.2222222222222222, \"#7201a8\"], [0.3333333333333333, \"#9c179e\"], [0.4444444444444444, \"#bd3786\"], [0.5555555555555556, \"#d8576b\"], [0.6666666666666666, \"#ed7953\"], [0.7777777777777778, \"#fb9f3a\"], [0.8888888888888888, \"#fdca26\"], [1.0, \"#f0f921\"]], \"type\": \"histogram2dcontour\"}], \"mesh3d\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"type\": \"mesh3d\"}], \"parcoords\": [{\"line\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"parcoords\"}], \"pie\": [{\"automargin\": true, \"type\": \"pie\"}], \"scatter\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scatter\"}], \"scatter3d\": [{\"line\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scatter3d\"}], \"scattercarpet\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scattercarpet\"}], \"scattergeo\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scattergeo\"}], \"scattergl\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scattergl\"}], \"scattermapbox\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scattermapbox\"}], \"scatterpolar\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scatterpolar\"}], \"scatterpolargl\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scatterpolargl\"}], \"scatterternary\": [{\"marker\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"type\": \"scatterternary\"}], \"surface\": [{\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}, \"colorscale\": [[0.0, \"#0d0887\"], [0.1111111111111111, \"#46039f\"], [0.2222222222222222, \"#7201a8\"], [0.3333333333333333, \"#9c179e\"], [0.4444444444444444, \"#bd3786\"], [0.5555555555555556, \"#d8576b\"], [0.6666666666666666, \"#ed7953\"], [0.7777777777777778, \"#fb9f3a\"], [0.8888888888888888, \"#fdca26\"], [1.0, \"#f0f921\"]], \"type\": \"surface\"}], \"table\": [{\"cells\": {\"fill\": {\"color\": \"#EBF0F8\"}, \"line\": {\"color\": \"white\"}}, \"header\": {\"fill\": {\"color\": \"#C8D4E3\"}, \"line\": {\"color\": \"white\"}}, \"type\": \"table\"}]}, \"layout\": {\"annotationdefaults\": {\"arrowcolor\": \"#2a3f5f\", \"arrowhead\": 0, \"arrowwidth\": 1}, \"coloraxis\": {\"colorbar\": {\"outlinewidth\": 0, \"ticks\": \"\"}}, \"colorscale\": {\"diverging\": [[0, \"#8e0152\"], [0.1, \"#c51b7d\"], [0.2, \"#de77ae\"], [0.3, \"#f1b6da\"], [0.4, \"#fde0ef\"], [0.5, \"#f7f7f7\"], [0.6, \"#e6f5d0\"], [0.7, \"#b8e186\"], [0.8, \"#7fbc41\"], [0.9, \"#4d9221\"], [1, \"#276419\"]], \"sequential\": [[0.0, \"#0d0887\"], [0.1111111111111111, \"#46039f\"], [0.2222222222222222, \"#7201a8\"], [0.3333333333333333, \"#9c179e\"], [0.4444444444444444, \"#bd3786\"], [0.5555555555555556, \"#d8576b\"], [0.6666666666666666, \"#ed7953\"], [0.7777777777777778, \"#fb9f3a\"], [0.8888888888888888, \"#fdca26\"], [1.0, \"#f0f921\"]], \"sequentialminus\": [[0.0, \"#0d0887\"], [0.1111111111111111, \"#46039f\"], [0.2222222222222222, \"#7201a8\"], [0.3333333333333333, \"#9c179e\"], [0.4444444444444444, \"#bd3786\"], [0.5555555555555556, \"#d8576b\"], [0.6666666666666666, \"#ed7953\"], [0.7777777777777778, \"#fb9f3a\"], [0.8888888888888888, \"#fdca26\"], [1.0, \"#f0f921\"]]}, \"colorway\": [\"#636efa\", \"#EF553B\", \"#00cc96\", \"#ab63fa\", \"#FFA15A\", \"#19d3f3\", \"#FF6692\", \"#B6E880\", \"#FF97FF\", \"#FECB52\"], \"font\": {\"color\": \"#2a3f5f\"}, \"geo\": {\"bgcolor\": \"white\", \"lakecolor\": \"white\", \"landcolor\": \"#E5ECF6\", \"showlakes\": true, \"showland\": true, \"subunitcolor\": \"white\"}, \"hoverlabel\": {\"align\": \"left\"}, \"hovermode\": \"closest\", \"mapbox\": {\"style\": \"light\"}, \"paper_bgcolor\": \"white\", \"plot_bgcolor\": \"#E5ECF6\", \"polar\": {\"angularaxis\": {\"gridcolor\": \"white\", \"linecolor\": \"white\", \"ticks\": \"\"}, \"bgcolor\": \"#E5ECF6\", \"radialaxis\": {\"gridcolor\": \"white\", \"linecolor\": \"white\", \"ticks\": \"\"}}, \"scene\": {\"xaxis\": {\"backgroundcolor\": \"#E5ECF6\", \"gridcolor\": \"white\", \"gridwidth\": 2, \"linecolor\": \"white\", \"showbackground\": true, \"ticks\": \"\", \"zerolinecolor\": \"white\"}, \"yaxis\": {\"backgroundcolor\": \"#E5ECF6\", \"gridcolor\": \"white\", \"gridwidth\": 2, \"linecolor\": \"white\", \"showbackground\": true, \"ticks\": \"\", \"zerolinecolor\": \"white\"}, \"zaxis\": {\"backgroundcolor\": \"#E5ECF6\", \"gridcolor\": \"white\", \"gridwidth\": 2, \"linecolor\": \"white\", \"showbackground\": true, \"ticks\": \"\", \"zerolinecolor\": \"white\"}}, \"shapedefaults\": {\"line\": {\"color\": \"#2a3f5f\"}}, \"ternary\": {\"aaxis\": {\"gridcolor\": \"white\", \"linecolor\": \"white\", \"ticks\": \"\"}, \"baxis\": {\"gridcolor\": \"white\", \"linecolor\": \"white\", \"ticks\": \"\"}, \"bgcolor\": \"#E5ECF6\", \"caxis\": {\"gridcolor\": \"white\", \"linecolor\": \"white\", \"ticks\": \"\"}}, \"title\": {\"x\": 0.05}, \"xaxis\": {\"automargin\": true, \"gridcolor\": \"white\", \"linecolor\": \"white\", \"ticks\": \"\", \"title\": {\"standoff\": 15}, \"zerolinecolor\": \"white\", \"zerolinewidth\": 2}, \"yaxis\": {\"automargin\": true, \"gridcolor\": \"white\", \"linecolor\": \"white\", \"ticks\": \"\", \"title\": {\"standoff\": 15}, \"zerolinecolor\": \"white\", \"zerolinewidth\": 2}}}, \"xaxis\": {\"tickvals\": [100, 200, 400, 600, 800, 1000, 2000, 4000], \"title\": {\"text\": \"Memory usage per protein [MB] (log)\"}, \"type\": \"log\"}, \"yaxis\": {\"title\": {\"text\": \"Site identification ROC-AUC\"}}},                        {\"responsive\": true}                    ).then(function(){\n", "                            \n", "var gd = document.getElementById('52c3e7a8-c75d-4b31-a7ed-1ccaf7a36311');\n", "var x = new MutationObserver(function (mutations, observer) {{\n", "        var display = window.getComputedStyle(gd).display;\n", "        if (!display || display === 'none') {{\n", "            console.log([gd, 'removed!']);\n", "            Plotly.purge(gd);\n", "            observer.disconnect();\n", "        }}\n", "}});\n", "\n", "// Listen for the removal of the full notebook cells\n", "var notebookContainer = gd.closest('#notebook-container');\n", "if (notebookContainer) {{\n", "    x.observe(<PERSON><PERSON><PERSON><PERSON>, {childList: true});\n", "}}\n", "\n", "// Listen for the clearing of the current output cell\n", "var outputEl = gd.closest('.output');\n", "if (outputEl) {{\n", "    x.observe(outputEl, {childList: true});\n", "}}\n", "\n", "                        })                };                });            </script>        </div>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig = go.Figure()\n", "for i in range(len(times)):\n", "    fig.add_trace(go.<PERSON>(\n", "            x=[memory[i]],\n", "            y=[performance[i]],\n", "            mode='markers',\n", "            marker = dict(color=colors[i]),\n", "            name=experiment_names_short[i],\n", "            error_x=dict(\n", "                type='data',\n", "                symmetric=True,\n", "                array=[memory_errors[i]])))\n", "\n", "\n", "fig.update_layout(\n", "    xaxis_title='Memory usage per protein [MB] (log)',\n", "    yaxis_title='Site identification ROC-AUC',\n", "    legend_title=\"Models\",\n", ")\n", "fig.update_xaxes(type=\"log\")\n", "fig.update_layout(\n", "    xaxis = dict(\n", "        tickvals = [100,200,400,600,800,1000,2000,4000],\n", "    )\n", ")\n", "fig.show()\n", "fig.write_image('figures/mem_vs_perf.pdf')"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 2}