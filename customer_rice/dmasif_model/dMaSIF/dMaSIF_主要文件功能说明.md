# dMaSIF 主要文件功能说明

## 1. main_training.py - 训练脚本

### 主要功能
`main_training.py` 是dMaSIF模型的训练脚本，负责：

1. **模型训练**: 训练深度学习模型进行蛋白质-蛋白质相互作用预测
2. **数据加载**: 加载和预处理蛋白质表面数据
3. **验证评估**: 在训练过程中进行验证集评估
4. **模型保存**: 保存最佳性能的模型检查点

### 核心流程

#### 1.1 数据准备
```python
# 加载训练数据集
train_dataset = ProteinPairsSurfaces(
    "surface_data", ppi=args.search, train=True, transform=transformations
)

# 加载测试数据集  
test_dataset = ProteinPairsSurfaces(
    "surface_data", ppi=args.search, train=False, transform=transformations
)

# 训练/验证集划分
train_dataset, val_dataset = random_split(
    train_dataset, [train_nsamples, val_nsamples]
)
```

#### 1.2 模型初始化
```python
# 创建dMaSIF模型
net = dMaSIF(args)
net = net.to(args.device)

# 优化器设置
optimizer = torch.optim.Adam(net.parameters(), lr=3e-4, amsgrad=True)
```

#### 1.3 训练循环
```python
for i in range(starting_epoch, args.n_epochs):
    for dataset_type in ["Train", "Validation", "Test"]:
        # 执行一个epoch的训练/验证/测试
        info = iterate(
            net, dataloader, optimizer, args,
            test=(dataset_type != "Train"),
            summary_writer=writer, epoch_number=i
        )
```

### 输出指标
训练过程中记录的指标包括：
- **Loss**: 二元交叉熵损失
- **ROC-AUC**: 受试者工作特征曲线下面积
- **Distance/Positives**: 正样本距离分布
- **Distance/Negatives**: 负样本距离分布
- **Matching ROC-AUC**: 匹配任务的ROC-AUC（搜索模式）

## 2. main_inference.py - 推理脚本

### 主要功能
`main_inference.py` 是dMaSIF模型的推理脚本，负责：

1. **模型加载**: 加载预训练的模型权重
2. **数据推理**: 对测试数据进行预测
3. **结果保存**: 保存预测结果和嵌入向量
4. **性能评估**: 计算测试集上的性能指标

### 核心流程

#### 2.1 模型加载
```python
# 加载预训练模型
net = dMaSIF(args)
net.load_state_dict(
    torch.load(model_path, map_location=args.device)["model_state_dict"]
)
net = net.to(args.device)
```

#### 2.2 数据加载选项
支持三种推理模式：

1. **单个PDB文件推理**:
```python
if args.single_pdb != "":
    test_dataset = [load_protein_pair(args.single_pdb, single_data_dir, single_pdb=True)]
```

2. **PDB列表批量推理**:
```python
elif args.pdb_list != "":
    with open(args.pdb_list) as f:
        pdb_list = f.read().splitlines()
    test_dataset = [load_protein_pair(pdb, single_data_dir, single_pdb=True) for pdb in pdb_list]
```

3. **标准测试集推理**:
```python
else:
    test_dataset = ProteinPairsSurfaces(
        "surface_data", train=False, ppi=args.search, transform=transformations
    )
```

#### 2.3 推理执行
```python
# 执行推理
info = iterate(
    net, test_loader, None, args,
    test=True,
    save_path=save_predictions_path,
    pdb_ids=test_pdb_ids,
)
```

### 输出结果
推理脚本会生成：
- **预测结果**: 保存在 `preds/` 目录下
- **性能指标**: ROC-AUC、损失等评估指标
- **嵌入向量**: 蛋白质表面的特征嵌入
- **时间统计**: 卷积时间和内存使用情况

## 3. 两个脚本的区别

| 特性 | main_training.py | main_inference.py |
|------|------------------|-------------------|
| **主要目的** | 训练模型 | 使用训练好的模型进行预测 |
| **数据使用** | 训练集+验证集+测试集 | 仅测试集或指定数据 |
| **模型状态** | 训练模式，更新参数 | 评估模式，参数固定 |
| **输出** | 模型检查点、训练日志 | 预测结果、性能评估 |
| **梯度计算** | 启用梯度计算 | 禁用梯度计算 |
| **优化器** | 使用Adam优化器 | 不使用优化器 |
| **TensorBoard** | 记录训练曲线 | 不记录训练曲线 |

## 4. 使用示例

### 训练模型
```bash
python main_training.py \
    --experiment_name "dMaSIF_site_1layer_15A" \
    --site True \
    --n_epochs 50 \
    --device "cuda:0"
```

### 推理预测
```bash
# 单个PDB推理
python main_inference.py \
    --experiment_name "dMaSIF_site_1layer_15A_epoch25" \
    --single_pdb "1A2K_A_B" \
    --site True

# 批量推理
python main_inference.py \
    --experiment_name "dMaSIF_site_1layer_15A_epoch25" \
    --pdb_list "test_list.txt" \
    --site True
```

## 5. 配置参数

主要参数通过 `Arguments.py` 配置：
- `--site`: 预测相互作用位点
- `--search`: 预测蛋白质匹配
- `--embedding_layer`: 选择嵌入层类型（dMaSIF/DGCNN/PointNet++）
- `--n_layers`: 卷积层数量
- `--radius`: 卷积半径
- `--emb_dims`: 嵌入维度
