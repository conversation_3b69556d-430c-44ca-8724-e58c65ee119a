#!/usr/bin/env python3
"""
快速日志查看器 - 简单版本，不需要额外依赖
"""

import os
import glob
import struct
import sys

def read_simple_logs(experiment_name):
    """简单读取TensorBoard日志的最新值"""
    
    log_dir = os.path.join('runs', experiment_name)
    
    if not os.path.exists(log_dir):
        print(f"实验目录不存在: {log_dir}")
        print("可用的实验:")
        for exp in os.listdir('runs'):
            if os.path.isdir(os.path.join('runs', exp)):
                print(f"  - {exp}")
        return
    
    # 查找事件文件
    event_files = glob.glob(os.path.join(log_dir, "events.out.tfevents.*"))
    
    if not event_files:
        print(f"在 {log_dir} 中没有找到日志文件")
        return
    
    latest_file = max(event_files, key=os.path.getctime)
    print(f"读取日志文件: {os.path.basename(latest_file)}")
    print(f"文件大小: {os.path.getsize(latest_file)} bytes")
    print(f"修改时间: {os.path.getctime(latest_file)}")
    
    # 尝试使用tensorboard读取
    try:
        from tensorboard.backend.event_processing.event_accumulator import EventAccumulator
        
        ea = EventAccumulator(latest_file)
        ea.Reload()
        
        scalar_tags = ea.Tags()['scalars']
        print(f"\n可用指标: {len(scalar_tags)} 个")
        
        for tag in scalar_tags:
            scalar_events = ea.Scalars(tag)
            if scalar_events:
                latest_event = scalar_events[-1]
                print(f"  {tag}: {latest_event.value:.4f} (step {latest_event.step})")
        
    except ImportError:
        print("\n注意: 需要安装tensorboard来读取详细日志")
        print("运行: pip install tensorboard")
        print("\n或者使用TensorBoard Web界面:")
        print("tensorboard --logdir=runs --port=6006")

def list_all_experiments():
    """列出所有实验"""
    
    if not os.path.exists('runs'):
        print("runs目录不存在")
        return
    
    experiments = []
    for item in os.listdir('runs'):
        exp_path = os.path.join('runs', item)
        if os.path.isdir(exp_path):
            # 检查是否有日志文件
            event_files = glob.glob(os.path.join(exp_path, "events.out.tfevents.*"))
            if event_files:
                latest_file = max(event_files, key=os.path.getctime)
                size = os.path.getsize(latest_file)
                experiments.append((item, len(event_files), size))
    
    if experiments:
        print("可用的实验:")
        print(f"{'实验名称':<20} {'文件数':<8} {'最新文件大小':<12}")
        print("-" * 45)
        for name, count, size in sorted(experiments):
            print(f"{name:<20} {count:<8} {size:<12}")
    else:
        print("没有找到任何实验日志")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        experiment_name = sys.argv[1]
        read_simple_logs(experiment_name)
    else:
        print("用法: python quick_log_viewer.py <experiment_name>")
        print("或者: python quick_log_viewer.py list")
        print()
        list_all_experiments()
