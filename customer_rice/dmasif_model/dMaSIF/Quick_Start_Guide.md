# dMaSIF 快速开始指南

## 环境设置

### 1. 创建conda环境
```bash
conda create -n dmasif python=3.7
conda activate dmasif
```

### 2. 安装依赖
```bash
# 安装PyTorch (根据CUDA版本选择)
conda install pytorch==1.6.0 torchvision torchaudio cudatoolkit=10.2 -c pytorch

# 安装PyTorch Geometric
pip install torch-scatter==2.0.5 -f https://pytorch-geometric.com/whl/torch-1.6.0+cu102.html
pip install torch-sparse==0.6.8 -f https://pytorch-geometric.com/whl/torch-1.6.0+cu102.html
pip install torch-cluster==1.5.8 -f https://pytorch-geometric.com/whl/torch-1.6.0+cu102.html
pip install torch-spline-conv==1.2.0 -f https://pytorch-geometric.com/whl/torch-1.6.0+cu102.html
pip install torch-geometric==1.6.1

# 安装其他依赖
pip install pykeops==1.4.1
pip install biopython plyfile tensorboard tqdm
```

## 数据准备

### 1. 下载示例数据
```bash
# 创建数据目录
mkdir -p surface_data/raw/01-benchmark_pdbs
mkdir -p surface_data/raw/01-benchmark_surfaces

# 下载PDB文件 (示例)
python data_preprocessing/download_pdb.py
```

### 2. 数据预处理
```bash
# 转换PDB文件为numpy格式
python data_preprocessing/convert_pdb2npy.py

# 如果有PLY表面文件，也需要转换
python data_preprocessing/convert_ply2npy.py
```

## 快速训练

### 1. 位点预测任务
```bash
# 基础训练命令
python main_training.py \
    --experiment_name my_first_site_model \
    --embedding_layer dMaSIF \
    --site True \
    --single_protein True \
    --random_rotation True \
    --radius 15.0 \
    --n_layers 1 \
    --n_epochs 10 \
    --batch_size 1

# 查看训练进度
tensorboard --logdir=runs --port=6006
```

### 2. 蛋白质配对任务
```bash
python main_training.py \
    --experiment_name my_first_search_model \
    --embedding_layer dMaSIF \
    --search True \
    --random_rotation True \
    --radius 12.0 \
    --n_layers 1 \
    --n_epochs 10 \
    --batch_size 1
```

## 快速推理

### 1. 使用预训练模型
```bash
# 下载预训练模型 (如果可用)
# 或使用自己训练的模型

# 单个蛋白质推理
python main_inference.py \
    --experiment_name my_first_site_model_epoch9 \
    --single_pdb 1A0G_B \
    --site True \
    --embedding_layer dMaSIF \
    --radius 15.0 \
    --n_layers 1
```

### 2. 批量推理
```bash
# 创建PDB列表文件
echo "1A0G_B" > my_test_list.txt
echo "1A22_A" >> my_test_list.txt

# 批量推理
python main_inference.py \
    --experiment_name my_first_site_model_epoch9 \
    --pdb_list my_test_list.txt \
    --site True \
    --embedding_layer dMaSIF \
    --radius 15.0 \
    --n_layers 1
```

## 结果分析

### 1. 查看训练日志
```bash
# 使用TensorBoard
tensorboard --logdir=runs

# 或查看文本日志
python read_logs.py --experiment_name my_first_site_model
```

### 2. 分析预测结果
```bash
# 预测结果保存在 preds/ 目录下
ls preds/my_first_site_model_epoch9/

# 使用分析脚本
python data_analysis/analyse_site_outputs.py \
    --predictions_dir preds/my_first_site_model_epoch9/
```

## 常见问题解决

### 1. CUDA内存不足
```bash
# 减小批次大小
--batch_size 1

# 减小卷积半径
--radius 9.0

# 减小分辨率
--resolution 1.5
```

### 2. 训练速度慢
```bash
# 使用更少的层数
--n_layers 1

# 减小特征维度
--in_channels 8
--emb_dims 4

# 使用更简单的嵌入层
--embedding_layer PointNet++
```

### 3. 数据加载错误
```bash
# 检查数据路径
ls surface_data/processed/

# 重新预处理数据
rm -rf surface_data/processed/
python main_training.py --experiment_name test_data_loading
```

## 自定义实验

### 1. 修改网络参数
```bash
# 更深的网络
python main_training.py \
    --experiment_name deep_model \
    --n_layers 3 \
    --in_channels 32 \
    --emb_dims 16

# 更大的感受野
python main_training.py \
    --experiment_name large_radius \
    --radius 20.0 \
    --curvature_scales "[1.0, 3.0, 5.0, 10.0, 15.0]"
```

### 2. 消融实验
```bash
# 不使用化学特征
python main_training.py \
    --experiment_name no_chem \
    --no_chem True

# 不使用几何特征
python main_training.py \
    --experiment_name no_geom \
    --no_geom True
```

### 3. 基线对比
```bash
# DGCNN基线
python main_training.py \
    --experiment_name dgcnn_baseline \
    --embedding_layer DGCNN \
    --k 20

# PointNet++基线
python main_training.py \
    --experiment_name pointnet_baseline \
    --embedding_layer PointNet++
```

## 性能监控

### 1. 内存使用监控
```bash
# 启用性能分析
python main_training.py \
    --experiment_name profile_test \
    --profile True

# 查看GPU内存使用
nvidia-smi -l 1
```

### 2. 时间分析
```bash
# 分析各组件耗时
python data_analysis/profiling_surface.ipynb
```

## 可视化结果

### 1. 保存VTK格式
```python
# 在代码中添加可视化输出
from geometry_processing import save_vtk

# 保存预测结果
save_vtk("output.vtk", 
         xyz=points, 
         values=predictions, 
         vectors=normals)
```

### 2. 使用ParaView查看
```bash
# 安装ParaView
# 打开output.vtk文件进行可视化
```

## 下一步

1. **阅读完整文档**: 查看 `dMaSIF_Documentation.md`
2. **运行基准测试**: 使用 `benchmark_scripts/` 中的脚本
3. **分析结果**: 使用 `data_analysis/` 中的Jupyter notebooks
4. **自定义模型**: 修改 `model.py` 添加新功能
5. **贡献代码**: 提交改进和新功能

## 获取帮助

- **GitHub Issues**: 报告bug和请求功能
- **论文**: 阅读原始论文了解算法细节
- **代码注释**: 查看源代码中的详细注释
- **社区**: 加入相关的学术讨论组
