# dMaSIF 改进后的评估指标使用指南

## 1. 新增评估指标概览

### 1.1 已添加的指标

我们为dMaSIF项目添加了以下重要的评估指标：

#### 基础分类指标
- ✅ **准确率 (Accuracy)**: 整体分类正确率
- ✅ **精确率 (Precision)**: 预测为正的样本中实际为正的比例
- ✅ **召回率 (Recall)**: 实际为正的样本中被正确预测的比例
- ✅ **F1分数 (F1-Score)**: 精确率和召回率的调和平均数
- ✅ **特异性 (Specificity)**: 实际为负的样本中被正确预测的比例

#### 高级评估指标
- ✅ **马修斯相关系数 (MCC)**: 平衡的分类性能指标
- ✅ **PR-AUC**: 精确率-召回率曲线下面积
- ✅ **混淆矩阵**: 详细的分类结果统计

#### 可视化功能
- ✅ **混淆矩阵热图**: 直观显示分类结果
- ✅ **ROC曲线**: 受试者工作特征曲线
- ✅ **PR曲线**: 精确率-召回率曲线

## 2. 修改的文件

### 2.1 核心修改

#### `data_iteration.py`
- 添加了 `compute_detailed_metrics()` 函数
- 修改了 `iterate()` 函数中的指标计算部分
- 新增了sklearn评估指标的导入

#### `main_training.py`
- 更新了TensorBoard记录的指标列表
- 添加了混淆矩阵元素的记录

#### `main_inference.py`
- 集成了详细的评估报告生成
- 添加了总体性能统计

#### `evaluation_metrics.py` (新文件)
- 完整的评估指标计算类
- 可视化功能
- 评估报告生成

## 3. 使用方法

### 3.1 训练时的评估

训练过程中，新的指标会自动计算并记录到TensorBoard：

```bash
python main_training.py \
    --experiment_name "dMaSIF_site_enhanced" \
    --site True \
    --n_epochs 50 \
    --device "cuda:0"
```

**TensorBoard查看**:
```bash
tensorboard --logdir runs/
```

现在可以在TensorBoard中查看以下指标：
- Loss, ROC-AUC, Accuracy, Precision, Recall, F1-Score
- Specificity, MCC, PR-AUC
- ConfusionMatrix/True_Positives, False_Positives, True_Negatives, False_Negatives

### 3.2 推理时的详细评估

推理脚本现在会生成详细的评估报告：

```bash
python main_inference.py \
    --experiment_name "dMaSIF_site_enhanced_epoch25" \
    --site True \
    --device "cuda:0"
```

**输出示例**:
```
============================================================
生成详细评估报告...
============================================================

推理完成!
实验名称: dMaSIF_site_enhanced_epoch25
测试样本数: 156

平均指标:
Loss: 0.4523
ROC-AUC: 0.8234
Accuracy: 0.7845
Precision: 0.7123
Recall: 0.6789
F1-Score: 0.6951
Specificity: 0.8456
MCC: 0.5678
PR-AUC: 0.7234

总体混淆矩阵:
真阴性 (TN):   8456  |  假阳性 (FP):   1234
假阴性 (FN):   2345  |  真阳性 (TP):   5678

总体性能指标:
总体准确率: 0.7845
总体精确率: 0.8213
总体召回率: 0.7076
总体F1分数: 0.7601

评估结果已保存到: evaluation_results/dMaSIF_site_enhanced_epoch25/
```

### 3.3 独立使用评估模块

也可以独立使用评估模块对已有的预测结果进行分析：

```python
from evaluation_metrics import ModelEvaluator
import numpy as np

# 加载你的预测结果
y_true = np.load("true_labels.npy")
y_pred = np.load("predictions.npy")
y_prob = np.load("probabilities.npy")

# 创建评估器
evaluator = ModelEvaluator("my_evaluation_results")

# 生成完整报告
metrics = evaluator.generate_report(
    y_true, y_pred, y_prob,
    model_name="dMaSIF",
    dataset_name="TestSet"
)
```

## 4. 输出文件说明

### 4.1 评估结果目录结构

```
evaluation_results/
└── {experiment_name}/
    ├── {model_name}_{dataset_name}_metrics.json      # 指标JSON文件
    ├── {model_name}_{dataset_name}_confusion_matrix.png  # 混淆矩阵图
    ├── {model_name}_{dataset_name}_roc_curve.png     # ROC曲线图
    └── {model_name}_{dataset_name}_pr_curve.png      # PR曲线图
```

### 4.2 指标JSON文件格式

```json
{
  "accuracy": 0.7845,
  "precision": 0.7123,
  "recall": 0.6789,
  "f1_score": 0.6951,
  "specificity": 0.8456,
  "sensitivity": 0.6789,
  "ppv": 0.7123,
  "npv": 0.9234,
  "mcc": 0.5678,
  "roc_auc": 0.8234,
  "pr_auc": 0.7234,
  "true_negatives": 8456,
  "false_positives": 1234,
  "false_negatives": 2345,
  "true_positives": 5678
}
```

## 5. 指标解释

### 5.1 基础指标

| 指标 | 公式 | 含义 | 最佳值 |
|------|------|------|--------|
| **准确率** | (TP+TN)/(TP+TN+FP+FN) | 整体预测正确率 | 1.0 |
| **精确率** | TP/(TP+FP) | 预测为正中实际为正的比例 | 1.0 |
| **召回率** | TP/(TP+FN) | 实际为正中被预测为正的比例 | 1.0 |
| **F1分数** | 2×(Precision×Recall)/(Precision+Recall) | 精确率和召回率的调和平均 | 1.0 |
| **特异性** | TN/(TN+FP) | 实际为负中被预测为负的比例 | 1.0 |

### 5.2 高级指标

| 指标 | 含义 | 优势 | 最佳值 |
|------|------|------|--------|
| **MCC** | 马修斯相关系数 | 平衡指标，适用于不平衡数据 | 1.0 |
| **ROC-AUC** | ROC曲线下面积 | 整体分类性能 | 1.0 |
| **PR-AUC** | PR曲线下面积 | 在不平衡数据上更有意义 | 1.0 |

## 6. 最佳实践建议

### 6.1 指标选择

1. **平衡数据集**: 重点关注准确率、F1分数、ROC-AUC
2. **不平衡数据集**: 重点关注精确率、召回率、PR-AUC、MCC
3. **生物学应用**: 重点关注召回率（避免漏检）和特异性（避免误报）

### 6.2 阈值选择

```python
# 可以通过ROC曲线找到最佳阈值
from sklearn.metrics import roc_curve
import numpy as np

fpr, tpr, thresholds = roc_curve(y_true, y_prob)
# 找到最大化Youden指数的阈值
optimal_idx = np.argmax(tpr - fpr)
optimal_threshold = thresholds[optimal_idx]
```

### 6.3 模型比较

使用多个指标综合评估模型性能：

```python
# 比较不同模型
models = ['dMaSIF_v1', 'dMaSIF_v2', 'dMaSIF_enhanced']
metrics_comparison = {}

for model in models:
    metrics_file = f"evaluation_results/{model}/metrics.json"
    with open(metrics_file, 'r') as f:
        metrics_comparison[model] = json.load(f)

# 创建比较表格
import pandas as pd
df = pd.DataFrame(metrics_comparison).T
print(df[['accuracy', 'f1_score', 'roc_auc', 'pr_auc', 'mcc']])
```

## 7. 故障排除

### 7.1 常见问题

1. **ImportError**: 确保安装了所需的依赖包
```bash
pip install scikit-learn matplotlib seaborn pandas
```

2. **只有一个类别的警告**: 这是正常的，表示某些批次只包含一种标签
3. **内存不足**: 对于大型数据集，考虑分批处理评估

### 7.2 性能优化

1. **大数据集**: 使用采样进行快速评估
2. **可视化**: 可以通过参数控制是否生成图表
3. **并行处理**: 对于多个模型的比较，可以并行计算指标

## 8. 未来改进方向

1. **交叉验证**: 添加k折交叉验证支持
2. **统计检验**: 添加模型性能的统计显著性检验
3. **置信区间**: 为指标添加置信区间估计
4. **生物学指标**: 添加特定于蛋白质相互作用的评估指标
