﻿import torch
import torch.nn as nn
import numpy as np
import os
from Bio.PDB import PDBParser, PDBIO
from Bio.PDB.PDBExceptions import PDBConstructionWarning
import warnings
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import argparse
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import numpy as np
import os
from Bio.PDB import PDBParser
from Bio.PDB.PDBExceptions import PDBConstructionWarning
import warnings
from tqdm import tqdm
import matplotlib.pyplot as plt

# 忽略PDB解析警告
warnings.filterwarnings("ignore", category=PDBConstructionWarning)
# 忽略PDB解析警告
warnings.filterwarnings("ignore", category=PDBConstructionWarning)


class ProteinDataset(Dataset):
    ATOM_TYPE_MAP = {
        'C': 0, 'CA': 1, 'CB': 2, 'CD': 3, 'CD1': 4, 'CD2': 5, 'CE': 6, 'CE1': 7,
        'CE2': 8, 'CE3': 9, 'CG': 10, 'CG1': 11, 'CG2': 12, 'CH2': 13, 'CZ': 14,
        'CZ2': 15, 'CZ3': 16, 'N': 17, 'ND1': 18, 'ND2': 19, 'NE': 20, 'NE1': 21,
        'NE2': 22, 'NH1': 23, 'NH2': 24, 'NZ': 25, 'O': 26, 'OD1': 27, 'OD2': 28,
        'OE1': 29, 'OE2': 30, 'OG': 31, 'OG1': 32, 'OH': 33, 'SD': 34, 'SG': 35,
        'H': 36, 'HA': 37, 'HB': 38, 'HD': 39, 'HE': 40, 'HH': 41, 'HZ': 42
    }

    def __init__(self, pdb_dir, max_atoms=1000):
        self.pdb_files = [
            os.path.join(pdb_dir, f)
            for f in os.listdir(pdb_dir) if f.endswith('.pdb')
        ]
        self.max_atoms = max_atoms
        self.parser = PDBParser(QUIET=True)
        print(f"Found {len(self.pdb_files)} PDB files.")

    def __len__(self):
        return len(self.pdb_files)

    def __getitem__(self, idx):
        path = self.pdb_files[idx]
        try:
            structure = self.parser.get_structure("prot", path)
            atoms = [atom for model in structure
                           for chain in model
                           for residue in chain
                           for atom in residue]
            coords = []
            types = []
            for atom in atoms:
                coords.append(atom.get_coord())
                t = atom.get_name().strip()
                types.append(self.ATOM_TYPE_MAP.get(t, len(self.ATOM_TYPE_MAP)))
            coords = np.array(coords, dtype=np.float32)
            types  = np.array(types, dtype=np.int64)

            # 截断或填充
            N = len(coords)
            if N > self.max_atoms:
                idxs = np.random.choice(N, self.max_atoms, replace=False)
                coords, types = coords[idxs], types[idxs]
                mask = np.ones(self.max_atoms, dtype=bool)
            else:
                pad = self.max_atoms - N
                coords = np.pad(coords, ((0,pad),(0,0)), 'constant', constant_values=0)
                types  = np.pad(types, (0,pad), 'constant', constant_values=len(self.ATOM_TYPE_MAP))
                mask = np.concatenate([np.ones(N, dtype=bool), np.zeros(pad, dtype=bool)])

            # 归一化
            mu = coords.mean(axis=0)
            st = coords.std(axis=0) + 1e-8
            coords = (coords - mu) / st

            return {
                'coords': torch.tensor(coords),
                'types':  torch.tensor(types),
                'mask':   torch.tensor(mask)
            }
        except Exception as e:
            # 出错时返回全零
            return {
                'coords': torch.zeros(self.max_atoms,3),
                'types':  torch.zeros(self.max_atoms,dtype=torch.long),
                'mask':   torch.zeros(self.max_atoms,dtype=torch.bool)
            }


class VectorQuantizer(nn.Module):
    def __init__(self, num_embeddings, embedding_dim, commitment_cost=0.25):
        super().__init__()
        self.num_embeddings = num_embeddings
        self.embedding_dim = embedding_dim
        self.commitment_cost = commitment_cost

        self.embeddings = nn.Embedding(num_embeddings, embedding_dim)
        nn.init.uniform_(self.embeddings.weight, -1.0, 1.0)

    def forward(self, inputs, mask=None):
        # inputs: [B,N,E]
        B, N, E = inputs.shape
        flat = inputs.view(-1, E)  # [B*N, E]

        # 计算距离
        d_sq = (flat.pow(2).sum(dim=1, keepdim=True)
               + self.embeddings.weight.pow(2).sum(dim=1)
               - 2 * flat @ self.embeddings.weight.t())
        encoding_inds = torch.argmin(d_sq, dim=1)
        quant = self.embeddings(encoding_inds).view(B, N, E)

        # straight-through
        quant_st = inputs + (quant - inputs).detach()

        # losses
        e_latent = F.mse_loss(quant.detach(), inputs)
        q_latent = F.mse_loss(quant, inputs.detach())
        loss = q_latent + self.commitment_cost * e_latent

        return quant_st, loss, encoding_inds.view(B, N)

class ProteinVQVAE(nn.Module):
    def __init__(self, num_atom_types, embedding_dim=128, num_embeddings=512, commitment=0.25):
        super().__init__()
        self.num_atom_types = num_atom_types
        
        # 编码器部分保持不变...
        self.atom_emb = nn.Embedding(num_atom_types+1, embedding_dim)
        self.coord_net = nn.Sequential(
            nn.Linear(3, embedding_dim),
            nn.ReLU(),
            nn.LayerNorm(embedding_dim)
        )
        self.fuse = nn.Sequential(
            nn.Linear(2*embedding_dim, embedding_dim),
            nn.ReLU(),
            nn.LayerNorm(embedding_dim)
        )
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=embedding_dim, nhead=8, dim_feedforward=512, batch_first=True
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=4)
        self.proj = nn.Sequential(
            nn.Linear(embedding_dim, embedding_dim),
            nn.ReLU(),
            nn.LayerNorm(embedding_dim)
        )
        self.vq = VectorQuantizer(num_embeddings, embedding_dim, commitment)

        # 修改解码器：同时输出坐标和原子类型
        self.decoder = nn.Sequential(
            nn.Linear(embedding_dim, 2 * embedding_dim),
            nn.ReLU(),
            nn.LayerNorm(2 * embedding_dim)
        )
        self.coord_head = nn.Linear(2 * embedding_dim, 3)  # 坐标输出层
        self.type_head = nn.Linear(2 * embedding_dim, num_atom_types+1)  # 原子类型输出层

    def forward(self, coords, types, mask=None):
        # 编码过程保持不变...
        t = self.atom_emb(types)           # [B,N,E]
        c = self.coord_net(coords)         # [B,N,E]
        x = self.fuse(torch.cat([t, c], dim=-1))
        pad_mask = ~mask if mask is not None else None
        h = self.transformer(x, src_key_padding_mask=pad_mask)
        h = self.proj(h)
        quant, vq_loss, codes = self.vq(h, mask)
        
        # 解码过程
        dec_out = self.decoder(quant)
        recon_coords = self.coord_head(dec_out)  # [B,N,3]
        logits = self.type_head(dec_out)        # [B,N,num_atom_types+1]
        
        return recon_coords, logits, vq_loss, codes
# 原子类型映射（必须与训练时一致）
ATOM_TYPE_MAP = {
    'C': 0, 'CA': 1, 'CB': 2, 'CD': 3, 'CD1': 4, 'CD2': 5, 'CE': 6, 'CE1': 7,
    'CE2': 8, 'CE3': 9, 'CG': 10, 'CG1': 11, 'CG2': 12, 'CH2': 13, 'CZ': 14,
    'CZ2': 15, 'CZ3': 16, 'N': 17, 'ND1': 18, 'ND2': 19, 'NE': 20, 'NE1': 21,
    'NE2': 22, 'NH1': 23, 'NH2': 24, 'NZ': 25, 'O': 26, 'OD1': 27, 'OD2': 28,
    'OE1': 29, 'OE2': 30, 'OG': 31, 'OG1': 32, 'OH': 33, 'SD': 34, 'SG': 35,
    'H': 36, 'HA': 37, 'HB': 38, 'HD': 39, 'HE': 40, 'HH': 41, 'HZ': 42
}

REVERSE_ATOM_MAP = {v: k for k, v in ATOM_TYPE_MAP.items()}

def process_pdb_file(pdb_file, max_atoms=30000):
    """处理单个PDB文件，返回标准化后的数据和原始统计信息"""
    parser = PDBParser(QUIET=True)
    structure = parser.get_structure("prot", pdb_file)
    
    # 提取所有原子
    atoms = []
    coords = []
    types = []
    for model in structure:
        for chain in model:
            for residue in chain:
                for atom in residue:
                    atoms.append(atom)
                    coords.append(atom.get_coord())
                    atom_name = atom.get_name().strip()
                    types.append(ATOM_TYPE_MAP.get(atom_name, len(ATOM_TYPE_MAP)))
    
    coords = np.array(coords, dtype=np.float32)
    types = np.array(types, dtype=np.int64)
    
    # 保存原始统计信息
    mu = coords.mean(axis=0)
    std = coords.std(axis=0) + 1e-8
    original_coords = coords.copy()
    
    # 随机采样（如果原子数超过max_atoms）
    N = len(coords)
    if N > max_atoms:
        idxs = np.random.choice(N, max_atoms, replace=False)
        coords = coords[idxs]
        types = types[idxs]
        mask = np.ones(max_atoms, dtype=bool)
        sampled_atoms = [atoms[i] for i in idxs]
    else:
        pad = max_atoms - N
        coords = np.pad(coords, ((0, pad), (0, 0)), 'constant', constant_values=0)
        types = np.pad(types, (0, pad), 'constant', constant_values=len(ATOM_TYPE_MAP))
        mask = np.concatenate([np.ones(N, dtype=bool), np.zeros(pad, dtype=bool)])
        sampled_atoms = atoms
    
    # 标准化坐标
    normalized_coords = (coords - mu) / std
    
    return {
        'coords': torch.tensor(normalized_coords),
        'types': torch.tensor(types),
        'mask': torch.tensor(mask),
        'original_coords': original_coords,
        'sampled_atoms': sampled_atoms,
        'mu': mu,
        'std': std
    }

def visualize_comparison(original_coords, reconstructed_coords, title, save_path=None):
    """可视化原始结构和重构结构的3D坐标对比"""
    fig = plt.figure(figsize=(12, 6))
    
    # 原始结构
    ax1 = fig.add_subplot(121, projection='3d')
    ax1.scatter(original_coords[:, 0], original_coords[:, 1], original_coords[:, 2], 
               s=10, alpha=0.5, c='b', label='Original')
    ax1.set_title('Original Structure')
    ax1.set_xlabel('X')
    ax1.set_ylabel('Y')
    ax1.set_zlabel('Z')
    ax1.legend()
    
    # 重构结构
    ax2 = fig.add_subplot(122, projection='3d')
    ax2.scatter(reconstructed_coords[:, 0], reconstructed_coords[:, 1], reconstructed_coords[:, 2], 
               s=10, alpha=0.5, c='r', label='Reconstructed')
    ax2.set_title('Reconstructed Structure')
    ax2.set_xlabel('X')
    ax2.set_ylabel('Y')
    ax2.set_zlabel('Z')
    ax2.legend()
    
    plt.suptitle(title)
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300)
        print(f"Saved visualization to {save_path}")
    else:
        plt.show()
    
    plt.close()

def group_atoms_by_code(codes, valid_mask):
    """根据VQ编码严格分组原子"""
    # 提取有效原子的编码
    valid_codes = codes[0][valid_mask].cpu().numpy()
    
    # 创建分组字典
    groups = {}
    for i, code in enumerate(valid_codes):
        if code not in groups:
            groups[code] = []
        groups[code].append(i)
    
    # 按组大小排序（从大到小）
    sorted_groups = sorted(groups.items(), key=lambda x: len(x[1]), reverse=True)
    
    # 创建组ID映射
    group_ids = np.zeros(len(valid_codes), dtype=int) - 1
    for group_id, (code, indices) in enumerate(sorted_groups):
        for idx in indices:
            group_ids[idx] = group_id
    
    return group_ids, sorted_groups

def visualize_code_groups(original_coords, group_ids, groups, title, save_path=None):
    """
    可视化基于VQ编码的分组
    """
    fig = plt.figure(figsize=(10, 8))
    ax = fig.add_subplot(111, projection='3d')
    
    # 获取唯一组ID
    unique_groups = np.unique(group_ids)
    num_groups = len(unique_groups)
    
    # 生成颜色映射
    if num_groups > 1:
        colors = plt.cm.tab20(np.linspace(0, 1, min(num_groups, 20)))
    else:
        colors = ['blue']
    
    # 为每个组绘制点
    for group_id in unique_groups:
        if group_id == -1:  # 未分组原子
            continue
            
        # 获取该组原子索引
        mask = (group_ids == group_id)
        group_coords = original_coords[mask]
        
        # 选择颜色（如果组太多，循环使用颜色）
        color_idx = group_id % len(colors)
        
        # 获取组信息
        group_info = groups[group_id]
        group_size = len(group_info[1])
        code_value = group_info[0]  # 原始VQ编码值
        
        # 绘制点
        ax.scatter(group_coords[:, 0], group_coords[:, 1], group_coords[:, 2],
                  s=15, alpha=0.7, color=colors[color_idx], 
                  label=f'Group {group_id}: Code {code_value} (n={group_size})')
    
    # 绘制未分组原子（如果有）
    ungrouped_mask = (group_ids == -1)
    if np.any(ungrouped_mask):
        ungrouped_coords = original_coords[ungrouped_mask]
        ax.scatter(ungrouped_coords[:, 0], ungrouped_coords[:, 1], ungrouped_coords[:, 2],
                  s=10, alpha=0.3, c='gray', label='Ungrouped')
    
    ax.set_title(f"{title}\nTotal Groups: {num_groups} | Total Atoms: {len(original_coords)}")
    ax.set_xlabel('X')
    ax.set_ylabel('Y')
    ax.set_zlabel('Z')
    
    # 添加图例（最多显示20个组）
    if num_groups <= 20:
        ax.legend(loc='best', fontsize='small', markerscale=0.8)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300)
        print(f"Saved VQ code group visualization to {save_path}")
    else:
        plt.show()
    
    plt.close()
    
    return fig

def visualize_individual_code_groups(original_coords, group_ids, groups, base_path, structure_name):
    """
    为每个VQ编码组生成单独的3D可视化
    """
    # 创建输出目录
    group_dir = os.path.join(base_path, f"{structure_name}_code_groups")
    os.makedirs(group_dir, exist_ok=True)
    
    # 获取唯一组ID
    unique_groups = np.unique(group_ids)
    unique_groups = [g for g in unique_groups if g != -1]  # 排除未分组
    
    print(f"Generating individual group visualizations for {len(unique_groups)} VQ code groups...")
    
    # 为每个组创建可视化
    for group_id in unique_groups:
        # 获取组信息
        group_info = groups[group_id]
        group_indices = group_info[1]
        code_value = group_info[0]
        group_size = len(group_indices)
        
        # 创建新图
        fig = plt.figure(figsize=(10, 8))
        ax = fig.add_subplot(111, projection='3d')
        
        # 绘制整个结构（浅灰色背景）
        ax.scatter(original_coords[:, 0], original_coords[:, 1], original_coords[:, 2], 
                  s=5, alpha=0.1, c='lightgray', label='Other atoms')
        
        # 高亮当前组的原子
        group_coords = original_coords[group_indices]
        ax.scatter(group_coords[:, 0], group_coords[:, 1], group_coords[:, 2],
                  s=30, alpha=0.8, c='red', label=f'Group {group_id}: Code {code_value} (n={group_size})')
        
        # 设置标题和标签
        ax.set_title(f'Structure: {structure_name}\nVQ Code Group: {group_id} (Code: {code_value}, Size: {group_size})')
        ax.set_xlabel('X')
        ax.set_ylabel('Y')
        ax.set_zlabel('Z')
        ax.legend()
        
        # 保存图像
        save_path = os.path.join(group_dir, f"code_group_{group_id}.png")
        plt.tight_layout()
        plt.savefig(save_path, dpi=200)
        plt.close()
    
    # 创建组概览图（所有组在一张图上）
    fig = visualize_code_groups(
        original_coords, group_ids, groups, 
        f"All VQ Code Groups: {structure_name}"
    )
    overview_path = os.path.join(group_dir, "all_groups_overview.png")
    fig.savefig(overview_path, dpi=300)
    plt.close(fig)
    
    print(f"Saved {len(unique_groups)} VQ code group visualizations to {group_dir}")
    
    return group_dir

def save_reconstructed_pdb(recon_coords, atom_types, atoms, output_path):
    """将重构的结构保存为PDB文件"""
    io = PDBIO()
    
    # 创建一个新的结构
    structure = PDBParser(QUIET=True).get_structure("reconstructed", "dummy.pdb")
    model = structure[0]
    chain = model["A"]
    
    # 为每个原子创建一个残基
    for i, (coord, atom_type_idx) in enumerate(zip(recon_coords, atom_types)):
        if i >= len(atoms):  # 填充部分跳过
            continue
        
        # 获取原始原子的残基信息
        original_atom = atoms[i]
        residue = original_atom.get_parent()
        
        # 创建新原子
        atom_name = REVERSE_ATOM_MAP.get(atom_type_idx.item(), "UNK")
        new_atom = original_atom.copy()
        new_atom.coord = coord
        new_atom.name = atom_name
        
        # 添加到结构
        if i == 0 or (i > 0 and atoms[i].get_parent() != atoms[i-1].get_parent()):
            new_residue = residue.copy()
            new_residue.add(new_atom)
            chain.add(new_residue)
        else:
            new_residue.add(new_atom)
    
    # 保存PDB文件
    io.set_structure(structure)
    io.save(output_path)
    print(f"Saved reconstructed PDB to {output_path}")
# python vqvae_visual.py --pdb_path /home/<USER>/code/MAPE-PPI/data/raw_data/Arabidopsis_pdb --model_path protein_vqvae.pth 
def main():
    parser = argparse.ArgumentParser(description='Visualize PDB reconstruction using VQ-VAE')
    parser.add_argument('--pdb_path', type=str, required=True, 
                       help='Path to PDB file or directory containing PDB files')
    parser.add_argument('--model_path', type=str, required=True,
                       help='Path to trained VQ-VAE model')
    parser.add_argument('--output_dir', type=str, default='reconstructions',
                       help='Output directory for visualizations and PDBs')
    parser.add_argument('--max_atoms', type=int, default=30000,
                       help='Maximum number of atoms to process')
    parser.add_argument('--device', type=str, default='cuda' if torch.cuda.is_available() else 'cpu',
                       help='Device to use for inference')
    parser.add_argument('--visualize_individual', action='store_true',
                       help='Generate individual visualizations for each VQ code group')
    args = parser.parse_args()

    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 加载模型
    num_atom_types = len(ATOM_TYPE_MAP)
    model = ProteinVQVAE(
        num_atom_types=num_atom_types,
        embedding_dim=256,
        num_embeddings=1024,
        commitment=0.25
    ).to(args.device)
    
    model.load_state_dict(torch.load(args.model_path, map_location=args.device))
    model.eval()
    print(f"Loaded model from {args.model_path}")
    
    # 处理PDB文件或目录
    pdb_files = []
    if os.path.isdir(args.pdb_path):
        for f in os.listdir(args.pdb_path):
            if f.endswith('.pdb'):
                pdb_files.append(os.path.join(args.pdb_path, f))
    else:
        pdb_files.append(args.pdb_path)
    
    print(f"Found {len(pdb_files)} PDB files to process")
    
    for pdb_file in pdb_files:
        file_name = os.path.splitext(os.path.basename(pdb_file))[0]
        print(f"\nProcessing {file_name}...")
        
        # 处理PDB文件
        data = process_pdb_file(pdb_file, max_atoms=args.max_atoms)
        
        # 准备输入
        coords = data['coords'].unsqueeze(0).to(args.device)  # 添加batch维度
        types = data['types'].unsqueeze(0).to(args.device)
        mask = data['mask'].unsqueeze(0).to(args.device)
        
        # 通过模型
        with torch.no_grad():
            recon_coords, logits, _, codes = model(coords, types, mask)
        
        # 只取真实原子（非填充部分）
        valid_mask = mask.cpu().numpy()[0]
        original_coords = data['original_coords']
        sampled_atoms = data['sampled_atoms']
        
        # 反标准化重构坐标
        mu = data['mu']
        std = data['std']
        recon_coords_np = recon_coords.cpu().numpy()[0][valid_mask]
        recon_coords_denorm = recon_coords_np * std + mu
        
        # 获取预测的原子类型
        pred_types = torch.argmax(logits, dim=-1).cpu().numpy()[0][valid_mask]
        
        # 根据VQ编码分组原子
        group_ids, groups = group_atoms_by_code(codes, valid_mask)
        
        # 打印分组统计信息
        num_groups = len(groups)
        group_sizes = [len(indices) for _, indices in groups]
        print(f"Created {num_groups} VQ code groups")
        print(f"Group sizes: min={min(group_sizes)}, max={max(group_sizes)}, avg={np.mean(group_sizes):.1f}")
        
        # 验证相同编码的输出一致性
        validate_output_consistency(groups, recon_coords_denorm, pred_types)
        
        # 可视化VQ编码分组
        code_group_path = os.path.join(args.output_dir, f"{file_name}_code_groups.png")
        fig = visualize_code_groups(
            original_coords=original_coords,
            group_ids=group_ids,
            groups=groups,
            title=f"VQ Code Groups: {file_name}",
            save_path=code_group_path
        )
        
        # 可视化对比
        vis_path = os.path.join(args.output_dir, f"{file_name}_comparison.png")
        visualize_comparison(
            original_coords=original_coords,
            reconstructed_coords=recon_coords_denorm,
            title=f"Structure Comparison: {file_name}",
            save_path=vis_path
        )
        
        # 为每个组生成单独的可视化
        if args.visualize_individual:
            group_dir = visualize_individual_code_groups(
                original_coords=original_coords,
                group_ids=group_ids,
                groups=groups,
                base_path=args.output_dir,
                structure_name=file_name
            )
            print(f"Individual VQ code group visualizations saved to: {group_dir}")
        
        # 保存重构的PDB
        # pdb_path = os.path.join(args.output_dir, f"{file_name}_reconstructed.pdb")
        # save_reconstructed_pdb(recon_coords_denorm, pred_types, sampled_atoms, pdb_path)

def validate_output_consistency(groups, recon_coords, pred_types):
    """验证相同编码原子的输出一致性"""
    print("Validating output consistency for same-code atoms...")
    
    for group_id, (code, indices) in enumerate(groups):
        if len(indices) < 2:
            continue
        
        # 获取相同编码原子的重建坐标
        group_coords = recon_coords[indices]
        
        # 计算坐标差异
        coord_std = np.std(group_coords, axis=0)
        max_coord_diff = np.max(coord_std)
        
        # 获取原子类型预测
        group_types = pred_types[indices]
        unique_types = np.unique(group_types)
        type_diff = len(unique_types)
        
        # 打印结果
        print(f"Group {group_id} (Code {code}, Size {len(indices)}): "
              f"Max coord diff: {max_coord_diff:.6f} | "
              f"Type diff: {type_diff}")
        
        # 如果有类型差异，打印详细信息
        if type_diff > 1:
            print(f"  Warning: {type_diff} different atom types in same-code group!")
            print(f"  Types present: {[REVERSE_ATOM_MAP.get(t, f'UNK({t})') for t in unique_types]}")
    
    print("Output consistency validation complete.\n")

if __name__ == "__main__":
    main()