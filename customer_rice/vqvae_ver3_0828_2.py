# VQ-VAE + GIN PPI Pipeline (End-to-End, Hybrid v3.0815/v3.0812)
# ===================================================================

# This script implements an end-to-end pipeline for protein-protein
# interaction (PPI) prediction.
# It is based on the end-to-end architecture of vqvae_ver3_0812_2.py.

# Modifications:
# - Uses the ProteinSurfaceDataset from vqvae_ver3_0815.py.
# - Uses the VQ-VAE model parameters (embedding dims, transformer settings)
#   from vqvae_ver3_0815.py for both peptide and protein encoders.
# - The core end-to-end training loop and composite loss function from
#   vqvae_ver3_0812_2.py are preserved.

# Author: Generated for customer_rice project
# Date: 2024-08-28

import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import numpy as np
import os
from Bio.PDB import PDBParser
from Bio.PDB.PDBExceptions import PDBConstructionWarning
import warnings
from tqdm import tqdm
import matplotlib.pyplot as plt
import dgl
from dgl.nn.pytorch import GINConv
from torch.utils.tensorboard import SummaryWriter
from sklearn.metrics import confusion_matrix, roc_auc_score, accuracy_score
import itertools
import pandas as pd
from pathlib import Path
from sklearn.model_selection import train_test_split

import torch.multiprocessing as mp
from torch.utils.checkpoint import checkpoint


# Import dMaSIF components
from dmasif_model.dMaSIF.geometry_processing import atoms_to_points_normals

# Ignore PDB parsing warnings
warnings.filterwarnings("ignore", category=PDBConstructionWarning)


# --- Data Loading (from vqvae_ver3_0815.py) ---

ele2num = {"C": 0, "H": 1, "O": 2, "N": 3, "S": 4, "SE": 5}

def convert_pdbs_to_npy(pdb_dir, npy_dir):
    """Converts all PDB files in a directory to .npy files for faster loading, skipping existing ones."""
    print(f"Converting PDBs from {pdb_dir} to npy format in {npy_dir}...")
    npy_dir.mkdir(parents=True, exist_ok=True)
    parser = PDBParser()
    for p in tqdm(list(pdb_dir.glob("*.pdb"))):
        target_xyz_path = npy_dir / (p.stem + "_atomxyz.npy")
        target_types_path = npy_dir / (p.stem + "_atomtypes.npy")
        if target_xyz_path.exists() and target_types_path.exists():
            continue
        try:
            structure = parser.get_structure("structure", p)
            atoms = structure.get_atoms()

            coords = []
            types = []
            for atom in atoms:
                coords.append(atom.get_coord())
                types.append(ele2num.get(atom.element.strip().upper(), 5))

            coords = np.stack(coords)
            types_array = np.zeros((len(types), len(ele2num)))
            for i, t in enumerate(types):
                types_array[i, t] = 1.0
            
            np.save(target_xyz_path, coords)
            np.save(target_types_path, types_array)
        except Exception as e:
            print(f"Could not process {p.name}: {e}")


class ProteinSurfaceDataset(Dataset):
    def __init__(self, npy_dir, max_surface_points=2000, subset_ids=None, device='cpu'):
        all_files = list(npy_dir.glob("*_atomxyz.npy"))
        if subset_ids:
            self.npy_files = [f for f in all_files if f.stem.replace("_atomxyz", "") in subset_ids]
        else:
            self.npy_files = all_files
            
        self.max_surface_points = max_surface_points
        self.device = device

    def __len__(self):
        return len(self.npy_files)

    def __getitem__(self, idx):
        xyz_path = self.npy_files[idx]
        types_path = xyz_path.with_name(xyz_path.name.replace("_atomxyz.npy", "_atomtypes.npy"))
        protein_id = xyz_path.stem.replace("_atomxyz", "")

        try:
            atom_coords_np = np.load(xyz_path)
            atom_types_np = np.load(types_path)

            atoms_tensor = torch.tensor(atom_coords_np, dtype=torch.float32)
            atomtypes_tensor = torch.tensor(atom_types_np, dtype=torch.float32)
            batch_tensor = torch.zeros(atoms_tensor.shape[0], dtype=torch.long)

            # Use the device passed during initialization
            device = self.device
            atoms_tensor = atoms_tensor.to(device)
            atomtypes_tensor = atomtypes_tensor.to(device)
            batch_tensor = batch_tensor.to(device)

            surface_points, surface_normals, _ = atoms_to_points_normals(
                atoms_tensor,
                batch_tensor,
                distance=1.05,
                smoothness=0.5,
                resolution=3.5,
                nits=4,
                atomtypes=atomtypes_tensor,
                sup_sampling=5,
                variance=0.1
            )

            N_surface = surface_points.shape[0]

            if N_surface > 0:
                distances = torch.cdist(surface_points, atoms_tensor)
                nearest_atom_indices = torch.argmin(distances, dim=1)
                surface_atom_types = atomtypes_tensor[nearest_atom_indices]
            else:
                surface_atom_types = torch.zeros(0, len(ele2num), device=device)

            mask = torch.ones(N_surface, dtype=torch.bool, device=device)
            if N_surface > self.max_surface_points:
                idxs = np.random.choice(N_surface, self.max_surface_points, replace=False)
                surface_points = surface_points[idxs]
                surface_normals = surface_normals[idxs]
                surface_atom_types = surface_atom_types[idxs]
                mask = torch.ones(self.max_surface_points, dtype=torch.bool, device=device)
            else:
                pad = self.max_surface_points - N_surface
                surface_points = F.pad(surface_points, (0, 0, 0, pad), 'constant', 0)
                surface_normals = F.pad(surface_normals, (0, 0, 0, pad), 'constant', 0)
                surface_atom_types = F.pad(surface_atom_types, (0, 0, 0, pad), 'constant', 0)
                mask = F.pad(mask, (0, pad), 'constant', False)

            if mask.any():
                mu_coords = surface_points[mask].mean(dim=0)
                st_coords = surface_points[mask].std(dim=0) + 1e-8
                surface_points[mask] = (surface_points[mask] - mu_coords) / st_coords

            return {
                'coords': surface_points.cpu(),
                'normals': surface_normals.cpu(),
                'atom_types': surface_atom_types.cpu(),
                'mask': mask.cpu(),
                'id': protein_id
            }
        except Exception as e:
            return {
                'coords': torch.zeros(self.max_surface_points, 3),
                'normals': torch.zeros(self.max_surface_points, 3),
                'atom_types': torch.zeros(self.max_surface_points, len(ele2num)),
                'mask': torch.zeros(self.max_surface_points, dtype=torch.bool),
                'id': 'error'
            }

# --- VQVAE Model (from vqvae_ver3_0812_2.py) ---

class VectorQuantizer(nn.Module):
    def __init__(self, num_embeddings, embedding_dim, commitment_cost=0.25):
        super().__init__()
        self.num_embeddings = num_embeddings
        self.embedding_dim = embedding_dim
        self.commitment_cost = commitment_cost
        self.embeddings = nn.Embedding(num_embeddings, embedding_dim)
        nn.init.uniform_(self.embeddings.weight, -1.0, 1.0)

    def forward(self, inputs, mask=None):
        B, N, E = inputs.shape
        flat = inputs.view(-1, E)
        d_sq = (flat.pow(2).sum(dim=1, keepdim=True)
               + self.embeddings.weight.pow(2).sum(dim=1)
               - 2 * flat @ self.embeddings.weight.t())
        encoding_inds = torch.argmin(d_sq, dim=1)
        quant = self.embeddings(encoding_inds).view(B, N, E)
        quant_st = inputs + (quant - inputs).detach()
        e_latent = F.mse_loss(quant.detach(), inputs)
        q_latent = F.mse_loss(quant, inputs.detach())
        loss = q_latent + self.commitment_cost * e_latent
        return quant_st, loss, encoding_inds.view(B, N)

class VQVAE(nn.Module):
    def __init__(self, embedding_dim=128, num_embeddings=512, commitment=0.25, nhead=8, dim_feedforward=512, num_layers=2, use_checkpointing=True):
        super().__init__()
        self.use_checkpointing = use_checkpointing
        self.coord_net = nn.Sequential(nn.Linear(3, embedding_dim), nn.ReLU(), nn.LayerNorm(embedding_dim))
        self.normal_net = nn.Sequential(nn.Linear(3, embedding_dim), nn.ReLU(), nn.LayerNorm(embedding_dim))
        self.fuse = nn.Sequential(nn.Linear(2 * embedding_dim, embedding_dim), nn.ReLU(), nn.LayerNorm(embedding_dim))
        encoder_layer = nn.TransformerEncoderLayer(d_model=embedding_dim, nhead=nhead, dim_feedforward=dim_feedforward, batch_first=True)
        self.transformer_encoder = nn.TransformerEncoder(encoder_layer, num_layers=num_layers)
        self.proj = nn.Sequential(nn.Linear(embedding_dim, embedding_dim), nn.ReLU(), nn.LayerNorm(embedding_dim))
        self.vq = VectorQuantizer(num_embeddings, embedding_dim, commitment)
        decoder_layer = nn.TransformerEncoderLayer(d_model=embedding_dim, nhead=nhead, dim_feedforward=dim_feedforward, batch_first=True)
        self.transformer_decoder = nn.TransformerEncoder(decoder_layer, num_layers=num_layers)
        self.decode_proj = nn.Sequential(nn.Linear(embedding_dim, embedding_dim), nn.ReLU(), nn.LayerNorm(embedding_dim))
        self.decode_coords = nn.Linear(embedding_dim, 3)
        self.decode_normals = nn.Linear(embedding_dim, 3)

    def _run_transformer_encoder(self, x, mask):
        return self.transformer_encoder(x, src_key_padding_mask=mask)

    def _run_transformer_decoder(self, x, mask):
        return self.transformer_decoder(x, src_key_padding_mask=mask)

    def encode(self, coords, normals, mask=None):
        c_feat = self.coord_net(coords)
        n_feat = self.normal_net(normals)
        x = self.fuse(torch.cat([c_feat, n_feat], dim=-1))
        pad_mask = ~mask if mask is not None else None
        
        if self.use_checkpointing and self.training:
            h = checkpoint(self._run_transformer_encoder, x, pad_mask, use_reentrant=False)
        else:
            h = self.transformer_encoder(x, src_key_padding_mask=pad_mask)

        h = self.proj(h)
        return h

    def decode(self, quant, mask=None):
        pad_mask = ~mask if mask is not None else None

        if self.use_checkpointing and self.training:
            h = checkpoint(self._run_transformer_decoder, quant, pad_mask, use_reentrant=False)
        else:
            h = self.transformer_decoder(quant, src_key_padding_mask=pad_mask)

        h = self.decode_proj(h)
        reconstructed_coords = self.decode_coords(h)
        reconstructed_normals = self.decode_normals(h)
        return reconstructed_coords, reconstructed_normals

    def forward(self, coords, normals, mask=None):
        h = self.encode(coords, normals, mask)
        quant, vq_loss, encoding_inds = self.vq(h, mask)
        reconstructed_coords, reconstructed_normals = self.decode(quant, mask)
        if mask is not None:
            protein_embedding = (quant * mask.unsqueeze(-1)).sum(dim=1) / (mask.sum(dim=1).unsqueeze(-1) + 1e-8)
        else:
            protein_embedding = quant.mean(dim=1)
        return reconstructed_coords, reconstructed_normals, vq_loss, protein_embedding, encoding_inds

# --- GIN Model (from vqvae_ver3_0812_2.py) ---

class GIN(torch.nn.Module):
    def __init__(self,  param, use_checkpointing=True):
        super(GIN, self).__init__()
        self.use_checkpointing = use_checkpointing
        self.num_layers = param['ppi_num_layers']
        self.dropout = nn.Dropout(param['dropout_ratio'])
        self.layers = nn.ModuleList()
        self.layers.append(GINConv(nn.Sequential(nn.Linear(param['prot_hidden_dim'], param['ppi_hidden_dim']), nn.ReLU(), nn.Linear(param['ppi_hidden_dim'], param['ppi_hidden_dim']), nn.ReLU(), nn.BatchNorm1d(param['ppi_hidden_dim'])), aggregator_type='sum', learn_eps=True))
        for i in range(self.num_layers - 1):
            self.layers.append(GINConv(nn.Sequential(nn.Linear(param['ppi_hidden_dim'], param['ppi_hidden_dim']), nn.ReLU(), nn.Linear(param['ppi_hidden_dim'], param['ppi_hidden_dim']), nn.ReLU(), nn.BatchNorm1d(param['ppi_hidden_dim'])), aggregator_type='sum', learn_eps=True))
        self.linear = nn.Linear(param['ppi_hidden_dim'], param['ppi_hidden_dim'])
        self.fc = nn.Linear(param['ppi_hidden_dim'], param['output_dim'])

    def forward(self, g, x, ppi_list, idx):
        for l, layer in enumerate(self.layers):
            if self.use_checkpointing and self.training:
                x = checkpoint(layer, g, x, use_reentrant=False)
            else:
                x = layer(g, x)
            x = self.dropout(x)
        x = F.dropout(F.relu(self.linear(x)), p=0.5, training=self.training)
        node_id = np.array(ppi_list)[idx]
        x1 = x[node_id[:, 0]]
        x2 = x[node_id[:, 1]]
        x = self.fc(torch.mul(x1, x2))
        return x

# --- PPI Prediction Model ---

class PPI_Prediction(nn.Module):
    def __init__(self, gin_param):
        super().__init__()
        use_checkpointing = gin_param.get('use_checkpointing', True)
        self.gin = GIN(gin_param, use_checkpointing=use_checkpointing)

    def forward(self, ppi_graph, protein_embeddings, ppi_list, ppi_idx):
        return self.gin(ppi_graph, protein_embeddings, ppi_list, ppi_idx)

# --- Evaluation Function ---
@torch.no_grad()
def evaluate(models, data_loaders, ppi_graph, all_pairs, labels_tensor, eval_idx, devices, protein_id_map, gin_input_dim):
    """Runs a full evaluation loop for a given dataset split (e.g., validation or test)."""
    vqvae_peptide, vqvae_protein, model, peptide_projection, protein_projection = models
    peptide_loader, protein_loader = data_loaders
    device_pep, device_prot, device_gin = devices

    # Set models to evaluation mode
    for m in models:
        if m is not None: m.eval()

    # Re-compute all protein embeddings for the evaluation set
    all_protein_embeddings = torch.zeros(len(protein_id_map), gin_input_dim, device=device_gin)
    
    # Peptide encoding
    for data in peptide_loader:
        # The new dataset returns 'atom_types', which we ignore here.
        coords, normals, mask, prot_ids = data['coords'].to(device_pep), data['normals'].to(device_pep), data['mask'].to(device_pep), data['id']
        if not mask.any() or all(pid not in protein_id_map for pid in prot_ids):
            continue
        _, _, _, embeddings, _ = vqvae_peptide(coords, normals, mask)
        projected_embeddings = peptide_projection(embeddings)
        # Move embeddings to the GIN device
        with torch.no_grad():
            valid_ids = [pid for pid in prot_ids if pid in protein_id_map]
            if valid_ids:
                all_protein_embeddings[[protein_id_map[pid] for pid in valid_ids]] = projected_embeddings[[prot_ids.index(pid) for pid in valid_ids]].to(device_gin)

    # Protein encoding
    for data in protein_loader:
        coords, normals, mask, prot_ids = data['coords'].to(device_prot), data['normals'].to(device_prot), data['mask'].to(device_prot), data['id']
        if not mask.any() or all(pid not in protein_id_map for pid in prot_ids):
            continue
        _, _, _, embeddings, _ = vqvae_protein(coords, normals, mask)
        projected_embeddings = protein_projection(embeddings)
        # Move embeddings to the GIN device
        with torch.no_grad():
            valid_ids = [pid for pid in prot_ids if pid in protein_id_map]
            if valid_ids:
                all_protein_embeddings[[protein_id_map[pid] for pid in valid_ids]] = projected_embeddings[[prot_ids.index(pid) for pid in valid_ids]].to(device_gin)

    # GIN classification for the evaluation set
    predictions = model(ppi_graph, all_protein_embeddings, all_pairs, eval_idx)
    labels = labels_tensor[eval_idx]
    
    # Calculate metrics
    preds_softmax = F.softmax(predictions, dim=1)
    preds_class = torch.argmax(preds_softmax, dim=1)
    
    acc = accuracy_score(labels.cpu().numpy(), preds_class.cpu().numpy())
    # Ensure there are both classes for AUC calculation
    if len(torch.unique(labels)) > 1:
        auc = roc_auc_score(labels.cpu().numpy(), preds_softmax[:, 1].cpu().numpy())
    else:
        auc = 0.5 # Default value if only one class is present
    
    return acc, auc

# --- Flexible Model Parallel Main Worker ---
def main_model_parallel(config):
    """
    Main worker for model parallel training with flexible GPU allocation.
    """
    # --- Unpack Config ---
    for key, val in config.items():
        globals()[key] = val

    # --- Flexible Device Setup ---
    num_gpus = torch.cuda.device_count()
    if num_gpus == 0:
        print("No GPUs found. This script requires at least one GPU. Aborting.")
        return

    print(f"Found {num_gpus} GPUs. Determining model placement...")
    gpu_memory = {i: torch.cuda.mem_get_info(i)[0] for i in range(num_gpus)}
    sorted_gpus = sorted(gpu_memory.items(), key=lambda item: item[1], reverse=True)
    gpu_indices = [g[0] for g in sorted_gpus]

    if num_gpus == 1:
        device_prot = device_pep = device_gin = torch.device(f"cuda:{gpu_indices[0]}")
        print(f"Placing all models on single GPU: {device_prot}")
    elif num_gpus == 2:
        device_prot = torch.device(f"cuda:{gpu_indices[0]}")
        device_pep = device_gin = torch.device(f"cuda:{gpu_indices[1]}")
        print(f"Using 2 GPUs: Protein VQ-VAE on {device_prot}; Peptide VQ-VAE and GIN on {device_pep}")
    else:  # 3 or more GPUs
        device_prot = torch.device(f"cuda:{gpu_indices[0]}")
        device_pep = torch.device(f"cuda:{gpu_indices[1]}")
        device_gin = torch.device(f"cuda:{gpu_indices[2]}")
        print(f"Using 3+ GPUs: Protein VQ-VAE on {device_prot}, Peptide VQ-VAE on {device_pep}, GIN on {device_gin}")

    devices = (device_pep, device_prot, device_gin)

    writer = SummaryWriter(log_dir=f'runs/{EXP_NAME}')

    # --- Data Loading and Preprocessing ---
    if DATASET_NAME == "Ar":
        PDB_DIR = Path(PDB_ROOT) / "Arabidopsis_pdb"
        positive_file = os.path.join(PPI_DATA_ROOT, "postive.txt")
        negative_file = os.path.join(PPI_DATA_ROOT, "negative.txt")
        pos_sep, neg_sep = '     ', '    '
    elif DATASET_NAME == "Rice":
        PDB_DIR = Path(PDB_ROOT) / "all_pdbs"
        positive_file = os.path.join(PPI_DATA_ROOT, "grind_positive.txt")
        negative_file = os.path.join(PPI_DATA_ROOT, "grind_negative.txt")
        pos_sep, neg_sep = ' ', ' '
    elif DATASET_NAME == "DeepPeppi":
        PDB_DIR = Path(PDB_ROOT) / "all_pdbs"
        positive_file = os.path.join(PPI_DATA_ROOT, "PepPIs ind.txt")
        negative_file = os.path.join(PPI_DATA_ROOT, "non-PepPIs ind.txt")
        pos_sep, neg_sep = '\t', '\t'
    else:
        raise ValueError(f"Unknown dataset: {DATASET_NAME}")

    print("Converting PDB files to NPY format (if necessary)...")
    NPY_DIR = PDB_DIR.parent / (PDB_DIR.name + f"_npy_{DATASET_NAME}")
    if not NPY_DIR.exists():
        convert_pdbs_to_npy(PDB_DIR, NPY_DIR)

    print("Loading PPI pairs...")
    positive_pairs_df = pd.read_csv(positive_file, sep=pos_sep, header=None, engine='python')
    negative_pairs_df = pd.read_csv(negative_file, sep=neg_sep, header=None, engine='python')
    
    if is_balance:
        print("Balancing dataset...")
        min_samples = min(len(positive_pairs_df), len(negative_pairs_df))
        positive_pairs_df = positive_pairs_df.sample(n=min_samples, random_state=42)
        negative_pairs_df = negative_pairs_df.sample(n=min_samples, random_state=42)

    pep_ids = set(positive_pairs_df[0].astype(str)).union(set(negative_pairs_df[0].astype(str)))
    prot_ids = set(positive_pairs_df[1].astype(str)).union(set(negative_pairs_df[1].astype(str)))
    all_unique_protein_names = sorted(list(pep_ids.union(prot_ids)))
    protein_id_map = {name: i for i, name in enumerate(all_unique_protein_names)}

    peptide_dataset = ProteinSurfaceDataset(NPY_DIR, max_surface_points=MAX_PEPTIDE_POINTS, subset_ids=pep_ids, device=device_pep)
    protein_dataset = ProteinSurfaceDataset(NPY_DIR, max_surface_points=MAX_PROTEIN_POINTS, subset_ids=prot_ids, device=device_prot)

    # Use non-shuffling loaders for reproducible evaluation embedding generation
    peptide_loader = DataLoader(peptide_dataset, batch_size=BATCH_SIZE, shuffle=True, num_workers=0, pin_memory=True)
    protein_loader = DataLoader(protein_dataset, batch_size=BATCH_SIZE, shuffle=True, num_workers=0, pin_memory=True)
    eval_peptide_loader = DataLoader(peptide_dataset, batch_size=BATCH_SIZE, shuffle=False, num_workers=0, pin_memory=True)
    eval_protein_loader = DataLoader(protein_dataset, batch_size=BATCH_SIZE, shuffle=False, num_workers=0, pin_memory=True)

    all_pairs = []
    all_labels = []
    for _, row in positive_pairs_df.iterrows():
        p1, p2 = str(row[0]), str(row[1])
        if p1 in protein_id_map and p2 in protein_id_map:
            all_pairs.append([protein_id_map[p1], protein_id_map[p2]])
            all_labels.append(1)
    for _, row in negative_pairs_df.iterrows():
        p1, p2 = str(row[0]), str(row[1])
        if p1 in protein_id_map and p2 in protein_id_map:
            all_pairs.append([protein_id_map[p1], protein_id_map[p2]])
            all_labels.append(0)

    indices = np.arange(len(all_pairs))
    labels_np = np.array(all_labels)
    train_idx, temp_idx, _, _ = train_test_split(indices, labels_np, test_size=0.2, stratify=labels_np, random_state=42)
    val_idx, test_idx, _, _ = train_test_split(temp_idx, labels_np[temp_idx], test_size=0.5, stratify=labels_np[temp_idx], random_state=42)

    ppi_graph = dgl.graph((torch.tensor([p[0] for p in all_pairs]), torch.tensor([p[1] for p in all_pairs])), num_nodes=len(all_unique_protein_names)) 
    ppi_graph = dgl.add_self_loop(ppi_graph).to(device_gin)
    labels_tensor = torch.tensor(all_labels, dtype=torch.long).to(device_gin)

    vqvae_peptide = VQVAE(**VQVAE_PEPTIDE_PARAMS).to(device_pep)
    vqvae_protein = VQVAE(**VQVAE_PROTEIN_PARAMS).to(device_prot)
    model = PPI_Prediction(GIN_PARAM).to(device_gin)
    peptide_projection = nn.Linear(VQVAE_PEPTIDE_PARAMS['embedding_dim'], GIN_INPUT_DIM).to(device_pep)
    protein_projection = nn.Linear(VQVAE_PROTEIN_PARAMS['embedding_dim'], GIN_INPUT_DIM).to(device_prot)
    
    models = (vqvae_peptide, vqvae_protein, model, peptide_projection, protein_projection)
    optimizer = optim.Adam(itertools.chain(*[m.parameters() for m in models if m is not None]), lr=LEARNING_RATE)
    criterion = nn.CrossEntropyLoss()

    print("Starting training...")
    for epoch in range(EPOCHS):
        for m in models: m.train()
        
        all_protein_embeddings = torch.zeros(len(all_unique_protein_names), GIN_INPUT_DIM, device=device_gin)
        total_vq_loss_pep, total_recon_loss_pep = torch.tensor(0.0, device=device_pep), torch.tensor(0.0, device=device_pep)
        total_vq_loss_prot, total_recon_loss_prot = torch.tensor(0.0, device=device_prot), torch.tensor(0.0, device=device_prot)
        all_pep_indices, all_prot_indices = [], []

        # --- Training Phase ---
        # Peptide Encoding
        for data in tqdm(peptide_loader, desc=f"Epoch {epoch+1}/{EPOCHS} Train Peptides", leave=False):
            coords, normals, mask, prot_ids = data['coords'].to(device_pep), data['normals'].to(device_pep), data['mask'].to(device_pep), data['id']
            if not mask.any(): continue
            rec_coords, rec_normals, vq_loss, embeddings, enc_inds = vqvae_peptide(coords, normals, mask)
            total_vq_loss_pep += vq_loss.mean()
            total_recon_loss_pep += F.mse_loss(rec_coords[mask], coords[mask]) + F.mse_loss(rec_normals[mask], normals[mask])
            projected_embeddings = peptide_projection(embeddings)
            with torch.no_grad():
                valid_ids = [pid for pid in prot_ids if pid in protein_id_map]
                if valid_ids:
                    all_protein_embeddings[[protein_id_map[pid] for pid in valid_ids]] = projected_embeddings[[prot_ids.index(pid) for pid in valid_ids]].to(device_gin)
                    all_pep_indices.append(enc_inds[mask].cpu())

        # Protein Encoding
        for data in tqdm(protein_loader, desc=f"Epoch {epoch+1}/{EPOCHS} Train Proteins", leave=False):
            coords, normals, mask, prot_ids = data['coords'].to(device_prot), data['normals'].to(device_prot), data['mask'].to(device_prot), data['id']
            if not mask.any(): continue
            rec_coords, rec_normals, vq_loss, embeddings, enc_inds = vqvae_protein(coords, normals, mask)
            total_vq_loss_prot += vq_loss.mean()
            total_recon_loss_prot += F.mse_loss(rec_coords[mask], coords[mask]) + F.mse_loss(rec_normals[mask], normals[mask])
            projected_embeddings = protein_projection(embeddings)
            with torch.no_grad():
                valid_ids = [pid for pid in prot_ids if pid in protein_id_map]
                if valid_ids:
                    all_protein_embeddings[[protein_id_map[pid] for pid in valid_ids]] = projected_embeddings[[prot_ids.index(pid) for pid in valid_ids]].to(device_gin)
                    all_prot_indices.append(enc_inds[mask].cpu())
        
        # GIN Classification
        predictions = model(ppi_graph, all_protein_embeddings, all_pairs, train_idx)
        ppi_loss = criterion(predictions, labels_tensor[train_idx])

        loss_pep = (total_vq_loss_pep / len(peptide_loader)).to(device_gin)
        loss_prot = (total_vq_loss_prot / len(protein_loader)).to(device_gin)
        loss_recon_pep = (total_recon_loss_pep / len(peptide_loader)).to(device_gin)
        loss_recon_prot = (total_recon_loss_prot / len(protein_loader)).to(device_gin)
        loss = ppi_loss + loss_pep + loss_prot + loss_recon_pep + loss_recon_prot
        
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()
        
        # --- Evaluation & Logging Phase ---
        eval_loaders = (eval_peptide_loader, eval_protein_loader)
        val_acc, val_auc = evaluate(models, eval_loaders, ppi_graph, all_pairs, labels_tensor, val_idx, devices, protein_id_map, GIN_INPUT_DIM)

        pep_codebook_usage = len(torch.unique(torch.cat(all_pep_indices))) / VQVAE_PEPTIDE_PARAMS['num_embeddings'] if all_pep_indices else 0.0
        prot_codebook_usage = len(torch.unique(torch.cat(all_prot_indices))) / VQVAE_PROTEIN_PARAMS['num_embeddings'] if all_prot_indices else 0.0

        log_msg = (
            f"Epoch {epoch+1}/{EPOCHS} | Loss: {loss.item():.4f} | Val Acc: {val_acc:.4f} | Val AUC: {val_auc:.4f} | "
            f"Pep Codebook: {pep_codebook_usage:.2%} | Prot Codebook: {prot_codebook_usage:.2%}"
        )
        print(log_msg)
        writer.add_scalar('Loss/train_total', loss.item(), epoch)
        writer.add_scalar('Loss/ppi', ppi_loss.item(), epoch)
        writer.add_scalar('Loss/vq', (loss_pep+loss_prot).item(), epoch)
        writer.add_scalar('Loss/recon', (loss_recon_pep+loss_recon_prot).item(), epoch)
        writer.add_scalar('Metrics/val_accuracy', val_acc, epoch)
        writer.add_scalar('Metrics/val_auc', val_auc, epoch)
        writer.add_scalar('Codebook/peptide_usage', pep_codebook_usage, epoch)
        writer.add_scalar('Codebook/protein_usage', prot_codebook_usage, epoch)

    writer.close()
    print("Training finished.")
    # --- Final Test Set Evaluation ---
    print("Running final evaluation on the test set...")
    test_acc, test_auc = evaluate(models, eval_loaders, ppi_graph, all_pairs, labels_tensor, test_idx, devices, protein_id_map, GIN_INPUT_DIM)
    print(f"Test Accuracy: {test_acc:.4f}, Test AUC: {test_auc:.4f}")


if __name__ == '__main__':
    # Set start method for multiprocessing
    try:
        mp.set_start_method('spawn', force=True)
        print("Spawn start method set.")
    except RuntimeError:
        pass 

    config = {
        'EXP_NAME': 'ppi_v3_0828_hybrid_params',
        'DATASET_NAME': "Ar", # Can be "Ar", "Rice", or "DeepPeppi"
        'is_balance': True,
        'PDB_ROOT': "/home/<USER>/code/MAPE-PPI/data/raw_data",
        'PPI_DATA_ROOT': "/home/<USER>/code/customer_rice/PepPI dataset/Arabidopsis thaliana",
        'MAX_PEPTIDE_POINTS': 1000,
        'MAX_PROTEIN_POINTS': 4000,
        'BATCH_SIZE': 1,
        'EPOCHS': 50,
        'LEARNING_RATE': 1e-4,
        'GIN_INPUT_DIM': 128,
        # Parameters below are adapted from vqvae_ver3_0815.py
        'VQVAE_PEPTIDE_PARAMS': {
            'embedding_dim': 32,
            'num_embeddings': 64,
            'commitment': 0.25,
            'nhead': 8,
            'dim_feedforward': 512,
            'num_layers': 4,
            'use_checkpointing': True
        },
        'VQVAE_PROTEIN_PARAMS': {
            'embedding_dim': 64,
            'num_embeddings': 128,
            'commitment': 0.25,
            'nhead': 8,
            'dim_feedforward': 512,
            'num_layers': 4,
            'use_checkpointing': True
        },
        'GIN_PARAM': {
            'ppi_num_layers': 3,
            'dropout_ratio': 0.5,
            'prot_hidden_dim': 128,
            'ppi_hidden_dim': 128,
            'output_dim': 2,
            'use_checkpointing': True
        }
    }
    config['MODEL_SAVE_PATH'] = f"best_model_{config['EXP_NAME']}.pth"

    if torch.cuda.is_available():
        main_model_parallel(config)
    else:
        print("No GPUs found. This script requires at least one GPU.")
