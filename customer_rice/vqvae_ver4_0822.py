# VQ-VAE + QuinNet Protein Classification Pipeline (Dual Encoder)
# ===============================================================

# This script implements a complete pipeline for protein classification using:
# 1. Two separate VQ-VAE encoders with different dimensions for peptides and proteins.
# 2. Support for multi-GPU training using nn.DataParallel.
# 3. QuinNet-inspired model architecture for classification on unified embeddings.

# The pipeline includes:
# - Separate dimensionality reduction for peptides and proteins.
# - Padding of lower-dimension embeddings to create a unified feature space.
# - QuinNet-based classification with attention mechanisms.
# - Comprehensive evaluation with accuracy and confusion matrix.
# - TensorBoard logging for training visualization.

# Author: Generated for customer_rice project
# Date: 2024-08-15


import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import numpy as np
import os
import sys
from Bio.PDB import PDB<PERSON>arser
from Bio.PDB.PDBExceptions import PDBConstructionWarning
import warnings
from tqdm import tqdm
import matplotlib.pyplot as plt
from torch.utils.tensorboard import SummaryWriter
from sklearn.metrics import confusion_matrix, accuracy_score, classification_report, roc_auc_score
import seaborn as sns
from pathlib import Path
import pandas as pd
from sklearn.model_selection import train_test_split

# Import dMaSIF components
from dmasif_model.dMaSIF.geometry_processing import atoms_to_points_normals

# Import QuinNet models directly
sys.path.append(os.path.abspath('QuinNet'))
from src.models.model import create_model

# --- Constants & Global Config ---
warnings.filterwarnings("ignore", category=PDBConstructionWarning)
ele2num = {"C": 0, "H": 1, "O": 2, "N": 3, "S": 4, "SE": 5}
ele2z = {"H": 1, "C": 6, "N": 7, "O": 8, "S": 16, "SE": 34}

# --- Data Preprocessing & Dataset --- 

def convert_pdbs_to_npy(pdb_dir, npy_dir):
    print(f"Converting PDBs from {pdb_dir} to npy format in {npy_dir}...")
    npy_dir.mkdir(parents=True, exist_ok=True)
    parser = PDBParser()
    for p in tqdm(list(pdb_dir.glob("*.pdb"))):
        try:
            structure = parser.get_structure("structure", p)
            atoms = list(structure.get_atoms())
            coords = np.array([atom.get_coord() for atom in atoms])
            elements = [atom.element.strip().upper() for atom in atoms]
            types = [ele2num.get(el, 5) for el in elements]
            types_array = np.zeros((len(types), len(ele2num)))
            for i, t in enumerate(types): types_array[i, t] = 1.0
            z_values = np.array([ele2z.get(el, 0) for el in elements])
            np.save(npy_dir / (p.stem + "_atomxyz.npy"), coords)
            np.save(npy_dir / (p.stem + "_atomtypes.npy"), types_array)
            np.save(npy_dir / (p.stem + "_atomz.npy"), z_values)
        except Exception as e:
            print(f"Could not process {p.name}: {e}")

class ProteinFeatureDataset(Dataset):
    def __init__(self, npy_dir, subset_ids, max_surface_points=1000, max_atoms=5000, min_atoms=10):
        self.npy_files = [f for f in npy_dir.glob("*_atomxyz.npy") if f.stem.replace("_atomxyz", "") in subset_ids]
        self.max_surface_points = max_surface_points
        self.max_atoms = max_atoms
        self.min_atoms = min_atoms

    def __len__(self):
        return len(self.npy_files)

    def __getitem__(self, idx):
        xyz_path = self.npy_files[idx]
        protein_id = xyz_path.stem.replace("_atomxyz", "")
        try:
            atom_coords_np = np.load(xyz_path)
            if len(atom_coords_np) < self.min_atoms: raise ValueError(f"Too few atoms: {len(atom_coords_np)}")

            types_path = xyz_path.with_name(xyz_path.name.replace("_atomxyz.npy", "_atomtypes.npy"))
            z_path = xyz_path.with_name(xyz_path.name.replace("_atomxyz.npy", "_atomz.npy"))
            atom_types_np = np.load(types_path)
            atom_z_np = np.load(z_path)

            # --- Atom processing with PADDING ---
            current_atoms = len(atom_coords_np)
            atom_mask_np = np.ones(current_atoms, dtype=bool)
            if current_atoms > self.max_atoms:
                indices = np.random.choice(current_atoms, self.max_atoms, replace=False)
                atom_coords_np, atom_z_np = atom_coords_np[indices], atom_z_np[indices]
                atom_mask_np = np.ones(self.max_atoms, dtype=bool)
            else:
                pad_width = self.max_atoms - current_atoms
                atom_coords_np = np.pad(atom_coords_np, ((0, pad_width), (0, 0)), 'constant', constant_values=0.0)
                atom_z_np = np.pad(atom_z_np, (0, pad_width), 'constant', constant_values=0)
                atom_mask_np = np.pad(atom_mask_np, (0, pad_width), 'constant', constant_values=False)
            
            atom_coords = torch.from_numpy(atom_coords_np).float()
            atom_z = torch.from_numpy(atom_z_np).long()
            atom_mask = torch.from_numpy(atom_mask_np).bool()

            # --- Surface generation with PADDING ---
            device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
            real_atoms = torch.tensor(np.load(xyz_path), dtype=torch.float32).to(device)
            real_atom_types = torch.tensor(np.load(types_path), dtype=torch.float32).to(device)
            batch_tensor = torch.zeros(real_atoms.shape[0], dtype=torch.long).to(device)

            surface_points, surface_normals, _ = atoms_to_points_normals(
                real_atoms, batch_tensor, atomtypes=real_atom_types,
                distance=1.05, smoothness=0.5, resolution=1.5, nits=4, sup_sampling=5, variance=0.1
            )
            surface_points, surface_normals = surface_points.cpu(), surface_normals.cpu()

            N_surface = surface_points.shape[0]
            surface_mask = torch.ones(N_surface, dtype=torch.bool)
            if N_surface > self.max_surface_points:
                idxs = np.random.choice(N_surface, self.max_surface_points, replace=False)
                surface_points = surface_points[idxs]
                surface_normals = surface_normals[idxs]
                surface_mask = torch.ones(self.max_surface_points, dtype=torch.bool)
            else:
                pad = self.max_surface_points - N_surface
                surface_points = F.pad(surface_points, (0, 0, 0, pad), 'constant', 0)
                surface_normals = F.pad(surface_normals, (0, 0, 0, pad), 'constant', 0)
                surface_mask = F.pad(surface_mask, (0, pad), 'constant', False)

            return {
                'surface_coords': surface_points,
                'surface_normals': surface_normals,
                'surface_mask': surface_mask,
                'atom_coords': atom_coords,
                'atom_z': atom_z,
                'atom_mask': atom_mask,
                'id': protein_id
            }
        except Exception as e:
            return {'id': 'error', 'reason': str(e)}


# --- Model Components ---

class VectorQuantizer(nn.Module):
    def __init__(self, num_embeddings, embedding_dim, commitment_cost=0.25):
        super().__init__()
        self.num_embeddings = num_embeddings
        self.embedding_dim = embedding_dim
        self.commitment_cost = commitment_cost
        self.embeddings = nn.Embedding(num_embeddings, embedding_dim)
        nn.init.uniform_(self.embeddings.weight, -1.0, 1.0)

    def forward(self, inputs):
        B, N, E = inputs.shape
        flat_inputs = inputs.view(-1, E)
        d_sq = (torch.sum(flat_inputs**2, dim=1, keepdim=True)
               + torch.sum(self.embeddings.weight**2, dim=1)
               - 2 * torch.matmul(flat_inputs, self.embeddings.weight.t()))
        encoding_inds = torch.argmin(d_sq, dim=1)
        quantized = self.embeddings(encoding_inds).view(B, N, E)
        e_latent_loss = F.mse_loss(quantized.detach(), inputs)
        q_latent_loss = F.mse_loss(quantized, inputs.detach())
        loss = q_latent_loss + self.commitment_cost * e_latent_loss
        quantized_st = inputs + (quantized - inputs).detach()
        return quantized_st, loss, encoding_inds.view(B, N)

class ProteinVQVAEEncoder(nn.Module):
    def __init__(self, embedding_dim=64, num_embeddings=512):
        super().__init__()
        self.embedding_dim = embedding_dim
        self.coord_net = nn.Sequential(nn.Linear(3, embedding_dim), nn.ReLU(), nn.LayerNorm(embedding_dim))
        self.normal_net = nn.Sequential(nn.Linear(3, embedding_dim), nn.ReLU(), nn.LayerNorm(embedding_dim))
        self.fuse = nn.Sequential(nn.Linear(2 * embedding_dim, embedding_dim), nn.ReLU(), nn.LayerNorm(embedding_dim))
        encoder_layer = nn.TransformerEncoderLayer(d_model=embedding_dim, nhead=4, dim_feedforward=256, batch_first=True, dropout=0.1)
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=2)
        self.proj = nn.Sequential(nn.Linear(embedding_dim, embedding_dim), nn.ReLU(), nn.LayerNorm(embedding_dim))
        self.vq = VectorQuantizer(num_embeddings, embedding_dim)
        self.decoder = nn.Linear(embedding_dim, 6) # For reconstruction loss

    def encode(self, coords, normals, mask):
        x = self.fuse(torch.cat([self.coord_net(coords), self.normal_net(normals)], dim=-1))
        pad_mask = ~mask
        h = self.proj(self.transformer(x, src_key_padding_mask=pad_mask))
        quant, vq_loss, codes = self.vq(h)
        return quant, vq_loss, codes

    def forward(self, coords, normals, mask):
        quant, vq_loss, codes = self.encode(coords, normals, mask)
        reconstructed = self.decoder(quant)
        recon_loss = F.mse_loss(reconstructed[mask], torch.cat([coords, normals], dim=-1)[mask])
        return quant, vq_loss, recon_loss, codes

class FinalClassifier(nn.Module):
    def __init__(self, input_dim, hidden_dim=256, num_classes=2):
        super().__init__()
        self.net = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(),
            nn.BatchNorm1d(hidden_dim),
            nn.Dropout(0.5),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.BatchNorm1d(hidden_dim // 2),
            nn.Dropout(0.5),
            nn.Linear(hidden_dim // 2, num_classes)
        )
    def forward(self, x):
        return self.net(x)

# --- Stage 1: VQ-VAE Training ---

def train_single_vqvae(model, npy_dir, subset_ids, config, run_name, device):
    print(f"--- Stage 1: Training VQ-VAE for '{run_name}' ---")
    dataset = ProteinFeatureDataset(npy_dir, subset_ids, max_surface_points=config['max_surface_points'], max_atoms=config['max_atoms'])
    dataloader = DataLoader(dataset, batch_size=config['vqvae_batch_size'], shuffle=True, num_workers=0)
    optimizer = optim.Adam(model.parameters(), lr=config['vqvae_lr'])
    writer = SummaryWriter(f"runs/{config['exp_name']}/{run_name}")
    model.to(device)

    for epoch in range(config['vqvae_epochs']):
        model.train()
        total_loss, used_codes = 0, set()
        pbar = tqdm(dataloader, desc=f"Epoch {epoch+1}/{config['vqvae_epochs']}")
        for data in pbar:
            if data['id'] == 'error': continue
            coords, normals, mask = data['surface_coords'].to(device), data['surface_normals'].to(device), data['surface_mask'].to(device)
            if not mask.any(): continue

            optimizer.zero_grad()
            _, vq_loss, recon_loss, codes = model(coords, normals, mask)
            loss = vq_loss + recon_loss
            loss.backward()
            optimizer.step()

            total_loss += loss.item()
            used_codes.update(codes.cpu().numpy().flatten())
            pbar.set_postfix(loss=loss.item())

        avg_loss = total_loss / len(dataloader)
        usage = len(used_codes) / model.vq.num_embeddings * 100
        print(f"Epoch {epoch+1} | Avg Loss: {avg_loss:.4f} | Codebook Usage: {usage:.2f}%")
        writer.add_scalar("Loss/train", avg_loss, epoch)
        writer.add_scalar("CodebookUsage/train", usage, epoch)
    writer.close()
    return model.cpu()

# --- Stage 2: Feature Extraction ---

def extract_all_features(p1_vqvae, p2_vqvae, quinnet, npy_dir, all_ids, p1_ids, p2_ids, config, device):
    print("--- Stage 2: Extracting all features (VQ-VAE + QuinNet) ---")
    p1_vqvae.to(device).eval()
    p2_vqvae.to(device).eval()
    quinnet.to(device).eval()

    dataset = ProteinFeatureDataset(npy_dir, all_ids, max_surface_points=config['max_surface_points'], max_atoms=config['max_atoms'])
    dataloader = DataLoader(dataset, batch_size=config['extract_batch_size'], shuffle=False, num_workers=0)
    
    feature_map = {}
    with torch.no_grad():
        for data in tqdm(dataloader, desc="Extracting Features"):
            if data['id'] == 'error': continue
            protein_ids = data['id']
            surface_coords, surface_normals, surface_mask = data['surface_coords'].to(device), data['surface_normals'].to(device), data['surface_mask'].to(device)
            atom_coords, atom_z, atom_mask = data['atom_coords'].to(device), data['atom_z'].to(device), data['atom_mask'].to(device)

            # QuinNet features
            B, M = atom_coords.shape[:2]
            batch_idx = torch.arange(B, device=device).view(-1, 1).repeat(1, M).view(-1)
            real_atom_mask = atom_mask.view(-1)
            pos, z, batch = atom_coords.view(-1, 3)[real_atom_mask], atom_z.view(-1)[real_atom_mask], batch_idx[real_atom_mask]
            quinnet_features = quinnet.representation_model(z, pos, batch)[0]
            quinnet_pooled = torch.zeros(B, quinnet_features.shape[-1], device=device)
            for i in range(B):
                if (batch == i).any():
                    quinnet_pooled[i] = quinnet_features[batch == i].mean(dim=0)

            # VQ-VAE features
            quant_p1, _, _ = p1_vqvae.encode(surface_coords, surface_normals, surface_mask)
            quant_p2, _, _ = p2_vqvae.encode(surface_coords, surface_normals, surface_mask)
            vq_pooled_p1 = (quant_p1 * surface_mask.unsqueeze(-1)).sum(dim=1) / (surface_mask.sum(dim=1, keepdim=True) + 1e-8)
            vq_pooled_p2 = (quant_p2 * surface_mask.unsqueeze(-1)).sum(dim=1) / (surface_mask.sum(dim=1, keepdim=True) + 1e-8)

            for i, pid in enumerate(protein_ids):
                quin_feat = quinnet_pooled[i]
                vq_feat = vq_pooled_p1[i] if pid in p1_ids else vq_pooled_p2[i]
                feature_map[pid] = torch.cat([vq_feat, quin_feat]).cpu()

    return feature_map

# --- Stage 3: Classifier Training ---

def train_final_classifier(classifier, paired_data, labels, valid_pairs, config, device):
    print("--- Stage 3: Training Final Classifier ---")
    indices = np.arange(len(paired_data))
    train_idx, test_idx, _, _ = train_test_split(indices, labels.numpy(), test_size=0.2, stratify=labels.numpy(), random_state=42)
    val_idx, test_idx, _, _ = train_test_split(test_idx, labels.numpy()[test_idx], test_size=0.5, stratify=labels.numpy()[test_idx], random_state=42)

    test_pairs = valid_pairs.iloc[test_idx].reset_index(drop=True)

    print(f"Classifier Data split: Train={len(train_idx)}, Val={len(val_idx)}, Test={len(test_idx)}")

    train_dataset = torch.utils.data.TensorDataset(paired_data[train_idx], labels[train_idx])
    val_dataset = torch.utils.data.TensorDataset(paired_data[val_idx], labels[val_idx])
    test_dataset = torch.utils.data.TensorDataset(paired_data[test_idx], labels[test_idx])

    train_loader = DataLoader(train_dataset, batch_size=config['classifier_batch_size'], shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=config['classifier_batch_size'])
    test_loader = DataLoader(test_dataset, batch_size=config['classifier_batch_size'])

    classifier.to(device)
    optimizer = optim.Adam(classifier.parameters(), lr=config['classifier_lr'])
    criterion = nn.CrossEntropyLoss()
    writer = SummaryWriter(f"runs/{config['exp_name']}/Classifier")
    best_val_auc = -1.0
    best_val_acc = -1.0

    for epoch in range(config['classifier_epochs']):
        classifier.train()
        for features, batch_labels in train_loader:
            features, batch_labels = features.to(device), batch_labels.to(device)
            optimizer.zero_grad()
            logits = classifier(features)
            loss = criterion(logits, batch_labels)
            loss.backward()
            optimizer.step()

        # Validation
        classifier.eval()
        all_preds, all_labels, all_probs = [], [], []
        with torch.no_grad():
            for features, batch_labels in val_loader:
                features, batch_labels = features.to(device), batch_labels.to(device)
                logits = classifier(features)
                probs = F.softmax(logits, dim=1)
                all_preds.extend(logits.argmax(dim=1).cpu().numpy())
                all_labels.extend(batch_labels.cpu().numpy())
                all_probs.extend(probs.cpu().numpy())
        
        val_acc = accuracy_score(all_labels, all_preds)
        val_auc = float('nan')
        if len(np.unique(all_labels)) > 1:
            val_auc = roc_auc_score(all_labels, np.array(all_probs)[:, 1])

        print(f"Epoch {epoch+1} | Val Acc: {val_acc:.4f}, Val AUC: {val_auc if not np.isnan(val_auc) else 'nan'}")
        writer.add_scalar("Val/Accuracy", val_acc, epoch)
        if not np.isnan(val_auc):
            writer.add_scalar("Val/AUC", val_auc, epoch)

        if not np.isnan(val_auc):
            if val_auc > best_val_auc:
                best_val_auc = val_auc
                torch.save(classifier.state_dict(), config['save_dir'] / "best_classifier.pth")
                print(f"New best model saved with AUC: {best_val_auc:.4f}")
        else:  # Fallback to accuracy
            if val_acc > best_val_acc:
                best_val_acc = val_acc
                torch.save(classifier.state_dict(), config['save_dir'] / "best_classifier.pth")
                print(f"New best model saved with Accuracy: {best_val_acc:.4f} (AUC is nan)")

    # Final Test
    print("\n--- Final Evaluation on Test Set ---")
    classifier.load_state_dict(torch.load(config['save_dir'] / "best_classifier.pth"))
    classifier.eval()
    all_preds, all_labels, all_probs = [], [], []
    with torch.no_grad():
        for features, batch_labels in test_loader:
            features, batch_labels = features.to(device), batch_labels.to(device)
            logits = classifier(features)
            probs = F.softmax(logits, dim=1)
            all_preds.extend(logits.argmax(dim=1).cpu().numpy())
            all_labels.extend(batch_labels.cpu().numpy())
            all_probs.extend(probs.cpu().numpy())

    if config.get('dry_run', False):
        results_df = test_pairs.copy()
        results_df['True_Label'] = all_labels
        results_df['Predicted_Label'] = all_preds
        results_df['Prediction_Probability_PPI'] = np.array(all_probs)[:, 1]
        csv_path = config['save_dir'] / 'dry_run_predictions.csv'
        results_df.to_csv(csv_path, index=False)
        print(f"Dry run predictions saved to {csv_path}")

    if len(np.unique(all_labels)) > 1:
        report = classification_report(all_labels, all_preds, target_names=['non-PPI', 'PPI'])
        print(report)
    else:
        print("\nCould not generate classification report: Only one class present in test set.")
    cm = confusion_matrix(all_labels, all_preds)
    plt.figure(figsize=(8, 6))
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', xticklabels=['non-PPI', 'PPI'], yticklabels=['non-PPI', 'PPI'])
    plt.savefig(config['save_dir'] / 'final_confusion_matrix.png')
    plt.close()

# --- Main Execution Block ---

def main():
    # --- Configuration ---
    config = {
        'dry_run': True,
        'dataset': "Ar",
        'is_balance': True,
        'exp_name': "vqvae4_dual_0822",
        'PDB_ROOT': "/home/<USER>/code/MAPE-PPI/data/raw_data",
        'PPI_DATA_ROOT': "/home/<USER>/code/customer_rice/PepPI dataset/Arabidopsis thaliana",
        'max_surface_points': 30000,
        'max_atoms': 5000,
        'p1_vqvae_dim': 32,
        'p1_num_embeddings': 64,
        'p2_vqvae_dim': 64,
        'p2_num_embeddings': 128,
        'quinnet_embedding_dim': 64,
        'vqvae_epochs': 10,
        'vqvae_lr': 1e-3,
        'vqvae_batch_size': 4,
        'extract_batch_size': 8,
        'classifier_epochs': 50,
        'classifier_lr': 1e-4,
        'classifier_batch_size': 32,
    }
    config['NPY_DIR'] = Path(f"./preprocessed_proteins_{config['dataset']}_v4")
    config['save_dir'] = Path(f"results/{config['exp_name']}")
    config['save_dir'].mkdir(parents=True, exist_ok=True)
    DEVICE = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

    print(f"--- Configuration v4.0822 (Dual VQ-VAE) ---")
    for key, val in config.items(): print(f"- {key}: {val}")
    print("-------------------------------------------------")

    # --- Data Loading for PPI ---
    if config['dataset'] == "Ar":
        PDB_DIR = Path(config['PDB_ROOT']) / "Arabidopsis_pdb"
        positive_file = os.path.join(config['PPI_DATA_ROOT'], "postive.txt")
        negative_file = os.path.join(config['PPI_DATA_ROOT'], "negative.txt")
        pos_sep, neg_sep = '     ', '    '
    elif config['dataset'] == "DeepPeppi":
        PDB_DIR = Path(config['PDB_ROOT']) / "all_pdbs"
        positive_file = os.path.join(config['PPI_DATA_ROOT'], "PepPIs ind.txt")
        negative_file = os.path.join(config['PPI_DATA_ROOT'], "non-PepPIs ind.txt")
        pos_sep, neg_sep = '\t', '\t'
    else:
        raise ValueError(f"Unknown dataset: {config['dataset']}")

    convert_pdbs_to_npy(PDB_DIR, config['NPY_DIR'])

    positive_pairs_df = pd.read_csv(positive_file, sep=pos_sep, header=None, engine='python')
    negative_pairs_df = pd.read_csv(negative_file, sep=neg_sep, header=None, engine='python')

    if config['is_balance']:
        print("Balancing dataset by downsampling negative pairs...")
        num_positives = len(positive_pairs_df)
        if len(negative_pairs_df) > num_positives:
            negative_pairs_df = negative_pairs_df.sample(n=num_positives, random_state=42)
        print(f"Dataset balanced with {num_positives} positive and {len(negative_pairs_df)} negative pairs.")

    p1_ids = set(positive_pairs_df[0].astype(str)).union(set(negative_pairs_df[0].astype(str)))
    p2_ids = set(positive_pairs_df[1].astype(str)).union(set(negative_pairs_df[1].astype(str)))
    all_unique_ids = sorted(list(p1_ids.union(p2_ids)))
    protein_id_map = {name: i for i, name in enumerate(all_unique_ids)}

    # --- Stage 1: Train VQ-VAE Encoders ---
    p1_vqvae = ProteinVQVAEEncoder(embedding_dim=config['p1_vqvae_dim'], num_embeddings=config['p1_num_embeddings'])
    p1_vqvae = train_single_vqvae(p1_vqvae, config['NPY_DIR'], p1_ids, config, "p1_vqvae", DEVICE)

    p2_vqvae = ProteinVQVAEEncoder(embedding_dim=config['p2_vqvae_dim'], num_embeddings=config['p2_num_embeddings'])
    p2_vqvae = train_single_vqvae(p2_vqvae, config['NPY_DIR'], p2_ids, config, "p2_vqvae", DEVICE)

    # --- Stage 2: Extract Features ---
    quinnet_config = {
        'model': 'QuinNet', 'embedding_dimension': config['quinnet_embedding_dim'], 'num_layers': 4, 'num_rbf': 32, 'rbf_type': 'expnorm',
        'trainable_rbf': True, 'activation': 'silu', 'attn_activation': 'silu', 'neighbor_embedding': True,
        'num_heads': 4, 'distance_influence': 'both', 'cutoff_lower': 0.0, 'cutoff_upper': 4.0, 'max_z': 100,
        'max_num_neighbors': 32, 'output_model': 'Scalar', 'reduce_op': 'add', 'derivative': False, 'atom_filter': -1,
        'prior_model': 'Atomref',
        'prior_args': {'max_z': 100}
    }
    quinnet = create_model(quinnet_config)
    feature_map = extract_all_features(p1_vqvae, p2_vqvae, quinnet, config['NPY_DIR'], all_unique_ids, p1_ids, p2_ids, config, DEVICE)

    # --- Stage 3: Prepare Paired Data and Train Classifier ---
    num_pos_before = len(positive_pairs_df)
    num_neg_before = len(negative_pairs_df)
    print(f"\n--- Preparing Paired Data ---")
    print(f"Before filtering: {num_pos_before} positive pairs, {num_neg_before} negative pairs.")

    all_pairs = pd.concat([positive_pairs_df, negative_pairs_df], ignore_index=True)
    labels = torch.tensor([1]*num_pos_before + [0]*num_neg_before, dtype=torch.long)
    
    paired_data_list, valid_labels_list, valid_pairs_list = [], [], []
    kept_pos, kept_neg = 0, 0
    discarded_pos, discarded_neg = 0, 0

    for i, row in tqdm(all_pairs.iterrows(), total=len(all_pairs), desc="Filtering pairs"):
        p1, p2 = str(row[0]), str(row[1])
        is_pos = labels[i].item() == 1
        
        p1_found = p1 in feature_map
        p2_found = p2 in feature_map

        if p1_found and p2_found:
            feat1, feat2 = feature_map[p1], feature_map[p2]
            paired_data_list.append(torch.cat([feat1, feat2]))
            valid_labels_list.append(labels[i])
            valid_pairs_list.append({'Peptide': p1, 'Protein': p2})
            if is_pos:
                kept_pos += 1
            else:
                kept_neg += 1
        else:
            if is_pos:
                discarded_pos += 1
            else:
                discarded_neg += 1
            # Optional: log the first few missing IDs
            if (discarded_pos + discarded_neg) < 5:
                 reason = []
                 if not p1_found: reason.append(f"P1 '{p1}' not in feature_map")
                 if not p2_found: reason.append(f"P2 '{p2}' not in feature_map")
                 print(f"Discarding pair ({p1}, {p2}): {', '.join(reason)}")

    print(f"Filtering complete.")
    print(f"Kept {kept_pos} positive pairs and {kept_neg} negative pairs.")
    print(f"Discarded {discarded_pos} positive pairs and {discarded_neg} negative pairs.")

    if not paired_data_list:
        print("Error: No valid pairs found after filtering. Cannot proceed with classifier training.")
        return

    paired_data = torch.stack(paired_data_list)
    valid_labels = torch.tensor(valid_labels_list)
    valid_pairs_df = pd.DataFrame(valid_pairs_list)

    label_counts = torch.bincount(valid_labels)
    print(f"Final class distribution in dataset: {label_counts.tolist()}")
    if len(label_counts) < 2:
        print("Warning: The filtered dataset contains only one class. AUC will be NaN and classification report will fail.")

    classifier_input_dim = paired_data.shape[1]
    classifier = FinalClassifier(input_dim=classifier_input_dim)
    train_final_classifier(classifier, paired_data, valid_labels, valid_pairs_df, config, DEVICE)

if __name__ == "__main__":
    main()
