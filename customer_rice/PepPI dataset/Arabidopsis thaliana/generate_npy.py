import torch
from transformers import TFT5EncoderModel, T5Tokenizer, T5EncoderModel
import numpy as np
import re
import gc
tokenizer = T5Tokenizer.from_pretrained("Rostlab/prot_t5_xl_uniref50", do_lower_case=False )
model = TFT5EncoderModel.from_pretrained("Rostlab/prot_t5_xl_uniref50", from_pt=True)
      
gc.collect()
# sequences_Example = "MGEVVRLTNSSTGGPVFVYVKDGKIIRMTPMDFDDAVDAPSWKIEARGKTFTPPRKTSIAPYTAGFKSMIYSDLRIPYPMKRKSFDPNGERNPQLRGAGLSKQDPWSDYERISWDEATDIVVAEINRIKHAYGPSAILSTPSSHHMWGNVGYRHSTYFRFMNMMGFTYADHNPDSWEGWHWGGMHMWGFSWRLGNPEQYDLLEDGLKHAEMIVFWSSDPETNSGIYAGFESNIRRQWLKDLGVDFVFIDPHMNHTARLVADKWFSPKIGTDHALSFAIAYTWLKEDSYDKEYVAANAHGFEEWADYVLGKTDGTPKTCEWAEEESGVPACEIRALARQWAKKNTYLAAGGLGGWGGACRASHGIEWARGMIALATMQGMGKPGSNMWSTTQGVPLDYEFYFPGYAEGGISGDCENSAAGFKFAWRMFDGKTTFPSPSNLNTSAGQHIPRLKIPECIMGGKFQWSGKGFAGGDISHQLHQYEYPAPGYSKIKMFWKYGGPHLGTMTATNRYAKMYTHDSLEFVVSQSIWFEGEVPFADIILPACTNFERWDISEFANCSGYIPDNYQLCNHRVISLQAKCIEPVGESMSDYEIYRLFAKKLNIEEMFSEGKDELAWCEQYFNATDMPKYMTWDEFFKKGYFVVPDNPNRKKTVALRWFAEGREKDTPDWGPRLNNQVCRKGLQTTTGKVEFIATSLKNFEEQGYIDEHRPSMHTYVPAWESQKHSPLAVKYPLGMLSPHPRFSMHTMGDGKNSYMNYIKDHRVEVDGYKYWIMRVNSIDAEARGIKNGDLIRAYNDRGSVILAAQVTECLQPGTVHSYESCAVYDPLGTAGKSADRGGCINILTPDRYISKYACGMANNTALVEIEKWDGDKYEIY"
# seq_split = []
# seq_split.append(str(' '.join([word for word in sequences_Example])))
# # replace all rare/ambiguous amino acids by X and introduce white-space between all amino acids
# sequences_Example = [re.sub(r"[UZOB]", "X", sequence) for sequence in seq_split]

# def load_sequences(file_path):
#     seq_dict = []
#     with open(file_path, 'r') as rf:
#         seq = ''
#         for line in rf:
#             line = line.strip()
#             if line[0] == '>':
#                 print(line)
#             else:
#                 seq = line.upper()
#                 seq_dict.append(seq) 
#     return seq_dict
# sequences_Example = load_sequences2('peptide sequences.fasta')
# sequences_Example = [re.sub(r"[UZOB]", "X", sequence) for sequence in sequences_Example]

def get_embeddings(sequences_Example):
    ids = tokenizer.batch_encode_plus(sequences_Example, add_special_tokens=True, padding=True, return_tensors="tf")
    input_ids = ids['input_ids']
    attention_mask = ids['attention_mask']
    embedding = model(input_ids)
    embedding = np.asarray(embedding.last_hidden_state)
    # embedding = embedding.mean(dim=0)
    attention_mask = np.asarray(attention_mask)
    features = [] 
    for seq_num in range(len(embedding)):
        seq_len = (attention_mask[seq_num] == 1).sum()
        seq_emd = embedding[seq_num][:seq_len-1]
        # seq_emd = seq_emd.mean(dim=0)
        # seq_emd.detach().cpu().numpy().squeeze()
        features.append(seq_emd[0])
    # features = features.mean(dim=0)
    print(np.array(features).shape)
    # np.save('./ProtT5.npy', np.array(features, dtype=object))
    return  np.array(features)


def load_sequences2(file_path):
    seq_dict = {}
    with open(file_path, 'r') as rf:
        seq = ''
        for line in rf:
            line = line.strip()
            if line[0] == '>':
                name = line[1:]
            else:
                seq = line.upper()
                seq_dict[name] = seq
    return seq_dict
def load_sequences3(file_path):
    seq_dict = {}
    with open(file_path, 'r') as rf:
        seq = ''
        for line in rf:
            line = line.strip()
            if line[0] == '>':
                item = line.split(" ")
                name = item[0][1:-2] 
                seq = ''
                seq_dict[name] = ""
            else:
                seq = line.upper().strip(" ").strip('*')
                if name in seq_dict:
                    seq_dict[name] += seq
                else:
                    seq_dict[name] = seq
    return seq_dict

# sequences_Example = load_sequences2('pep_sequence.fasta')
sequences_Example = load_sequences3('grind_pep_sequence.fasta.txt')
result = {}
for key, value in sequences_Example.items():
    sequences_Example[key] = re.sub(r"[UZOB]", "X", value)
    result[key] = get_embeddings(sequences_Example[key])
# np.save('./ProtT5.npy', np.array(features))
np.save('./ProtT5_grind_pep.npy', result, allow_pickle=True)



