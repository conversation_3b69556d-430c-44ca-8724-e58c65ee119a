#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@File         :label_search_entity.py
@Description  :产品词标准化和相似度搜索相关的MySQL实体类和请求响应实体类
<AUTHOR> HONG
@Date         :2025/09/04
"""

from sqlalchemy import Column, Integer, String, Text, Index
from sqlalchemy.ext.declarative import declarative_base
from pydantic import BaseModel, Field
from typing import Optional, List, Tuple, Dict, Any
from datetime import datetime

# 创建 SQLAlchemy 的基类
Base = declarative_base()


class ProductsUnique(Base):
    """产品词唯一标识表 ORM 模型"""
    __tablename__ = 'Products_Unique'
    __table_args__ = {
        'comment': '存储单独的产品词信息',
        'mysql_engine': 'InnoDB',
        'mysql_charset': 'utf8mb4',
        'mysql_collate': 'utf8mb4_unicode_ci'
    }

    id = Column(Integer, primary_key=True, autoincrement=True, comment='产品词唯一标识')
    ProductName = Column(String(50), nullable=False, comment='产品词名称')
    ProductSource = Column(String(20), nullable=False, comment='来源类型：产品词或末级环节')
    Task_id = Column(Integer, nullable=False, comment='关联的任务ID')
    Version = Column(Integer, nullable=False, comment='产品词版本')
    Product_ids = Column(Text, nullable=True, comment='来源产品词id列表')
    Industry_last_node_code = Column(String(50), nullable=False, comment='来源产品词id列表')

    # 创建索引
    __table_args__ = (
        Index('idx_product_name', 'ProductName'),
        Index('idx_task_id', 'Task_id'),
        Index('idx_industry_last_node_code', 'Industry_last_node_code'),
        Index('idx_product_source', 'ProductSource'),
        __table_args__
    )


class IndustryChainStructure(Base):
    """产业链结构表 ORM 模型"""
    __tablename__ = 'Industry_Chain_Structure'
    __table_args__ = {
        'comment': '产业链结构表，存储产业链各节点的结构信息',
        'mysql_engine': 'InnoDB',
        'mysql_charset': 'utf8mb4',
        'mysql_collate': 'utf8mb4_unicode_ci'
    }

    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键，自增')
    Task_id = Column(Integer, nullable=True, comment='任务id')
    Industry_chain_type_code = Column(String(20), nullable=True, comment='产业链类型代码：上中下游、链条型、并列型')
    Industry_chain_position = Column(Integer, nullable=True, comment='产业链位置')
    Node_sequence = Column(Integer, nullable=True, comment='节点序号，同级节点之间的排序方式 12345...')
    Node_name = Column(String(50), nullable=True, comment='节点名称')
    Node_code = Column(String(50), nullable=True, comment='节点代码')
    Parent_node_code = Column(String(50), nullable=True, comment='父节点代码')
    Is_leaf_node = Column(Integer, nullable=True, comment='是否叶节点：0否 1是')
    Node_depth = Column(Integer, nullable=True, comment='节点深度，产业链层级123456')
    Is_modified = Column(Integer, nullable=True, comment='是否修改：0否 1是')
    Node_modified_name = Column(String(50), nullable=True, comment='节点修改名称')
    Status = Column(Integer, default=1, nullable=True, comment='节点状态  0-删除 1-正常')

    # 创建索引
    __table_args__ = (
        Index('idx_node_code', 'Node_code'),
        Index('idx_node_name', 'Node_name'),
        Index('idx_parent_node_code', 'Parent_node_code'),
        Index('idx_task_id', 'Task_id'),
        __table_args__
    )


# ==================== 请求响应实体类 ====================

class SemanticSearchRequest(BaseModel):
    """语义相似度搜索请求实体"""
    product_name: str = Field(..., description="非标准产品词", example="新能源汽车电池")
    similarity_threshold: Optional[float] = Field(0.5, description="语义相似度阈值", example=0.5, ge=0.0, le=1.0)
    limit: Optional[int] = Field(10, description="返回结果数量限制", example=10, ge=1, le=100)


class SemanticSearchResponse(BaseModel):
    """语义相似度搜索响应实体"""
    standard_word: str = Field(..., description="标准词", example="锂电池")
    similarity_score: float = Field(..., description="相似度分数", example=0.85, ge=0.0, le=1.0)


class IndustryChainSearchRequest(BaseModel):
    """产业链相似度搜索请求实体"""
    standard_product_name: str = Field(..., description="标准产品词", example="锂电池")
    k_parameter: Optional[float] = Field(1.0, description="距离衰减参数k", example=1.0, ge=0.1, le=10.0)


class IndustryChainSearchResponse(BaseModel):
    """产业链相似度搜索响应实体"""
    standard_word: str = Field(..., description="标准词", example="电动汽车")
    similarity_score: float = Field(..., description="产业链相似度分数", example=0.67, ge=0.0, le=1.0)
    distance: int = Field(..., description="产业链距离", example=2, ge=0)


class ComprehensiveSearchRequest(BaseModel):
    """综合相似度搜索请求实体"""
    product_name: str = Field(..., description="非标准产品词", example="新能源汽车电池")
    semantic_threshold: Optional[float] = Field(0.5, description="语义相似度阈值", example=0.5, ge=0.0, le=1.0)
    alpha: Optional[float] = Field(0.7, description="语义相似度权重", example=0.7, ge=0.0, le=1.0)
    k_parameter: Optional[float] = Field(1.0, description="产业链距离衰减参数", example=1.0, ge=0.1, le=10.0)


class ComprehensiveSearchResponse(BaseModel):
    """综合相似度搜索响应实体"""
    standard_word: str = Field(..., description="标准词", example="锂电池")
    word_type: str = Field(..., description="词类型：语义/产业链", example="语义")
    overall_similarity: float = Field(..., description="总体相似度", example=0.78, ge=0.0, le=1.0)
    semantic_similarity: float = Field(..., description="语义相似度", example=0.85, ge=0.0, le=1.0)
    industry_similarity: Optional[float] = Field(None, description="产业链相似度", example=0.67, ge=0.0, le=1.0)
    llm_generated_source: Optional[str] = Field(None, description="LLM生成的源产品（仅当使用LLM保底逻辑时）", example="电动汽车")
    industry_distance: Optional[int] = Field(None, description="产业链距离（仅当为产业链类型时）", example=1, ge=0)


class StandardWordMatchRequest(BaseModel):
    """标准词匹配请求实体"""
    product_name: str = Field(..., description="非标准产品词", example="新能源汽车电池")
    similarity_threshold: Optional[float] = Field(0.5, description="语义相似度阈值", example=0.5, ge=0.0, le=1.0)


class StandardWordMatchResponse(BaseModel):
    """标准词匹配响应实体"""
    standard_word: str = Field(..., description="最匹配的标准词", example="锂电池")
    similarity_score: float = Field(..., description="相似度分数", example=0.85, ge=0.0, le=1.0)


class ComprehensiveSearchListRequest(BaseModel):
    """综合相似度搜索列表请求实体"""
    product_name: str = Field(..., description="非标准产品词", example="新能源汽车电池")
    semantic_threshold: Optional[float] = Field(0.5, description="语义相似度阈值", example=0.5, ge=0.0, le=1.0)
    overall_threshold: Optional[float] = Field(0.3, description="总体相似度阈值", example=0.3, ge=0.0, le=1.0)
    limit: Optional[int] = Field(20, description="输出词列表个数", example=20, ge=1, le=100)
    alpha: Optional[float] = Field(0.7, description="语义相似度权重", example=0.7, ge=0.0, le=1.0)
    k_parameter: Optional[float] = Field(1.0, description="产业链距离衰减参数", example=1.0, ge=0.1, le=10.0)


class ComprehensiveSearchListResponse(BaseModel):
    """综合相似度搜索列表响应实体"""
    results: List[ComprehensiveSearchResponse] = Field(..., description="搜索结果列表")
    total_count: int = Field(..., description="总结果数量", example=15)


# ==================== 向量数据库更新相关实体 ====================

class VectorUpdateRequest(BaseModel):
    """向量数据库更新请求实体"""
    task_id: Optional[int] = Field(None, description="任务ID，为空时更新所有", example=1)
    force_update: Optional[bool] = Field(False, description="是否强制全量更新", example=False)


class VectorUpdateResponse(BaseModel):
    """向量数据库更新响应实体"""
    updated_count: int = Field(..., description="更新的记录数", example=150)
    total_count: int = Field(..., description="总记录数", example=1000)
    is_incremental: bool = Field(..., description="是否为增量更新", example=True)
    update_time: datetime = Field(..., description="更新时间")
