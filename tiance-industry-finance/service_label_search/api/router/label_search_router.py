﻿#!/usr/bin/env python
# -*- encoding: utf-8 -*-
'''
@Time    :   2025/09/04 17:08:37
<AUTHOR>   WEIHA<PERSON> HONG 
@Email    :   <EMAIL>
@File    :   label_search_router.py
@Project    :   tiance-industry-finance
'''

from fastapi import APIRouter, HTTPException, Query, Body
from typing import Optional, List
import asyncio
from datetime import datetime
from entity.response_entity import SuccessResponse, FalseResponse
from utils.log_util import LogUtil
from service_label_search.entity.label_search_entity import (
    StandardWordMatchRequest, StandardWordMatchResponse,
    ComprehensiveSearchListRequest, ComprehensiveSearchListResponse,
    ComprehensiveSearchResponse, VectorUpdateRequest, VectorUpdateResponse
)
from service_label_search.service.semantic_search_service import semantic_search_service
from service_label_search.service.comprehensive_search_service import comprehensive_search_service
from service_label_search.service.vector_update_service import vector_update_service
from service_label_search.configs.label_search_config import LabelSearchConfig

api_router = APIRouter()


@api_router.post("/match_standard_word", 
                response_model=SuccessResponse,
                summary="标准词匹配",
                description="输入非标准产品词，返回最高相似度的标准词")
async def match_standard_word(request: StandardWordMatchRequest):
    """
    标准词匹配接口
    输入一个非标准产品词，返回一个相似度最高的标准词
    """
    try:
        LogUtil.info(f"标准词匹配请求: {request.model_dump()}")
        
        # 获取最佳匹配
        best_match = await semantic_search_service.get_best_match(
            product_name=request.product_name,
            similarity_threshold=request.similarity_threshold,
            task_id=None  # 暂时不限制任务ID
        )
        
        if best_match:
            response_data = StandardWordMatchResponse(
                standard_word=best_match[0],
                similarity_score=best_match[1]
            )
            
            LogUtil.info(f"标准词匹配成功: {request.product_name} -> {best_match[0]} (相似度: {best_match[1]:.4f})")
            return SuccessResponse(data=response_data.model_dump())
        else:
            LogUtil.warn(f"未找到满足阈值的标准词: {request.product_name} (阈值: {request.similarity_threshold})")
            return SuccessResponse(
                data={},
                message=f"未找到相似度大于 {request.similarity_threshold} 的标准词"
            )
            
    except ValueError as e:
        LogUtil.error(f"标准词匹配参数错误: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        LogUtil.error(f"标准词匹配失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"标准词匹配失败: {str(e)}")


@api_router.get("/match_standard_word_simple",
               response_model=SuccessResponse,
               summary="标准词匹配（简化版）",
               description="使用GET方法的简化标准词匹配接口")
async def match_standard_word_simple(
    product_name: str = Query(..., description="非标准产品词", example="新能源汽车电池"),
    similarity_threshold: Optional[float] = Query(0.5, description="语义相似度阈值", ge=0.0, le=1.0)
):
    """
    标准词匹配接口（GET方法）
    """
    try:
        # 创建请求对象
        request = StandardWordMatchRequest(
            product_name=product_name,
            similarity_threshold=similarity_threshold
        )
        
        # 调用POST接口的逻辑
        return await match_standard_word(request)
        
    except Exception as e:
        LogUtil.error(f"简化标准词匹配失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"标准词匹配失败: {str(e)}")


@api_router.post("/comprehensive_search",
                response_model=SuccessResponse,
                summary="综合相似度搜索",
                description="综合语义相似度和产业链相似度的搜索接口")
async def comprehensive_search(request: ComprehensiveSearchListRequest):
    """
    综合相似度搜索接口
    输入非标准产品词，返回综合相似度排序的标准词列表
    """
    try:
        LogUtil.info(f"综合相似度搜索请求: {request.model_dump()}")
        
        # 执行综合搜索
        search_results = await comprehensive_search_service.comprehensive_search(
            product_name=request.product_name,
            semantic_threshold=request.semantic_threshold,
            overall_threshold=request.overall_threshold,
            alpha=request.alpha,
            k_parameter=request.k_parameter,
            limit=request.limit,
            task_id=None  # 暂时不限制任务ID
        )
        
        # 转换为响应格式
        response_results = []
        for result in search_results:
            response_item = ComprehensiveSearchResponse(
                standard_word=result["product_name"],
                word_type=result["word_type"],
                overall_similarity=result["overall_similarity"],
                semantic_similarity=result["semantic_similarity"],
                industry_similarity=result.get("industry_similarity"),
                llm_generated_source=result.get("llm_generated_source"),
                industry_distance=result.get("industry_distance")
            )
            response_results.append(response_item)
        
        response_data = ComprehensiveSearchListResponse(
            results=response_results,
            total_count=len(response_results)
        )
        
        LogUtil.info(f"综合相似度搜索成功: {request.product_name} -> {len(response_results)} 个结果")
        return SuccessResponse(data=response_data.model_dump())
        
    except ValueError as e:
        LogUtil.error(f"综合相似度搜索参数错误: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        LogUtil.error(f"综合相似度搜索失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"综合相似度搜索失败: {str(e)}")


@api_router.get("/comprehensive_search_simple",
               response_model=SuccessResponse,
               summary="综合相似度搜索（简化版）",
               description="使用GET方法的简化综合相似度搜索接口")
async def comprehensive_search_simple(
    product_name: str = Query(..., description="非标准产品词", example="新能源汽车电池"),
    semantic_threshold: Optional[float] = Query(0.5, description="语义相似度阈值", ge=0.0, le=1.0),
    overall_threshold: Optional[float] = Query(0.3, description="总体相似度阈值", ge=0.0, le=1.0),
    limit: Optional[int] = Query(20, description="输出词列表个数", ge=1, le=100),
    alpha: Optional[float] = Query(0.7, description="语义相似度权重", ge=0.0, le=1.0),
    k_parameter: Optional[float] = Query(1.0, description="产业链距离衰减参数", ge=0.1, le=10.0)
):
    """
    综合相似度搜索接口（GET方法）
    """
    try:
        # 创建请求对象
        request = ComprehensiveSearchListRequest(
            product_name=product_name,
            semantic_threshold=semantic_threshold,
            overall_threshold=overall_threshold,
            limit=limit,
            alpha=alpha,
            k_parameter=k_parameter
        )
        
        # 调用POST接口的逻辑
        return await comprehensive_search(request)
        
    except Exception as e:
        LogUtil.error(f"简化综合相似度搜索失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"综合相似度搜索失败: {str(e)}")


@api_router.post("/update_vector_database",
                response_model=SuccessResponse,
                summary="更新向量数据库",
                description="手动触发向量数据库更新")
async def update_vector_database(request: VectorUpdateRequest):
    """
    向量数据库更新接口
    """
    try:
        LogUtil.info(f"向量数据库更新请求: {request.model_dump()}")
        
        # 执行向量数据库更新
        update_result = await vector_update_service.check_and_update(
            task_id=request.task_id,
            force_full_update=request.force_update
        )
        
        response_data = VectorUpdateResponse(
            updated_count=update_result["updated_count"],
            total_count=update_result["total_count"],
            is_incremental=update_result["update_type"] == "incremental",
            update_time=update_result["update_time"]
        )
        
        LogUtil.info(f"向量数据库更新成功: {update_result}")
        return SuccessResponse(
            data=response_data.model_dump(),
            message=LabelSearchConfig.SUCCESS_MESSAGES["VECTOR_UPDATE_SUCCESS"]
        )
        
    except Exception as e:
        LogUtil.error(f"向量数据库更新失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"向量数据库更新失败: {str(e)}")


@api_router.get("/health",
               response_model=SuccessResponse,
               summary="健康检查",
               description="检查标签搜索服务状态")
async def health_check():
    """
    健康检查接口
    """
    try:
        # 检查向量数据库连接
        collection_exists = vector_update_service.milvus_util.collection_is_exists(
            vector_update_service.collection_name
        )
        
        health_info = {
            "service": "label_search",
            "status": "healthy",
            "vector_collection_exists": collection_exists,
            "timestamp": datetime.now().isoformat()
        }
        
        return SuccessResponse(data=health_info, message="服务运行正常")
        
    except Exception as e:
        LogUtil.error(f"健康检查失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"健康检查失败: {str(e)}")


@api_router.get("/config",
               response_model=SuccessResponse,
               summary="获取配置信息",
               description="获取当前服务配置信息")
async def get_config():
    """
    获取配置信息接口
    """
    try:
        config_info = {
            "collection_name": LabelSearchConfig.PRODUCT_VECTOR_COLLECTION,
            "default_semantic_threshold": LabelSearchConfig.DEFAULT_SEMANTIC_THRESHOLD,
            "default_overall_threshold": LabelSearchConfig.DEFAULT_OVERALL_THRESHOLD,
            "default_alpha": LabelSearchConfig.DEFAULT_ALPHA,
            "default_k_parameter": LabelSearchConfig.DEFAULT_K_PARAMETER,
            "default_search_limit": LabelSearchConfig.DEFAULT_SEARCH_LIMIT,
            "max_search_limit": LabelSearchConfig.MAX_SEARCH_LIMIT,
            "vector_dimension": LabelSearchConfig.VECTOR_DIMENSION
        }
        
        return SuccessResponse(data=config_info, message="配置信息获取成功")
        
    except Exception as e:
        LogUtil.error(f"获取配置信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取配置信息失败: {str(e)}")