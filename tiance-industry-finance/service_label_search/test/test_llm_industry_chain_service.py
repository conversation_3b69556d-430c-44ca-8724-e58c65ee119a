#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@File         :test_llm_industry_chain_service.py
@Description  :LLM产业链生成服务单元测试
<AUTHOR> HONG
@Date         :2025/09/08
"""

import unittest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# 初始化日志
from utils.log_util import LogUtil
LogUtil.init(process_name="test_llm_industry_chain_service")

from service_label_search.service.llm_industry_chain_service import LlmIndustryChainService


class TestLlmIndustryChainService(unittest.TestCase):
    """LLM产业链生成服务测试类"""
    
    def setUp(self):
        """测试前置设置"""
        self.service = LlmIndustryChainService()
        
        # Mock LLM服务
        self.mock_llm_service = Mock()
        self.service.llm_service = self.mock_llm_service
    
    def test_build_industry_chain_prompt(self):
        """测试构建产业链生成prompt"""
        product_name = "锂电池"
        prompt = self.service._build_industry_chain_prompt(product_name)
        
        # 验证prompt包含必要内容
        self.assertIn(product_name, prompt)
        self.assertIn("产业链", prompt)
        self.assertIn("JSON", prompt)
        self.assertIn("products", prompt)
    
    def test_parse_llm_response_valid_json(self):
        """测试解析有效JSON响应"""
        response = '''
        {
            "products": [
                "锂矿",
                "电解液",
                "正极材料",
                "负极材料",
                "电动汽车"
            ]
        }
        '''
        
        products = self.service._parse_llm_response(response)
        
        self.assertEqual(len(products), 5)
        self.assertIn("锂矿", products)
        self.assertIn("电解液", products)
        self.assertIn("电动汽车", products)
    
    def test_parse_llm_response_invalid_json(self):
        """测试解析无效JSON响应"""
        response = '''
        根据锂电池的产业链，相关产品包括：
        1. 锂矿
        2. 电解液
        3. 正极材料
        4. 负极材料
        5. 电动汽车
        '''
        
        products = self.service._parse_llm_response(response)
        
        # 应该能够通过备用解析方式获得结果
        self.assertGreater(len(products), 0)
        self.assertIn("锂矿", products)
        self.assertIn("电解液", products)
    
    def test_parse_llm_response_empty(self):
        """测试解析空响应"""
        response = ""
        products = self.service._parse_llm_response(response)
        self.assertEqual(len(products), 0)
    
    def test_parse_llm_response_malformed(self):
        """测试解析格式错误的响应"""
        response = "这是一个无法解析的响应内容，没有任何有用信息。"
        products = self.service._parse_llm_response(response)
        self.assertEqual(len(products), 0)
    
    def test_validate_generated_products(self):
        """测试验证生成的产品列表"""
        products = [
            "锂电池",
            "电动汽车",
            "",  # 空字符串
            "这是一个非常长的产品名称超过了二十个字符的限制",  # 过长
            "1. 带数字开头的产品",  # 数字开头
            "正常产品名称",
            "Product123",  # 纯英文
            "包含<特殊>符号的产品",  # 特殊符号
            "示例产品",  # 包含说明性文字
        ]
        
        validated = self.service.validate_generated_products(products)
        
        # 应该只保留有效的产品
        self.assertIn("锂电池", validated)
        self.assertIn("电动汽车", validated)
        self.assertIn("正常产品名称", validated)
        
        # 无效产品应该被过滤掉
        self.assertNotIn("", validated)
        self.assertNotIn("这是一个非常长的产品名称超过了二十个字符的限制", validated)
        self.assertNotIn("1. 带数字开头的产品", validated)
        self.assertNotIn("Product123", validated)
        self.assertNotIn("包含<特殊>符号的产品", validated)
        self.assertNotIn("示例产品", validated)
    
    def test_generate_industry_chain_products_success(self):
        """测试成功生成产业链产品"""
        product_name = "锂电池"
        
        # Mock LLM响应
        mock_response = '''
        {
            "products": [
                "锂矿",
                "电解液",
                "正极材料",
                "负极材料",
                "电动汽车",
                "储能系统"
            ]
        }
        '''
        
        self.mock_llm_service.answer_question.return_value = mock_response
        
        # 执行测试
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        products = loop.run_until_complete(
            self.service.generate_industry_chain_products(product_name)
        )
        
        # 验证结果
        self.assertGreater(len(products), 0)
        self.assertIn("锂矿", products)
        self.assertIn("电解液", products)
        self.assertIn("电动汽车", products)
        
        # 验证不包含输入产品本身
        self.assertNotIn("锂电池", products)
    
    def test_generate_industry_chain_products_empty_input(self):
        """测试空输入"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        products = loop.run_until_complete(
            self.service.generate_industry_chain_products("")
        )
        
        self.assertEqual(len(products), 0)
    
    def test_generate_industry_chain_products_llm_error(self):
        """测试LLM调用失败"""
        product_name = "锂电池"
        
        # Mock LLM抛出异常
        self.mock_llm_service.answer_question.side_effect = Exception("LLM调用失败")
        
        # 执行测试
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        products = loop.run_until_complete(
            self.service.generate_industry_chain_products(product_name)
        )
        
        # 应该返回空列表
        self.assertEqual(len(products), 0)
    
    def test_generate_industry_chain_products_invalid_response(self):
        """测试LLM返回无效响应"""
        product_name = "锂电池"
        
        # Mock LLM返回无效响应
        self.mock_llm_service.answer_question.return_value = "这是一个完全无法解析的响应，没有任何产品信息，也没有列表格式。"
        
        # 执行测试
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        products = loop.run_until_complete(
            self.service.generate_industry_chain_products(product_name)
        )
        
        # 应该返回空列表
        self.assertEqual(len(products), 0)


if __name__ == '__main__':
    unittest.main()
