#!/bin/bash

# 单元测试运行脚本
# @File         :run_unit_tests.sh
# @Description  :运行标签搜索服务的所有单元测试
# <AUTHOR> HONG
# @Date         :2025/09/04

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../../../" && pwd)"

log_info "项目根目录: $PROJECT_ROOT"
log_info "测试目录: $SCRIPT_DIR"

# 切换到项目根目录
cd "$PROJECT_ROOT"

# 检查Python环境
log_info "检查Python环境..."
if ! command -v python &> /dev/null; then
    log_error "Python未安装或不在PATH中"
    exit 1
fi

PYTHON_VERSION=$(python --version 2>&1)
log_info "Python版本: $PYTHON_VERSION"

# 检查conda环境
if [[ -n "$CONDA_DEFAULT_ENV" ]]; then
    log_info "当前conda环境: $CONDA_DEFAULT_ENV"
else
    log_warning "未检测到conda环境"
fi

# 设置Python路径
export PYTHONPATH="$PROJECT_ROOT:$PYTHONPATH"
log_info "PYTHONPATH已设置: $PYTHONPATH"

# 创建测试结果目录
TEST_RESULTS_DIR="$SCRIPT_DIR/test_results"
mkdir -p "$TEST_RESULTS_DIR"

# 测试文件列表
TEST_FILES=(
    "service_label_search/test/test_semantic_search_service.py"
    "service_label_search/test/test_industry_chain_distance_util.py"
    "service_label_search/test/test_comprehensive_search_service.py"
    "service_label_search/test/test_llm_industry_chain_service.py"
)

# 运行单个测试文件
run_test_file() {
    local test_file=$1
    local test_name=$(basename "$test_file" .py)
    local result_file="$TEST_RESULTS_DIR/${test_name}_result.txt"
    
    log_info "运行测试: $test_file"
    
    if python -m unittest "$test_file" > "$result_file" 2>&1; then
        log_success "测试通过: $test_name"
        return 0
    else
        log_error "测试失败: $test_name"
        log_error "错误详情请查看: $result_file"
        cat "$result_file"
        return 1
    fi
}

# 主测试流程
main() {
    log_info "开始运行标签搜索服务单元测试..."
    
    local total_tests=${#TEST_FILES[@]}
    local passed_tests=0
    local failed_tests=0
    
    # 运行所有测试
    for test_file in "${TEST_FILES[@]}"; do
        if run_test_file "$test_file"; then
            ((passed_tests++))
        else
            ((failed_tests++))
        fi
        echo ""
    done
    
    # 生成测试报告
    local report_file="$TEST_RESULTS_DIR/test_summary.txt"
    {
        echo "标签搜索服务单元测试报告"
        echo "=========================="
        echo "测试时间: $(date)"
        echo "总测试数: $total_tests"
        echo "通过测试: $passed_tests"
        echo "失败测试: $failed_tests"
        echo ""
        
        if [[ $failed_tests -eq 0 ]]; then
            echo "所有测试均通过！"
        else
            echo "存在失败的测试，请检查具体错误信息。"
        fi
    } > "$report_file"
    
    # 显示测试结果
    log_info "测试完成！"
    log_info "总测试数: $total_tests"
    log_success "通过测试: $passed_tests"
    
    if [[ $failed_tests -gt 0 ]]; then
        log_error "失败测试: $failed_tests"
        log_info "详细报告: $report_file"
        exit 1
    else
        log_success "所有测试均通过！"
        log_info "详细报告: $report_file"
    fi
}

# 清理函数
cleanup() {
    log_info "清理测试环境..."
    # 这里可以添加清理逻辑
}

# 设置退出时清理
trap cleanup EXIT

# 运行主函数
main "$@"
