#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@File         :test_comprehensive_search_service.py
@Description  :综合搜索服务单元测试
<AUTHOR> HONG
@Date         :2025/09/04
"""

import unittest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# 初始化日志
from utils.log_util import LogUtil
LogUtil.init(process_name="test_comprehensive_search_service")

from service_label_search.service.comprehensive_search_service import ComprehensiveSearchService


class TestComprehensiveSearchService(unittest.TestCase):
    """综合搜索服务测试类"""
    
    def setUp(self):
        """测试前置设置"""
        self.service = ComprehensiveSearchService()
        
        # Mock依赖服务
        self.mock_semantic_service = Mock()
        self.mock_industry_service = Mock()
        self.mock_llm_service = Mock()
        self.service.semantic_service = self.mock_semantic_service
        self.service.industry_service = self.mock_industry_service
        self.service.llm_service = self.mock_llm_service
    
    def test_calculate_comprehensive_similarity(self):
        """测试计算综合相似度"""
        # 准备测试数据
        results = {
            "产品A": {
                "semantic_similarity": 0.8,
                "industry_similarity": 0.6
            },
            "产品B": {
                "semantic_similarity": 0.7,
                "industry_similarity": 0.9
            },
            "产品C": {
                "semantic_similarity": 0.9,
                "industry_similarity": None  # 测试None值处理
            }
        }
        alpha = 0.7
        
        # 执行测试
        updated_results = self.service.calculate_comprehensive_similarity(results, alpha)
        
        # 验证结果
        # 产品A: 0.7 * 0.8 + 0.3 * 0.6 = 0.56 + 0.18 = 0.74
        self.assertAlmostEqual(updated_results["产品A"]["overall_similarity"], 0.74, places=6)
        
        # 产品B: 0.7 * 0.7 + 0.3 * 0.9 = 0.49 + 0.27 = 0.76
        self.assertAlmostEqual(updated_results["产品B"]["overall_similarity"], 0.76, places=6)
        
        # 产品C: 0.7 * 0.9 + 0.3 * 0.0 = 0.63 + 0.0 = 0.63
        self.assertAlmostEqual(updated_results["产品C"]["overall_similarity"], 0.63, places=6)
    
    def test_filter_and_sort_results(self):
        """测试过滤和排序结果"""
        # 准备测试数据
        results = {
            "产品A": {"overall_similarity": 0.8, "product_name": "产品A"},
            "产品B": {"overall_similarity": 0.6, "product_name": "产品B"},
            "产品C": {"overall_similarity": 0.4, "product_name": "产品C"},
            "产品D": {"overall_similarity": 0.9, "product_name": "产品D"},
            "产品E": {"overall_similarity": 0.2, "product_name": "产品E"}
        }
        overall_threshold = 0.5
        limit = 3
        
        # 执行测试
        filtered_results = self.service.filter_and_sort_results(results, overall_threshold, limit)
        
        # 验证结果
        self.assertEqual(len(filtered_results), 3)
        self.assertEqual(filtered_results[0]["product_name"], "产品D")  # 0.9
        self.assertEqual(filtered_results[1]["product_name"], "产品A")  # 0.8
        self.assertEqual(filtered_results[2]["product_name"], "产品B")  # 0.6
    
    def test_get_semantic_similarity_results(self):
        """测试获取语义相似度结果"""
        # 准备测试数据
        product_name = "新能源汽车电池"
        semantic_threshold = 0.5
        task_id = 1
        
        mock_semantic_results = [
            {
                "product_id": 1,
                "product_name": "锂电池",
                "product_source": "产品词",
                "task_id": 1,
                "version": 1,
                "product_ids": "1,2,3",
                "industry_last_node_code": "NODE001",
                "similarity_score": 0.85,
                "distance": 0.15
            }
        ]
        
        # Mock语义搜索服务
        self.mock_semantic_service.get_detailed_search_results = AsyncMock(
            return_value=mock_semantic_results
        )
        
        # 执行测试
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        results = loop.run_until_complete(
            self.service.get_semantic_similarity_results(product_name, semantic_threshold, task_id)
        )
        
        # 验证结果
        self.assertEqual(len(results), 1)
        self.assertIn("锂电池", results)
        self.assertEqual(results["锂电池"]["semantic_similarity"], 0.85)
        self.assertEqual(results["锂电池"]["word_type"], "语义")
    
    def test_get_industry_similarity_results(self):
        """测试获取产业链相似度结果"""
        # 准备测试数据
        semantic_results = {
            "锂电池": {
                "product_id": 1,
                "product_name": "锂电池",
                "task_id": 1,
                "semantic_similarity": 0.85
            }
        }
        k_parameter = 1.0
        product_name = "新能源汽车电池"
        
        mock_industry_results = [
            {
                "product_id": 2,
                "product_name": "电动汽车",
                "product_source": "产品词",
                "task_id": 1,
                "version": 1,
                "product_ids": "4,5,6",
                "industry_last_node_code": "NODE002",
                "industry_similarity": 0.67,
                "industry_distance": 1
            }
        ]
        
        # Mock产业链搜索服务
        self.mock_industry_service.get_industry_chain_products_with_details.return_value = mock_industry_results
        
        # Mock语义相似度计算
        with patch.object(self.service, '_calculate_semantic_similarity_for_industry_product') as mock_calc:
            mock_calc.return_value = 0.6
            
            # 执行测试
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            results = loop.run_until_complete(
                self.service.get_industry_similarity_results(semantic_results, k_parameter, product_name)
            )
            
            # 验证结果
            self.assertEqual(len(results), 1)
            self.assertIn("电动汽车", results)
            self.assertEqual(results["电动汽车"]["industry_similarity"], 0.67)
            self.assertEqual(results["电动汽车"]["semantic_similarity"], 0.6)
            self.assertEqual(results["电动汽车"]["word_type"], "产业链")
    
    def test_comprehensive_search_success(self):
        """测试综合搜索成功"""
        # 准备测试数据
        product_name = "新能源汽车电池"
        semantic_threshold = 0.5
        overall_threshold = 0.3
        alpha = 0.7
        k_parameter = 1.0
        limit = 10
        
        # Mock语义搜索结果
        mock_semantic_results = {
            "锂电池": {
                "product_name": "锂电池",
                "semantic_similarity": 0.85,
                "word_type": "语义"
            }
        }
        
        # Mock产业链搜索结果
        mock_industry_results = {
            "电动汽车": {
                "product_name": "电动汽车",
                "semantic_similarity": 0.6,
                "industry_similarity": 0.67,
                "word_type": "产业链"
            }
        }
        
        # Mock方法
        with patch.object(self.service, 'get_semantic_similarity_results') as mock_semantic, \
             patch.object(self.service, 'get_industry_similarity_results') as mock_industry:
            
            mock_semantic.return_value = mock_semantic_results
            mock_industry.return_value = mock_industry_results
            
            # 执行测试
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            results = loop.run_until_complete(
                self.service.comprehensive_search(
                    product_name=product_name,
                    semantic_threshold=semantic_threshold,
                    overall_threshold=overall_threshold,
                    alpha=alpha,
                    k_parameter=k_parameter,
                    limit=limit
                )
            )
            
            # 验证结果
            self.assertEqual(len(results), 2)
            # 验证综合相似度计算
            for result in results:
                self.assertIn("overall_similarity", result)
    
    def test_comprehensive_search_invalid_params(self):
        """测试综合搜索参数验证"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        # 测试空产品名称
        with self.assertRaises(ValueError):
            loop.run_until_complete(
                self.service.comprehensive_search(product_name="")
            )
        
        # 测试无效语义阈值
        with self.assertRaises(ValueError):
            loop.run_until_complete(
                self.service.comprehensive_search(
                    product_name="测试产品",
                    semantic_threshold=1.5
                )
            )
        
        # 测试无效alpha值
        with self.assertRaises(ValueError):
            loop.run_until_complete(
                self.service.comprehensive_search(
                    product_name="测试产品",
                    alpha=1.5
                )
            )
        
        # 测试无效k参数
        with self.assertRaises(ValueError):
            loop.run_until_complete(
                self.service.comprehensive_search(
                    product_name="测试产品",
                    k_parameter=0.05
                )
            )
        
        # 测试无效限制数量
        with self.assertRaises(ValueError):
            loop.run_until_complete(
                self.service.comprehensive_search(
                    product_name="测试产品",
                    limit=0
                )
            )
    
    def test_comprehensive_search_no_semantic_results_with_llm_fallback(self):
        """测试综合搜索无语义结果时启用LLM保底逻辑"""
        # Mock语义搜索返回空结果
        mock_llm_fallback_results = {
            "相关产品1": {
                "product_name": "相关产品1",
                "semantic_similarity": 0.7,
                "industry_similarity": 0.368,
                "word_type": "产业链",
                "llm_generated_source": "生成产品A",
                "industry_distance": 1
            }
        }

        with patch.object(self.service, 'get_semantic_similarity_results') as mock_semantic, \
             patch.object(self.service, 'get_llm_fallback_results') as mock_llm:

            mock_semantic.return_value = {}
            mock_llm.return_value = mock_llm_fallback_results

            # 执行测试
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            results = loop.run_until_complete(
                self.service.comprehensive_search(product_name="不存在的产品")
            )

            # 验证结果
            self.assertEqual(len(results), 1)
            self.assertEqual(results[0]["product_name"], "相关产品1")
            self.assertEqual(results[0]["word_type"], "产业链")
            self.assertIn("llm_generated_source", results[0])

    def test_comprehensive_search_no_semantic_and_no_llm_results(self):
        """测试综合搜索无语义结果且LLM保底逻辑也无结果"""
        # Mock方法返回空结果
        with patch.object(self.service, 'get_semantic_similarity_results') as mock_semantic, \
             patch.object(self.service, 'get_llm_fallback_results') as mock_llm:

            mock_semantic.return_value = {}
            mock_llm.return_value = {}

            # 执行测试
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            results = loop.run_until_complete(
                self.service.comprehensive_search(product_name="完全不存在的产品")
            )

            # 验证结果
            self.assertEqual(len(results), 0)

    def test_get_llm_fallback_results_success(self):
        """测试LLM保底逻辑成功"""
        product_name = "新产品"
        semantic_threshold = 0.5
        task_id = 1

        # Mock LLM生成的产品
        mock_generated_products = ["相关产品A", "相关产品B"]

        # Mock语义搜索结果
        mock_semantic_results = [
            {
                "product_id": 1,
                "product_name": "找到的产品1",
                "product_source": "产品词",
                "task_id": 1,
                "version": 1,
                "product_ids": "1,2",
                "industry_last_node_code": "NODE001",
                "similarity_score": 0.7,
                "distance": 0.3
            }
        ]

        # Mock服务
        self.mock_llm_service.generate_industry_chain_products = AsyncMock(
            return_value=mock_generated_products
        )
        self.mock_semantic_service.get_detailed_search_results = AsyncMock(
            return_value=mock_semantic_results
        )

        # 执行测试
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        results = loop.run_until_complete(
            self.service.get_llm_fallback_results(product_name, semantic_threshold, task_id)
        )

        # 验证结果
        self.assertEqual(len(results), 1)
        self.assertIn("找到的产品1", results)
        result = results["找到的产品1"]
        self.assertEqual(result["word_type"], "产业链")
        self.assertEqual(result["industry_distance"], 1)
        self.assertIn("llm_generated_source", result)

    def test_get_llm_fallback_results_no_generated_products(self):
        """测试LLM保底逻辑无生成产品"""
        product_name = "新产品"
        semantic_threshold = 0.5

        # Mock LLM返回空列表
        self.mock_llm_service.generate_industry_chain_products = AsyncMock(
            return_value=[]
        )

        # 执行测试
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        results = loop.run_until_complete(
            self.service.get_llm_fallback_results(product_name, semantic_threshold)
        )

        # 验证结果
        self.assertEqual(len(results), 0)

    def test_get_llm_fallback_results_llm_error(self):
        """测试LLM保底逻辑LLM调用失败"""
        product_name = "新产品"
        semantic_threshold = 0.5

        # Mock LLM抛出异常
        self.mock_llm_service.generate_industry_chain_products = AsyncMock(
            side_effect=Exception("LLM调用失败")
        )

        # 执行测试
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        results = loop.run_until_complete(
            self.service.get_llm_fallback_results(product_name, semantic_threshold)
        )

        # 验证结果
        self.assertEqual(len(results), 0)


if __name__ == '__main__':
    unittest.main()
