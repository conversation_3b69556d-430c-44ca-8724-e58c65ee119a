#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@File         :test_api_llm_fallback.py
@Description  :API层面的LLM保底逻辑测试
<AUTHOR> HONG
@Date         :2025/09/08
"""

import requests
import json
import time
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))


class ApiLlmFallbackTester:
    """API层面的LLM保底逻辑测试器"""
    
    def __init__(self, base_url="http://localhost:8000"):
        """
        初始化测试器
        :param base_url: API服务的基础URL
        """
        self.base_url = base_url
        self.api_prefix = "/tc/llm/base/label_search/v1"
        
    def test_comprehensive_search_with_fallback(self):
        """测试综合搜索的LLM保底逻辑"""
        
        # 测试用例：使用一个不太可能存在的产品名称
        test_cases = [
            {
                "product_name": "超级新型未知产品XYZ123",
                "description": "完全不存在的产品，应该触发LLM保底逻辑"
            },
            {
                "product_name": "神秘量子材料ABC999",
                "description": "虚构的材料名称，测试LLM生成能力"
            },
            {
                "product_name": "未来科技产品DEF888",
                "description": "未来概念产品，验证保底机制"
            }
        ]
        
        results = []
        
        for test_case in test_cases:
            print(f"\n测试用例: {test_case['description']}")
            print(f"产品名称: {test_case['product_name']}")
            
            # 构建请求
            url = f"{self.base_url}{self.api_prefix}/comprehensive_search"
            payload = {
                "product_name": test_case["product_name"],
                "semantic_threshold": 0.5,
                "overall_threshold": 0.3,
                "limit": 10,
                "alpha": 0.7,
                "k_parameter": 1.0
            }
            
            try:
                # 发送请求
                response = requests.post(url, json=payload, timeout=30)
                
                if response.status_code == 200:
                    data = response.json()
                    
                    if data.get("code") == 200:
                        search_results = data.get("data", {}).get("results", [])
                        
                        print(f"✅ 请求成功，返回 {len(search_results)} 个结果")
                        
                        # 检查是否有LLM保底逻辑的结果
                        llm_results = [r for r in search_results if r.get("llm_generated_source")]
                        
                        if llm_results:
                            print(f"🎯 检测到 {len(llm_results)} 个LLM保底逻辑结果:")
                            for i, result in enumerate(llm_results[:3]):  # 只显示前3个
                                print(f"  {i+1}. {result.get('standard_word')} "
                                      f"(类型: {result.get('word_type')}, "
                                      f"综合相似度: {result.get('overall_similarity'):.3f}, "
                                      f"LLM源: {result.get('llm_generated_source')})")
                        else:
                            print("⚠️  未检测到LLM保底逻辑结果")
                        
                        results.append({
                            "test_case": test_case,
                            "success": True,
                            "total_results": len(search_results),
                            "llm_results": len(llm_results),
                            "results": search_results[:5]  # 保存前5个结果
                        })
                    else:
                        print(f"❌ API返回错误: {data.get('message', '未知错误')}")
                        results.append({
                            "test_case": test_case,
                            "success": False,
                            "error": data.get('message', '未知错误')
                        })
                else:
                    print(f"❌ HTTP错误: {response.status_code}")
                    results.append({
                        "test_case": test_case,
                        "success": False,
                        "error": f"HTTP {response.status_code}"
                    })
                    
            except requests.exceptions.RequestException as e:
                print(f"❌ 请求异常: {str(e)}")
                results.append({
                    "test_case": test_case,
                    "success": False,
                    "error": str(e)
                })
            
            # 等待一下避免请求过快
            time.sleep(1)
        
        return results
    
    def test_normal_search_without_fallback(self):
        """测试正常搜索不触发LLM保底逻辑"""
        
        # 使用常见的产品名称，应该有语义搜索结果
        test_cases = [
            "锂电池",
            "电动汽车",
            "太阳能电池板"
        ]
        
        results = []
        
        for product_name in test_cases:
            print(f"\n测试正常搜索: {product_name}")
            
            url = f"{self.base_url}{self.api_prefix}/comprehensive_search"
            payload = {
                "product_name": product_name,
                "semantic_threshold": 0.5,
                "overall_threshold": 0.3,
                "limit": 10,
                "alpha": 0.7,
                "k_parameter": 1.0
            }
            
            try:
                response = requests.post(url, json=payload, timeout=30)
                
                if response.status_code == 200:
                    data = response.json()
                    
                    if data.get("code") == 200:
                        search_results = data.get("data", {}).get("results", [])
                        
                        # 检查结果类型
                        semantic_results = [r for r in search_results if r.get("word_type") == "语义"]
                        industry_results = [r for r in search_results if r.get("word_type") == "产业链" and not r.get("llm_generated_source")]
                        llm_results = [r for r in search_results if r.get("llm_generated_source")]
                        
                        print(f"✅ 语义结果: {len(semantic_results)}, 产业链结果: {len(industry_results)}, LLM结果: {len(llm_results)}")
                        
                        if llm_results:
                            print("⚠️  意外检测到LLM保底逻辑结果（正常搜索不应该触发）")
                        
                        results.append({
                            "product_name": product_name,
                            "success": True,
                            "semantic_count": len(semantic_results),
                            "industry_count": len(industry_results),
                            "llm_count": len(llm_results)
                        })
                    else:
                        print(f"❌ API返回错误: {data.get('message', '未知错误')}")
                        results.append({
                            "product_name": product_name,
                            "success": False,
                            "error": data.get('message', '未知错误')
                        })
                else:
                    print(f"❌ HTTP错误: {response.status_code}")
                    results.append({
                        "product_name": product_name,
                        "success": False,
                        "error": f"HTTP {response.status_code}"
                    })
                    
            except requests.exceptions.RequestException as e:
                print(f"❌ 请求异常: {str(e)}")
                results.append({
                    "product_name": product_name,
                    "success": False,
                    "error": str(e)
                })
            
            time.sleep(1)
        
        return results
    
    def run_all_tests(self):
        """运行所有测试"""
        print("=" * 60)
        print("LLM保底逻辑API测试")
        print("=" * 60)
        
        # 测试服务是否可用
        try:
            health_url = f"{self.base_url}{self.api_prefix}/health"
            response = requests.get(health_url, timeout=10)
            if response.status_code == 200:
                print("✅ 服务健康检查通过")
            else:
                print(f"⚠️  服务健康检查异常: {response.status_code}")
        except Exception as e:
            print(f"❌ 无法连接到服务: {str(e)}")
            print("请确保服务已启动并运行在正确的端口上")
            return
        
        # 测试LLM保底逻辑
        print("\n" + "=" * 40)
        print("测试1: LLM保底逻辑")
        print("=" * 40)
        fallback_results = self.test_comprehensive_search_with_fallback()
        
        # 测试正常搜索
        print("\n" + "=" * 40)
        print("测试2: 正常搜索（不应触发LLM保底逻辑）")
        print("=" * 40)
        normal_results = self.test_normal_search_without_fallback()
        
        # 生成测试报告
        print("\n" + "=" * 60)
        print("测试报告")
        print("=" * 60)
        
        # LLM保底逻辑测试报告
        successful_fallback = sum(1 for r in fallback_results if r.get("success"))
        total_fallback = len(fallback_results)
        print(f"LLM保底逻辑测试: {successful_fallback}/{total_fallback} 成功")
        
        llm_triggered = sum(1 for r in fallback_results if r.get("success") and r.get("llm_results", 0) > 0)
        print(f"成功触发LLM保底逻辑: {llm_triggered}/{successful_fallback}")
        
        # 正常搜索测试报告
        successful_normal = sum(1 for r in normal_results if r.get("success"))
        total_normal = len(normal_results)
        print(f"正常搜索测试: {successful_normal}/{total_normal} 成功")
        
        unexpected_llm = sum(1 for r in normal_results if r.get("success") and r.get("llm_count", 0) > 0)
        if unexpected_llm > 0:
            print(f"⚠️  意外触发LLM保底逻辑: {unexpected_llm} 次")
        else:
            print("✅ 正常搜索未意外触发LLM保底逻辑")
        
        print("\n测试完成！")


if __name__ == "__main__":
    # 可以通过命令行参数指定服务地址
    base_url = sys.argv[1] if len(sys.argv) > 1 else "http://localhost:8000"
    
    tester = ApiLlmFallbackTester(base_url)
    tester.run_all_tests()
