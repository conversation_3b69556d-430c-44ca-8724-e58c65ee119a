#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@File         :test_llm_fallback_integration.py
@Description  :LLM保底逻辑集成测试
<AUTHOR> HONG
@Date         :2025/09/08
"""

import unittest
import asyncio
import sys
import os
import json
from unittest.mock import patch, Mock

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from service_label_search.service.comprehensive_search_service import comprehensive_search_service
from service_label_search.configs.label_search_config import LabelSearchConfig


class TestLlmFallbackIntegration(unittest.TestCase):
    """LLM保底逻辑集成测试类"""
    
    def setUp(self):
        """测试前置设置"""
        self.service = comprehensive_search_service
        
    def test_end_to_end_llm_fallback_flow(self):
        """测试端到端LLM保底逻辑流程"""
        
        # 模拟一个不存在的产品，触发保底逻辑
        product_name = "超级新型未知产品XYZ123"
        
        # Mock语义搜索返回空结果
        with patch.object(self.service.semantic_service, 'get_detailed_search_results') as mock_semantic:
            mock_semantic.return_value = []
            
            # Mock LLM生成相关产品
            with patch.object(self.service.llm_service, 'generate_industry_chain_products') as mock_llm:
                mock_llm.return_value = ["相关产品A", "相关产品B", "相关产品C"]
                
                # Mock对生成产品的语义搜索
                def mock_semantic_search(product_name, **kwargs):
                    if product_name == "相关产品A":
                        return [{
                            "product_id": 1,
                            "product_name": "找到的产品1",
                            "product_source": "产品词",
                            "task_id": 1,
                            "version": 1,
                            "product_ids": "1,2",
                            "industry_last_node_code": "NODE001",
                            "similarity_score": 0.75,
                            "distance": 0.25
                        }]
                    elif product_name == "相关产品B":
                        return [{
                            "product_id": 2,
                            "product_name": "找到的产品2",
                            "product_source": "产品词",
                            "task_id": 1,
                            "version": 1,
                            "product_ids": "3,4",
                            "industry_last_node_code": "NODE002",
                            "similarity_score": 0.65,
                            "distance": 0.35
                        }]
                    else:
                        return []
                
                mock_semantic.side_effect = mock_semantic_search
                
                # 执行测试
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                results = loop.run_until_complete(
                    self.service.comprehensive_search(
                        product_name=product_name,
                        semantic_threshold=0.5,
                        overall_threshold=0.3,
                        alpha=0.7,
                        k_parameter=1.0,
                        limit=10
                    )
                )
                
                # 验证结果
                self.assertGreater(len(results), 0, "LLM保底逻辑应该返回结果")
                
                # 验证结果包含LLM生成的标识
                for result in results:
                    self.assertEqual(result["word_type"], "产业链")
                    self.assertEqual(result["industry_distance"], LabelSearchConfig.LLM_FALLBACK_INDUSTRY_DISTANCE)
                    self.assertIn("llm_generated_source", result)
                    self.assertIn("overall_similarity", result)
                
                # 验证LLM服务被调用
                mock_llm.assert_called_once_with(product_name)
    
    def test_llm_fallback_with_real_semantic_results(self):
        """测试有语义结果时不触发LLM保底逻辑"""
        
        product_name = "锂电池"
        
        # Mock语义搜索返回结果
        with patch.object(self.service.semantic_service, 'get_detailed_search_results') as mock_semantic:
            mock_semantic.return_value = [{
                "product_id": 1,
                "product_name": "锂离子电池",
                "product_source": "产品词",
                "task_id": 1,
                "version": 1,
                "product_ids": "1,2",
                "industry_last_node_code": "NODE001",
                "similarity_score": 0.85,
                "distance": 0.15
            }]
            
            # Mock产业链搜索
            with patch.object(self.service.industry_service, 'get_industry_chain_products_with_details') as mock_industry:
                mock_industry.return_value = []
                
                # Mock LLM服务（不应该被调用）
                with patch.object(self.service.llm_service, 'generate_industry_chain_products') as mock_llm:
                    
                    # 执行测试
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    results = loop.run_until_complete(
                        self.service.comprehensive_search(
                            product_name=product_name,
                            semantic_threshold=0.5,
                            overall_threshold=0.3,
                            alpha=0.7,
                            k_parameter=1.0,
                            limit=10
                        )
                    )
                    
                    # 验证结果
                    self.assertGreater(len(results), 0, "应该有语义搜索结果")
                    
                    # 验证LLM服务没有被调用
                    mock_llm.assert_not_called()
                    
                    # 验证结果类型为语义
                    semantic_results = [r for r in results if r["word_type"] == "语义"]
                    self.assertGreater(len(semantic_results), 0, "应该有语义类型的结果")
    
    def test_llm_fallback_error_handling(self):
        """测试LLM保底逻辑错误处理"""
        
        product_name = "测试产品"
        
        # Mock语义搜索返回空结果
        with patch.object(self.service.semantic_service, 'get_detailed_search_results') as mock_semantic:
            mock_semantic.return_value = []
            
            # Mock LLM服务抛出异常
            with patch.object(self.service.llm_service, 'generate_industry_chain_products') as mock_llm:
                mock_llm.side_effect = Exception("LLM服务不可用")
                
                # 执行测试
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                results = loop.run_until_complete(
                    self.service.comprehensive_search(
                        product_name=product_name,
                        semantic_threshold=0.5,
                        overall_threshold=0.3,
                        alpha=0.7,
                        k_parameter=1.0,
                        limit=10
                    )
                )
                
                # 验证结果
                self.assertEqual(len(results), 0, "LLM异常时应该返回空结果")
    
    def test_llm_fallback_configuration(self):
        """测试LLM保底逻辑配置"""
        
        # 验证配置项存在
        self.assertTrue(hasattr(LabelSearchConfig, 'ENABLE_LLM_FALLBACK'))
        self.assertTrue(hasattr(LabelSearchConfig, 'MAX_LLM_GENERATED_PRODUCTS'))
        self.assertTrue(hasattr(LabelSearchConfig, 'LLM_PRODUCT_SEARCH_LIMIT'))
        self.assertTrue(hasattr(LabelSearchConfig, 'LLM_FALLBACK_INDUSTRY_DISTANCE'))
        self.assertTrue(hasattr(LabelSearchConfig, 'LLM_FALLBACK_INDUSTRY_SIMILARITY'))
        
        # 验证配置值合理性
        self.assertIsInstance(LabelSearchConfig.ENABLE_LLM_FALLBACK, bool)
        self.assertGreater(LabelSearchConfig.MAX_LLM_GENERATED_PRODUCTS, 0)
        self.assertGreater(LabelSearchConfig.LLM_PRODUCT_SEARCH_LIMIT, 0)
        self.assertEqual(LabelSearchConfig.LLM_FALLBACK_INDUSTRY_DISTANCE, 1)
        self.assertGreater(LabelSearchConfig.LLM_FALLBACK_INDUSTRY_SIMILARITY, 0)
        self.assertLessEqual(LabelSearchConfig.LLM_FALLBACK_INDUSTRY_SIMILARITY, 1)
    
    def test_response_format_with_llm_fields(self):
        """测试响应格式包含LLM相关字段"""
        
        # 创建包含LLM字段的测试结果
        test_result = {
            "product_name": "测试产品",
            "word_type": "产业链",
            "overall_similarity": 0.7,
            "semantic_similarity": 0.6,
            "industry_similarity": 0.368,
            "llm_generated_source": "LLM生成的源产品",
            "industry_distance": 1
        }
        
        # 验证所有必要字段都存在
        required_fields = [
            "product_name", "word_type", "overall_similarity", 
            "semantic_similarity", "industry_similarity",
            "llm_generated_source", "industry_distance"
        ]
        
        for field in required_fields:
            self.assertIn(field, test_result, f"结果应该包含字段: {field}")
        
        # 验证LLM特有字段的值
        self.assertEqual(test_result["word_type"], "产业链")
        self.assertEqual(test_result["industry_distance"], 1)
        self.assertIsNotNone(test_result["llm_generated_source"])


if __name__ == '__main__':
    unittest.main()
