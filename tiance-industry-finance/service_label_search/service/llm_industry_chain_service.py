#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@File         :llm_industry_chain_service.py
@Description  :LLM产业链生成服务
<AUTHOR> HONG
@Date         :2025/09/08
"""

import json
import re
from typing import List
from utils.llm_model_util import Llm_Service, MessageConverter, UserMessage
from utils.log_util import LogUtil
from configs.model_config import ModelConfig


class LlmIndustryChainService:
    """LLM产业链生成服务类"""
    
    def __init__(self):
        """初始化服务"""
        self.llm_service = Llm_Service()
        self.model_name = ModelConfig.MAX_LLM_MODEL_NAME
    
    def _build_industry_chain_prompt(self, product_name: str) -> str:
        """
        构建产业链生成的prompt
        :param product_name: 产品名称
        :return: prompt字符串
        """
        prompt = f"""# 角色
你是一个专业的产业链分析专家，能够根据给定的产品名称生成相关的一级产业链产品。

## 任务
根据输入的产品名称"{product_name}"，分析并生成与该产品相关的一级产业链产品列表。

## 要求
1. 生成的产品应该是与输入产品直接相关的上游、中游、下游产品
2. 产品名称应该是标准的、常见的产业术语
3. 每个产品名称长度不超过20个字符
4. 生成3-8个相关产品
5. 产品之间应该有明确的产业链关系

## 输出格式
请严格按照以下JSON格式输出，不要包含任何其他内容：
{{
    "products": [
        "产品1",
        "产品2",
        "产品3"
    ]
}}

## 示例
输入：锂电池
输出：
{{
    "products": [
        "锂矿",
        "电解液",
        "正极材料",
        "负极材料",
        "电池隔膜",
        "电动汽车",
        "储能系统"
    ]
}}

现在请为"{product_name}"生成相关的一级产业链产品："""
        
        return prompt
    
    def _parse_llm_response(self, response: str) -> List[str]:
        """
        解析LLM返回的产品列表
        :param response: LLM返回的原始响应
        :return: 产品名称列表
        """
        try:
            # 清理响应文本
            cleaned_response = response.strip()
            
            # 尝试提取JSON部分
            json_match = re.search(r'\{.*\}', cleaned_response, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                try:
                    data = json.loads(json_str)
                    if "products" in data and isinstance(data["products"], list):
                        products = [str(p).strip() for p in data["products"] if p and str(p).strip()]
                        # 过滤掉过长的产品名称
                        products = [p for p in products if len(p) <= 20]
                        LogUtil.info(f"成功解析LLM响应，获得{len(products)}个产品")
                        return products
                except json.JSONDecodeError as e:
                    LogUtil.warn(f"JSON解析失败: {str(e)}")
            
            # 如果JSON解析失败，尝试其他解析方式
            # 查找列表格式的内容
            lines = cleaned_response.split('\n')
            products = []
            
            for line in lines:
                line = line.strip()
                # 匹配各种可能的列表格式
                if re.match(r'^[\d\.\-\*\+]+\s+', line):
                    # 移除列表标记
                    product = re.sub(r'^[\d\.\-\*\+]+\s+', '', line).strip()
                    if product and len(product) <= 20:
                        products.append(product)
                elif line and not line.startswith('#') and not line.startswith('输出') and len(line) <= 20:
                    # 简单的行内容
                    if '：' not in line and '说明' not in line and '示例' not in line:
                        products.append(line)
            
            if products:
                LogUtil.info(f"通过备用解析方式获得{len(products)}个产品")
                return products[:8]  # 限制最多8个产品
            
            LogUtil.warn("无法解析LLM响应，返回空列表")
            return []
            
        except Exception as e:
            LogUtil.error(f"解析LLM响应失败: {str(e)}")
            return []
    
    async def generate_industry_chain_products(self, product_name: str) -> List[str]:
        """
        生成产业链相关产品
        :param product_name: 输入的产品名称
        :return: 相关产品名称列表
        """
        try:
            if not product_name or not product_name.strip():
                LogUtil.warn("产品名称为空，无法生成产业链")
                return []
            
            LogUtil.info(f"开始为产品'{product_name}'生成产业链相关产品")
            
            # 构建prompt
            prompt = self._build_industry_chain_prompt(product_name.strip())
            
            # 调用LLM
            messages = MessageConverter.convert_messages([UserMessage(prompt)])
            response = self.llm_service.answer_question(
                messages=messages,
                model_name=self.model_name,
                max_tokens=1000
            )
            
            LogUtil.info(f"LLM响应: {response}")
            
            # 解析响应
            products = self._parse_llm_response(response)
            
            # 过滤掉与输入产品相同的产品
            filtered_products = [p for p in products if p.lower() != product_name.lower()]
            
            LogUtil.info(f"为产品'{product_name}'生成了{len(filtered_products)}个相关产品: {filtered_products}")
            return filtered_products
            
        except Exception as e:
            LogUtil.error(f"生成产业链产品失败: {str(e)}")
            return []
    
    def validate_generated_products(self, products: List[str]) -> List[str]:
        """
        验证生成的产品列表
        :param products: 产品列表
        :return: 验证后的产品列表
        """
        try:
            validated_products = []
            
            for product in products:
                if not product or not isinstance(product, str):
                    continue
                
                product = product.strip()
                
                # 基本验证
                if len(product) == 0 or len(product) > 20:
                    continue
                
                # 过滤明显不合适的内容
                invalid_patterns = [
                    r'^\d+\.',  # 数字开头
                    r'^[a-zA-Z0-9]+$',  # 纯英文数字
                    r'[<>{}[\]()]',  # 包含特殊符号
                    r'示例|说明|输出|格式',  # 包含说明性文字
                ]
                
                is_valid = True
                for pattern in invalid_patterns:
                    if re.search(pattern, product):
                        is_valid = False
                        break
                
                if is_valid:
                    validated_products.append(product)
            
            LogUtil.info(f"产品验证完成: {len(products)} -> {len(validated_products)}")
            return validated_products
            
        except Exception as e:
            LogUtil.error(f"产品验证失败: {str(e)}")
            return []


# 创建全局实例
llm_industry_chain_service = LlmIndustryChainService()
