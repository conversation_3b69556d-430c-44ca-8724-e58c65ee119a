#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@File         :comprehensive_search_service.py
@Description  :综合相似度计算服务
<AUTHOR> HONG
@Date         :2025/09/04
"""

from typing import List, Dict, Optional, Tuple, Set
from collections import defaultdict

from utils.log_util import LogUtil
from service_label_search.service.semantic_search_service import semantic_search_service
from service_label_search.service.industry_chain_search_service import industry_chain_search_service
from service_label_search.service.llm_industry_chain_service import llm_industry_chain_service
from service_label_search.configs.label_search_config import LabelSearchConfig


class ComprehensiveSearchService:
    """综合相似度计算服务类"""
    
    def __init__(self):
        """初始化服务"""
        self.semantic_service = semantic_search_service
        self.industry_service = industry_chain_search_service
        self.llm_service = llm_industry_chain_service
    
    async def get_semantic_similarity_results(self, product_name: str,
                                            semantic_threshold: float,
                                            task_id: Optional[int] = None) -> Dict[str, Dict]:
        """
        获取语义相似度结果
        :param product_name: 非标准产品词
        :param semantic_threshold: 语义相似度阈值
        :param task_id: 任务ID
        :return: {product_name: {similarity, type, ...}}
        """
        try:
            # 获取详细的语义搜索结果
            semantic_results = await self.semantic_service.get_detailed_search_results(
                product_name=product_name,
                similarity_threshold=semantic_threshold,
                limit=LabelSearchConfig.DEFAULT_VECTOR_SEARCH_LIMIT,
                task_id=task_id
            )
            
            # 转换为字典格式
            semantic_dict = {}
            for result in semantic_results:
                product_name_key = result["product_name"]
                semantic_dict[product_name_key] = {
                    "product_id": result["product_id"],
                    "product_name": result["product_name"],
                    "product_source": result["product_source"],
                    "task_id": result["task_id"],
                    "version": result["version"],
                    "product_ids": result["product_ids"],
                    "industry_last_node_code": result["industry_last_node_code"],
                    "semantic_similarity": result["similarity_score"],
                    "industry_similarity":1.0 ,
                    "word_type": "语义",
                    "distance": result.get("distance", 0.0)
                }
            
            LogUtil.info(f"获取语义相似度结果: {len(semantic_dict)} 个")
            return semantic_dict
            
        except Exception as e:
            LogUtil.error(f"获取语义相似度结果失败: {str(e)}")
            return {}
    
    async def get_industry_similarity_results(self, semantic_results: Dict[str, Dict],
                                            k_parameter: float,
                                            product_name: str) -> Dict[str, Dict]:
        """
        获取产业链相似度结果
        :param semantic_results: 语义相似度结果
        :param k_parameter: 距离衰减参数
        :param product_name: 原始查询产品名称
        :return: {product_name: {similarity, type, ...}}
        """
        try:
            industry_dict = {}

            # 只处理第一个语义相似的产品，避免无限循环
            if not semantic_results:
                return industry_dict

            # 选择相似度最高的语义产品作为产业链搜索的起点
            best_semantic_product = max(semantic_results.items(),
                                      key=lambda x: x[1]["semantic_similarity"])
            semantic_product_name, semantic_info = best_semantic_product

            try:
                # 获取该产品的产业链相似度
                industry_results = self.industry_service.get_industry_chain_products_with_details(
                    standard_product_name=semantic_product_name,
                    k_parameter=k_parameter,
                    task_id=semantic_info["task_id"]
                )

                # 处理产业链结果，限制数量避免过多结果
                max_industry_results = 20  # 进一步限制产业链结果数量
                for i, industry_result in enumerate(industry_results[:max_industry_results]):
                    industry_product_name = industry_result["product_name"]

                    # 如果该产品已经在语义结果中，跳过（避免重复）
                    if industry_product_name in semantic_results:
                        continue

                    # 计算语义相似度（使用优化的方法，避免循环调用）
                    semantic_sim_for_industry = await self._calculate_semantic_similarity_for_industry_product(
                        original_product=product_name,
                        industry_product=industry_product_name,
                        task_id=semantic_info["task_id"]
                    )

                    industry_dict[industry_product_name] = {
                        "product_id": industry_result["product_id"],
                        "product_name": industry_result["product_name"],
                        "product_source": industry_result["product_source"],
                        "task_id": industry_result["task_id"],
                        "version": industry_result["version"],
                        "product_ids": industry_result["product_ids"],
                        "industry_last_node_code": industry_result["industry_last_node_code"],
                        "industry_similarity": industry_result["industry_similarity"],
                        "industry_distance": industry_result["industry_distance"],
                        "semantic_similarity": semantic_sim_for_industry,
                        "word_type": "产业链",
                        "source_semantic_product": semantic_product_name
                    }

            except Exception as e:
                LogUtil.warn(f"处理产品 {semantic_product_name} 的产业链相似度失败: {str(e)}")

            LogUtil.info(f"获取产业链相似度结果: {len(industry_dict)} 个")
            return industry_dict

        except Exception as e:
            LogUtil.error(f"获取产业链相似度结果失败: {str(e)}")
            return {}

    async def get_llm_fallback_results(self, product_name: str,
                                     semantic_threshold: float,
                                     task_id: Optional[int] = None) -> Dict[str, Dict]:
        """
        获取LLM保底逻辑的搜索结果
        :param product_name: 原始查询产品名称
        :param semantic_threshold: 语义相似度阈值
        :param task_id: 任务ID
        :return: {product_name: {similarity, type, ...}}
        """
        try:
            LogUtil.info(f"启动LLM保底逻辑，为产品'{product_name}'生成产业链")

            # 使用LLM生成相关产业链产品
            generated_products = await self.llm_service.generate_industry_chain_products(product_name)

            if not generated_products:
                LogUtil.warn(f"LLM未能为产品'{product_name}'生成相关产业链产品")
                return {}

            LogUtil.info(f"LLM生成了{len(generated_products)}个相关产品: {generated_products}")

            # 对生成的每个产品进行语义搜索
            llm_results = {}

            for generated_product in generated_products:
                try:
                    # 对生成的产品进行语义搜索
                    semantic_results = await self.semantic_service.get_detailed_search_results(
                        product_name=generated_product,
                        similarity_threshold=semantic_threshold,
                        limit=5,  # 每个生成产品限制返回5个结果
                        task_id=task_id
                    )

                    # 处理搜索结果
                    for result in semantic_results:
                        product_name_key = result["product_name"]

                        # 避免重复添加
                        if product_name_key not in llm_results:
                            llm_results[product_name_key] = {
                                "product_id": result["product_id"],
                                "product_name": result["product_name"],
                                "product_source": result["product_source"],
                                "task_id": result["task_id"],
                                "version": result["version"],
                                "product_ids": result["product_ids"],
                                "industry_last_node_code": result["industry_last_node_code"],
                                "semantic_similarity": result["similarity_score"],
                                "industry_similarity": LabelSearchConfig.LLM_FALLBACK_INDUSTRY_SIMILARITY,  # 距离为1的产业链相似度
                                "word_type": "产业链",
                                "distance": result.get("distance", 0.0),
                                "llm_generated_source": generated_product,  # 记录是通过哪个LLM生成的产品找到的
                                "industry_distance": LabelSearchConfig.LLM_FALLBACK_INDUSTRY_DISTANCE  # LLM生成的产品距离
                            }

                except Exception as e:
                    LogUtil.warn(f"对LLM生成的产品'{generated_product}'进行语义搜索失败: {str(e)}")
                    continue

            LogUtil.info(f"LLM保底逻辑完成，获得{len(llm_results)}个结果")
            return llm_results

        except Exception as e:
            LogUtil.error(f"LLM保底逻辑失败: {str(e)}")
            return {}
    
    async def _calculate_semantic_similarity_for_industry_product(self, original_product: str,
                                                                industry_product: str,
                                                                task_id: int) -> float:
        """
        计算产业链产品与原始查询的语义相似度（使用COSINE方法，与SemanticSearchService保持一致）
        :param original_product: 原始查询产品
        :param industry_product: 产业链产品
        :param task_id: 任务ID
        :return: 语义相似度分数
        """
        try:
            # 1. 完全匹配检查
            if original_product == industry_product:
                return 1.0

            # 2. 使用SemanticSearchService的向量搜索方法计算相似度
            # 生成原始产品的向量
            original_vector = await self.semantic_service.generate_query_vector(original_product)

            # 生成产业链产品的向量
            industry_vector = await self.semantic_service.generate_query_vector(industry_product)

            # 计算余弦相似度
            similarity = self._calculate_cosine_similarity(original_vector, industry_vector)
            return max(0.0, min(1.0, similarity))  # 确保在[0,1]范围内

        except Exception as e:
            LogUtil.warn(f"计算产业链产品语义相似度失败: {str(e)}")
            # 降级到字符串相似度
            try:
                if original_product in industry_product or industry_product in original_product:
                    return 0.8
                return self._calculate_string_similarity(original_product, industry_product)
            except:
                return 0.1


    def _calculate_cosine_similarity(self, vector1: List[float], vector2: List[float]) -> float:
        """
        计算两个向量的余弦相似度（与SemanticSearchService保持一致）
        :param vector1: 向量1
        :param vector2: 向量2
        :return: 余弦相似度 [0, 1]
        """
        try:
            import numpy as np

            # 转换为numpy数组
            v1 = np.array(vector1)
            v2 = np.array(vector2)

            # 计算余弦相似度
            dot_product = np.dot(v1, v2)
            norm1 = np.linalg.norm(v1)
            norm2 = np.linalg.norm(v2)

            if norm1 == 0 or norm2 == 0:
                return 0.0

            cosine_sim = dot_product / (norm1 * norm2)

            # 对于COSINE距离，需要转换为相似度
            # 余弦相似度范围[-1, 1]，我们需要转换为[0, 1]
            # 但是根据SemanticSearchService的逻辑，我们应该直接返回余弦相似度
            # 因为Milvus返回的distance已经是处理过的值
            return max(0.0, cosine_sim)  # 确保非负

        except Exception as e:
            LogUtil.warn(f"余弦相似度计算失败: {str(e)}")
            return 0.0

    def _calculate_string_similarity(self, str1: str, str2: str) -> float:
        """
        基于编辑距离计算字符串相似度
        :param str1: 字符串1
        :param str2: 字符串2
        :return: 相似度 [0, 1]
        """
        try:
            # 简单的编辑距离算法
            def levenshtein_distance(s1, s2):
                if len(s1) < len(s2):
                    return levenshtein_distance(s2, s1)

                if len(s2) == 0:
                    return len(s1)

                previous_row = list(range(len(s2) + 1))
                for i, c1 in enumerate(s1):
                    current_row = [i + 1]
                    for j, c2 in enumerate(s2):
                        insertions = previous_row[j + 1] + 1
                        deletions = current_row[j] + 1
                        substitutions = previous_row[j] + (c1 != c2)
                        current_row.append(min(insertions, deletions, substitutions))
                    previous_row = current_row

                return previous_row[-1]

            max_len = max(len(str1), len(str2))
            if max_len == 0:
                return 1.0

            distance = levenshtein_distance(str1, str2)
            similarity = 1 - (distance / max_len)
            return max(0.0, similarity)

        except Exception as e:
            LogUtil.warn(f"字符串相似度计算失败: {str(e)}")
            return 0.0

    def calculate_comprehensive_similarity(self, results: Dict[str, Dict],
                                         alpha: float) -> Dict[str, Dict]:
        """
        计算综合相似度
        :param results: 所有结果字典
        :param alpha: 语义相似度权重
        :return: 包含综合相似度的结果字典
        """
        try:
            for product_name, info in results.items():
                semantic_sim = info.get("semantic_similarity", 0.0)
                industry_sim = info.get("industry_similarity", 0.0)
                
                # 计算综合相似度: α * 语义相似度 + (1 - α) * 产业链相似度
                comprehensive_sim = alpha * semantic_sim + (1 - alpha) * industry_sim
                
                info["overall_similarity"] = comprehensive_sim
            
            LogUtil.info(f"计算综合相似度完成，权重α: {alpha}")
            return results
            
        except Exception as e:
            LogUtil.error(f"计算综合相似度失败: {str(e)}")
            return results
    
    def filter_and_sort_results(self, results: Dict[str, Dict],
                               overall_threshold: float,
                               limit: int) -> List[Dict]:
        """
        过滤和排序结果
        :param results: 结果字典
        :param overall_threshold: 总体相似度阈值
        :param limit: 结果数量限制
        :return: 排序后的结果列表
        """
        try:
            # 过滤满足阈值的结果
            filtered_results = [
                info for info in results.values()
                if info.get("overall_similarity", 0.0) >= overall_threshold
            ]
            
            # 按综合相似度降序排序
            filtered_results.sort(key=lambda x: x.get("overall_similarity", 0.0), reverse=True)
            
            # 限制结果数量
            final_results = filtered_results[:limit]
            
            LogUtil.info(f"过滤和排序结果: {len(results)} -> {len(filtered_results)} -> {len(final_results)}")
            return final_results
            
        except Exception as e:
            LogUtil.error(f"过滤和排序结果失败: {str(e)}")
            return []
    
    async def comprehensive_search(self, product_name: str,
                                 semantic_threshold: float = 0.5,
                                 overall_threshold: float = 0.3,
                                 alpha: float = 0.7,
                                 k_parameter: float = 1.0,
                                 limit: int = 20,
                                 task_id: Optional[int] = None) -> List[Dict]:
        """
        综合相似度搜索
        :param product_name: 非标准产品词
        :param semantic_threshold: 语义相似度阈值
        :param overall_threshold: 总体相似度阈值
        :param alpha: 语义相似度权重
        :param k_parameter: 产业链距离衰减参数
        :param limit: 返回结果数量限制
        :param task_id: 任务ID
        :return: 综合搜索结果列表
        """
        try:
            # 参数验证
            if not product_name or not product_name.strip():
                raise ValueError(LabelSearchConfig.ERROR_MESSAGES["INVALID_PRODUCT_NAME"])
            
            if not LabelSearchConfig.validate_similarity_threshold(semantic_threshold):
                raise ValueError(LabelSearchConfig.ERROR_MESSAGES["INVALID_SIMILARITY_THRESHOLD"])
            
            if not LabelSearchConfig.validate_similarity_threshold(overall_threshold):
                raise ValueError(LabelSearchConfig.ERROR_MESSAGES["INVALID_SIMILARITY_THRESHOLD"])
            
            if not LabelSearchConfig.validate_alpha(alpha):
                raise ValueError(LabelSearchConfig.ERROR_MESSAGES["INVALID_ALPHA"])
            
            if not LabelSearchConfig.validate_k_parameter(k_parameter):
                raise ValueError(LabelSearchConfig.ERROR_MESSAGES["INVALID_K_PARAMETER"])
            
            if not LabelSearchConfig.validate_search_limit(limit):
                raise ValueError(LabelSearchConfig.ERROR_MESSAGES["INVALID_LIMIT"])
            
            LogUtil.info(f"开始综合相似度搜索: {product_name}")
            
            # 1. 获取语义相似度结果
            semantic_results = await self.get_semantic_similarity_results(
                product_name=product_name.strip(),
                semantic_threshold=semantic_threshold,
                task_id=task_id
            )


            if not semantic_results:
                LogUtil.warn(f"未找到语义相似的产品: {product_name}，启动LLM保底逻辑")

                # 启动LLM保底逻辑
                llm_fallback_results = await self.get_llm_fallback_results(
                    product_name=product_name.strip(),
                    semantic_threshold=semantic_threshold,
                    task_id=task_id
                )

                if not llm_fallback_results:
                    LogUtil.warn(f"LLM保底逻辑也未找到相关产品: {product_name}")
                    return []

                # 使用LLM保底结果作为最终结果
                all_results = llm_fallback_results

                # 计算综合相似度
                all_results = self.calculate_comprehensive_similarity(all_results, alpha)

                # 过滤和排序结果
                final_results = self.filter_and_sort_results(
                    results=all_results,
                    overall_threshold=overall_threshold,
                    limit=limit
                )

                LogUtil.info(f"LLM保底逻辑搜索完成: {product_name} -> {len(final_results)} 个结果")
                return final_results

            # 2. 获取产业链相似度结果（使用优化的方法）
            industry_results = await self.get_industry_similarity_results(
                semantic_results=semantic_results,
                k_parameter=k_parameter,
                product_name=product_name.strip()
            )
            LogUtil.info(f"产业链搜索已启用，使用优化算法避免无限循环")
            
            # 3. 合并所有结果
            all_results = {}
            all_results.update(semantic_results)
            all_results.update(industry_results)

            # 4. 计算综合相似度
            all_results = self.calculate_comprehensive_similarity(all_results, alpha)

            # 5. 过滤和排序结果
            final_results = self.filter_and_sort_results(
                results=all_results,
                overall_threshold=overall_threshold,
                limit=limit
            )
            
            LogUtil.info(f"综合相似度搜索完成: {product_name} -> {len(final_results)} 个结果")
            return final_results
            
        except Exception as e:
            LogUtil.error(f"综合相似度搜索失败: {str(e)}")
            raise Exception(f"综合相似度搜索失败: {str(e)}")


# 创建全局实例
comprehensive_search_service = ComprehensiveSearchService()
