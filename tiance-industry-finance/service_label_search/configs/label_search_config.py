#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@File         :label_search_config.py
@Description  :标签搜索服务配置
<AUTHOR> HONG
@Date         :2025/09/04
"""

import os
from typing import Dict, Any


class LabelSearchConfig:
    """标签搜索服务配置类"""
    
    # ==================== 向量数据库配置 ====================
    
    # 产品词向量集合名称
    PRODUCT_VECTOR_COLLECTION = "product_words_vector"
    
    # 向量维度
    VECTOR_DIMENSION = 1024
    
    # 向量搜索默认参数
    DEFAULT_VECTOR_SEARCH_LIMIT = 50
    VECTOR_SEARCH_PARAMS = {
        "metric_type": "COSINE",
        "params": {"ef": 100}
    }
    
    # ==================== 相似度计算配置 ====================
    
    # 默认语义相似度阈值
    DEFAULT_SEMANTIC_THRESHOLD = 0.5
    
    # 默认总体相似度阈值
    DEFAULT_OVERALL_THRESHOLD = 0.3
    
    # 默认语义相似度权重（α参数）
    DEFAULT_ALPHA = 0.7
    
    # 默认产业链距离衰减参数（k参数）
    DEFAULT_K_PARAMETER = 1.0
    
    # 相似度阈值范围
    MIN_SIMILARITY_THRESHOLD = 0.0
    MAX_SIMILARITY_THRESHOLD = 1.0
    
    # 权重参数范围
    MIN_ALPHA = 0.0
    MAX_ALPHA = 1.0
    
    # 距离衰减参数范围
    MIN_K_PARAMETER = 0.1
    MAX_K_PARAMETER = 10.0
    
    # ==================== 搜索结果配置 ====================
    
    # 默认返回结果数量
    DEFAULT_SEARCH_LIMIT = 20
    
    # 搜索结果数量范围
    MIN_SEARCH_LIMIT = 1
    MAX_SEARCH_LIMIT = 100
    
    # ==================== 产业链距离计算配置 ====================
    
    # 最大产业链距离（超过此距离的节点不参与计算）
    MAX_INDUSTRY_CHAIN_DISTANCE = 10
    
    # 产业链相似度计算公式：f(x) = e^(-k*x)
    # 其中 x 为距离，k 为衰减参数
    INDUSTRY_SIMILARITY_FORMULA = "exp(-k*x)"
    
    # ==================== 缓存配置 ====================
    
    # 是否启用缓存
    ENABLE_CACHE = True
    
    # 缓存过期时间（秒）
    CACHE_EXPIRE_TIME = 3600  # 1小时
    
    # 缓存键前缀
    CACHE_KEY_PREFIX = "label_search:"
    
    # ==================== 异步更新配置 ====================
    
    # 向量数据库更新批次大小
    VECTOR_UPDATE_BATCH_SIZE = 100
    
    # 增量更新检查间隔（秒）
    INCREMENTAL_UPDATE_INTERVAL = 300  # 5分钟
    
    # 是否启用自动增量更新
    ENABLE_AUTO_INCREMENTAL_UPDATE = True

    # ==================== LLM保底逻辑配置 ====================

    # 是否启用LLM保底逻辑
    ENABLE_LLM_FALLBACK = True

    # LLM生成产品的最大数量
    MAX_LLM_GENERATED_PRODUCTS = 8

    # 每个LLM生成产品的搜索结果限制
    LLM_PRODUCT_SEARCH_LIMIT = 5

    # LLM保底逻辑的产业链距离
    LLM_FALLBACK_INDUSTRY_DISTANCE = 1

    # LLM保底逻辑的产业链相似度（距离为1时的相似度）
    LLM_FALLBACK_INDUSTRY_SIMILARITY = 0.368  # e^(-1*1) ≈ 0.368
    
    # ==================== 日志配置 ====================
    
    # 日志级别
    LOG_LEVEL = "INFO"
    
    # 日志文件名
    LOG_FILE_NAME = "label_search.log"
    
    # ==================== API配置 ====================
    
    # API路由前缀
    API_PREFIX = "/label_search"
    
    # API版本
    API_VERSION = "v1"
    
    # 完整API路径前缀
    FULL_API_PREFIX = f"{API_PREFIX}/{API_VERSION}"
    
    # ==================== 数据库表配置 ====================
    
    # Products_Unique表名
    PRODUCTS_UNIQUE_TABLE = "Products_Unique"
    
    # Industry_Chain_Structure表名
    INDUSTRY_CHAIN_STRUCTURE_TABLE = "Industry_Chain_Structure"
    
    # ==================== 错误消息配置 ====================
    
    ERROR_MESSAGES = {
        "INVALID_PRODUCT_NAME": "产品名称不能为空",
        "INVALID_SIMILARITY_THRESHOLD": f"相似度阈值必须在{MIN_SIMILARITY_THRESHOLD}到{MAX_SIMILARITY_THRESHOLD}之间",
        "INVALID_ALPHA": f"权重参数α必须在{MIN_ALPHA}到{MAX_ALPHA}之间",
        "INVALID_K_PARAMETER": f"距离衰减参数k必须在{MIN_K_PARAMETER}到{MAX_K_PARAMETER}之间",
        "INVALID_LIMIT": f"返回结果数量必须在{MIN_SEARCH_LIMIT}到{MAX_SEARCH_LIMIT}之间",
        "PRODUCT_NOT_FOUND": "未找到匹配的产品词",
        "VECTOR_COLLECTION_NOT_FOUND": "向量集合不存在",
        "DATABASE_CONNECTION_ERROR": "数据库连接错误",
        "VECTOR_UPDATE_ERROR": "向量数据库更新失败",
        "INDUSTRY_CHAIN_NOT_FOUND": "未找到产业链信息"
    }
    
    # ==================== 成功消息配置 ====================
    
    SUCCESS_MESSAGES = {
        "VECTOR_UPDATE_SUCCESS": "向量数据库更新成功",
        "SEARCH_SUCCESS": "搜索完成",
        "COLLECTION_CREATED": "向量集合创建成功"
    }
    
    # ==================== 环境变量配置 ====================
    
    @classmethod
    def get_config_from_env(cls) -> Dict[str, Any]:
        """
        从环境变量获取配置
        :return: 配置字典
        """
        return {
            "semantic_threshold": float(os.getenv("LABEL_SEARCH_SEMANTIC_THRESHOLD", cls.DEFAULT_SEMANTIC_THRESHOLD)),
            "overall_threshold": float(os.getenv("LABEL_SEARCH_OVERALL_THRESHOLD", cls.DEFAULT_OVERALL_THRESHOLD)),
            "alpha": float(os.getenv("LABEL_SEARCH_ALPHA", cls.DEFAULT_ALPHA)),
            "k_parameter": float(os.getenv("LABEL_SEARCH_K_PARAMETER", cls.DEFAULT_K_PARAMETER)),
            "search_limit": int(os.getenv("LABEL_SEARCH_LIMIT", cls.DEFAULT_SEARCH_LIMIT)),
            "enable_cache": os.getenv("LABEL_SEARCH_ENABLE_CACHE", "true").lower() == "true",
            "cache_expire_time": int(os.getenv("LABEL_SEARCH_CACHE_EXPIRE", cls.CACHE_EXPIRE_TIME)),
            "vector_update_batch_size": int(os.getenv("LABEL_SEARCH_BATCH_SIZE", cls.VECTOR_UPDATE_BATCH_SIZE)),
            "enable_auto_update": os.getenv("LABEL_SEARCH_AUTO_UPDATE", "true").lower() == "true"
        }
    
    # ==================== 验证方法 ====================
    
    @classmethod
    def validate_similarity_threshold(cls, threshold: float) -> bool:
        """验证相似度阈值"""
        return cls.MIN_SIMILARITY_THRESHOLD <= threshold <= cls.MAX_SIMILARITY_THRESHOLD
    
    @classmethod
    def validate_alpha(cls, alpha: float) -> bool:
        """验证权重参数α"""
        return cls.MIN_ALPHA <= alpha <= cls.MAX_ALPHA
    
    @classmethod
    def validate_k_parameter(cls, k: float) -> bool:
        """验证距离衰减参数k"""
        return cls.MIN_K_PARAMETER <= k <= cls.MAX_K_PARAMETER
    
    @classmethod
    def validate_search_limit(cls, limit: int) -> bool:
        """验证搜索结果数量限制"""
        return cls.MIN_SEARCH_LIMIT <= limit <= cls.MAX_SEARCH_LIMIT
