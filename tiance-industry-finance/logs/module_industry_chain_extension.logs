[2025-09-08 11:21:25,967] [module_industry_chain_extension 14025] [label_search_router.comprehensive_search: 108] INFO: 综合相似度搜索请求: {'product_name': '超级新型未知产品XYZ123', 'semantic_threshold': 0.5, 'overall_threshold': 0.3, 'limit': 10, 'alpha': 0.7, 'k_parameter': 1.0}
[2025-09-08 11:21:25,967] [module_industry_chain_extension 14025] [comprehensive_search_service.comprehensive_search: 416] INFO: 开始综合相似度搜索: 超级新型未知产品XYZ123
[2025-09-08 11:21:25,975] [module_industry_chain_extension 14025] [vector_update_service.ensure_collection_exists: 41] INFO: 向量集合已存在: product_words_vector
[2025-09-08 11:21:26,812] [module_industry_chain_extension 14025] [vector_update_service.get_products_from_mysql: 107] INFO: 从MySQL获取产品数据成功，数量: 11717
[2025-09-08 11:21:26,964] [module_industry_chain_extension 14025] [vector_update_service.get_existing_product_ids: 135] INFO: 向量数据库中已存在产品数量: 11717
[2025-09-08 11:21:26,965] [module_industry_chain_extension 14025] [vector_update_service.update_vectors_incremental: 243] INFO: 所有产品数据已是最新，无需更新
[2025-09-08 11:21:26,966] [module_industry_chain_extension 14025] [semantic_search_service.ensure_vector_database_updated: 44] INFO: 向量数据库更新完成: {'update_type': 'incremental', 'updated_count': 0, 'total_count': 11717, 'duration_seconds': 0.99895, 'update_time': '2025-09-08T11:21:26.966677', 'task_id': None}
[2025-09-08 11:21:27,600] [module_industry_chain_extension 14025] [semantic_search_service.search_similar_products: 136] INFO: 向量搜索完成，返回结果数量: 100
[2025-09-08 11:21:27,600] [module_industry_chain_extension 14025] [semantic_search_service.filter_by_similarity_threshold: 156] INFO: 相似度阈值过滤: 100 -> 0 (阈值: 0.5)
[2025-09-08 11:21:27,600] [module_industry_chain_extension 14025] [semantic_search_service.get_detailed_search_results: 300] INFO: 详细搜索完成: 超级新型未知产品XYZ123 -> 0 个结果
[2025-09-08 11:21:27,600] [module_industry_chain_extension 14025] [comprehensive_search_service.get_semantic_similarity_results: 66] INFO: 获取语义相似度结果: 0 个
[2025-09-08 11:21:27,600] [module_industry_chain_extension 14025] [comprehensive_search_service.comprehensive_search: 427] WARNING: 未找到语义相似的产品: 超级新型未知产品XYZ123，启动LLM保底逻辑
[2025-09-08 11:21:27,600] [module_industry_chain_extension 14025] [comprehensive_search_service.get_llm_fallback_results: 155] INFO: 启动LLM保底逻辑，为产品'超级新型未知产品XYZ123'生成产业链
[2025-09-08 11:21:27,600] [module_industry_chain_extension 14025] [llm_industry_chain_service.generate_industry_chain_products: 139] INFO: 开始为产品'超级新型未知产品XYZ123'生成产业链相关产品
[2025-09-08 11:21:28,424] [module_industry_chain_extension 14025] [llm_industry_chain_service.generate_industry_chain_products: 152] INFO: LLM响应: {\n    "products": [\n        "基础材料",\n        "加工设备",\n        "核心技术部件",\n        "组装生产线",\n        "测试仪器",\n        "用户解决方案"\n    ]\n}
[2025-09-08 11:21:28,424] [module_industry_chain_extension 14025] [llm_industry_chain_service._parse_llm_response: 94] INFO: 成功解析LLM响应，获得6个产品
[2025-09-08 11:21:28,425] [module_industry_chain_extension 14025] [llm_industry_chain_service.generate_industry_chain_products: 160] INFO: 为产品'超级新型未知产品XYZ123'生成了6个相关产品: ['基础材料', '加工设备', '核心技术部件', '组装生产线', '测试仪器', '用户解决方案']
[2025-09-08 11:21:28,425] [module_industry_chain_extension 14025] [comprehensive_search_service.get_llm_fallback_results: 164] INFO: LLM生成了6个相关产品: ['基础材料', '加工设备', '核心技术部件', '组装生产线', '测试仪器', '用户解决方案']
[2025-09-08 11:21:28,434] [module_industry_chain_extension 14025] [vector_update_service.ensure_collection_exists: 41] INFO: 向量集合已存在: product_words_vector
[2025-09-08 11:21:28,878] [module_industry_chain_extension 14025] [vector_update_service.get_products_from_mysql: 107] INFO: 从MySQL获取产品数据成功，数量: 11717
[2025-09-08 11:21:29,053] [module_industry_chain_extension 14025] [vector_update_service.get_existing_product_ids: 135] INFO: 向量数据库中已存在产品数量: 11717
[2025-09-08 11:21:29,054] [module_industry_chain_extension 14025] [vector_update_service.update_vectors_incremental: 243] INFO: 所有产品数据已是最新，无需更新
[2025-09-08 11:21:29,055] [module_industry_chain_extension 14025] [semantic_search_service.ensure_vector_database_updated: 44] INFO: 向量数据库更新完成: {'update_type': 'incremental', 'updated_count': 0, 'total_count': 11717, 'duration_seconds': 0.630461, 'update_time': '2025-09-08T11:21:29.055612', 'task_id': None}
[2025-09-08 11:21:29,139] [module_industry_chain_extension 14025] [semantic_search_service.search_similar_products: 136] INFO: 向量搜索完成，返回结果数量: 100
[2025-09-08 11:21:29,139] [module_industry_chain_extension 14025] [semantic_search_service.filter_by_similarity_threshold: 156] INFO: 相似度阈值过滤: 100 -> 100 (阈值: 0.5)
[2025-09-08 11:21:29,139] [module_industry_chain_extension 14025] [semantic_search_service.get_detailed_search_results: 300] INFO: 详细搜索完成: 基础材料 -> 5 个结果
[2025-09-08 11:21:29,147] [module_industry_chain_extension 14025] [vector_update_service.ensure_collection_exists: 41] INFO: 向量集合已存在: product_words_vector
[2025-09-08 11:21:29,397] [module_industry_chain_extension 14025] [vector_update_service.get_products_from_mysql: 107] INFO: 从MySQL获取产品数据成功，数量: 11717
[2025-09-08 11:21:29,521] [module_industry_chain_extension 14025] [vector_update_service.get_existing_product_ids: 135] INFO: 向量数据库中已存在产品数量: 11717
[2025-09-08 11:21:29,522] [module_industry_chain_extension 14025] [vector_update_service.update_vectors_incremental: 243] INFO: 所有产品数据已是最新，无需更新
[2025-09-08 11:21:29,523] [module_industry_chain_extension 14025] [semantic_search_service.ensure_vector_database_updated: 44] INFO: 向量数据库更新完成: {'update_type': 'incremental', 'updated_count': 0, 'total_count': 11717, 'duration_seconds': 0.384248, 'update_time': '2025-09-08T11:21:29.523685', 'task_id': None}
[2025-09-08 11:21:29,842] [module_industry_chain_extension 14025] [semantic_search_service.search_similar_products: 136] INFO: 向量搜索完成，返回结果数量: 100
[2025-09-08 11:21:29,842] [module_industry_chain_extension 14025] [semantic_search_service.filter_by_similarity_threshold: 156] INFO: 相似度阈值过滤: 100 -> 100 (阈值: 0.5)
[2025-09-08 11:21:29,842] [module_industry_chain_extension 14025] [semantic_search_service.get_detailed_search_results: 300] INFO: 详细搜索完成: 加工设备 -> 5 个结果
[2025-09-08 11:21:29,851] [module_industry_chain_extension 14025] [vector_update_service.ensure_collection_exists: 41] INFO: 向量集合已存在: product_words_vector
[2025-09-08 11:21:30,078] [module_industry_chain_extension 14025] [vector_update_service.get_products_from_mysql: 107] INFO: 从MySQL获取产品数据成功，数量: 11717
[2025-09-08 11:21:30,207] [module_industry_chain_extension 14025] [vector_update_service.get_existing_product_ids: 135] INFO: 向量数据库中已存在产品数量: 11717
[2025-09-08 11:21:30,208] [module_industry_chain_extension 14025] [vector_update_service.update_vectors_incremental: 243] INFO: 所有产品数据已是最新，无需更新
[2025-09-08 11:21:30,209] [module_industry_chain_extension 14025] [semantic_search_service.ensure_vector_database_updated: 44] INFO: 向量数据库更新完成: {'update_type': 'incremental', 'updated_count': 0, 'total_count': 11717, 'duration_seconds': 0.366331, 'update_time': '2025-09-08T11:21:30.209169', 'task_id': None}
[2025-09-08 11:21:30,298] [module_industry_chain_extension 14025] [semantic_search_service.search_similar_products: 136] INFO: 向量搜索完成，返回结果数量: 100
[2025-09-08 11:21:30,298] [module_industry_chain_extension 14025] [semantic_search_service.filter_by_similarity_threshold: 156] INFO: 相似度阈值过滤: 100 -> 100 (阈值: 0.5)
[2025-09-08 11:21:30,298] [module_industry_chain_extension 14025] [semantic_search_service.get_detailed_search_results: 300] INFO: 详细搜索完成: 核心技术部件 -> 5 个结果
[2025-09-08 11:21:30,312] [module_industry_chain_extension 14025] [vector_update_service.ensure_collection_exists: 41] INFO: 向量集合已存在: product_words_vector
[2025-09-08 11:21:30,517] [module_industry_chain_extension 14025] [vector_update_service.get_products_from_mysql: 107] INFO: 从MySQL获取产品数据成功，数量: 11717
[2025-09-08 11:21:30,657] [module_industry_chain_extension 14025] [vector_update_service.get_existing_product_ids: 135] INFO: 向量数据库中已存在产品数量: 11717
[2025-09-08 11:21:30,658] [module_industry_chain_extension 14025] [vector_update_service.update_vectors_incremental: 243] INFO: 所有产品数据已是最新，无需更新
[2025-09-08 11:21:30,659] [module_industry_chain_extension 14025] [semantic_search_service.ensure_vector_database_updated: 44] INFO: 向量数据库更新完成: {'update_type': 'incremental', 'updated_count': 0, 'total_count': 11717, 'duration_seconds': 0.360728, 'update_time': '2025-09-08T11:21:30.659429', 'task_id': None}
[2025-09-08 11:21:30,758] [module_industry_chain_extension 14025] [semantic_search_service.search_similar_products: 136] INFO: 向量搜索完成，返回结果数量: 100
[2025-09-08 11:21:30,758] [module_industry_chain_extension 14025] [semantic_search_service.filter_by_similarity_threshold: 156] INFO: 相似度阈值过滤: 100 -> 100 (阈值: 0.5)
[2025-09-08 11:21:30,758] [module_industry_chain_extension 14025] [semantic_search_service.get_detailed_search_results: 300] INFO: 详细搜索完成: 组装生产线 -> 5 个结果
[2025-09-08 11:21:30,771] [module_industry_chain_extension 14025] [vector_update_service.ensure_collection_exists: 41] INFO: 向量集合已存在: product_words_vector
[2025-09-08 11:21:31,052] [module_industry_chain_extension 14025] [vector_update_service.get_products_from_mysql: 107] INFO: 从MySQL获取产品数据成功，数量: 11717
[2025-09-08 11:21:31,218] [module_industry_chain_extension 14025] [vector_update_service.get_existing_product_ids: 135] INFO: 向量数据库中已存在产品数量: 11717
[2025-09-08 11:21:31,219] [module_industry_chain_extension 14025] [vector_update_service.update_vectors_incremental: 243] INFO: 所有产品数据已是最新，无需更新
[2025-09-08 11:21:31,220] [module_industry_chain_extension 14025] [semantic_search_service.ensure_vector_database_updated: 44] INFO: 向量数据库更新完成: {'update_type': 'incremental', 'updated_count': 0, 'total_count': 11717, 'duration_seconds': 0.461168, 'update_time': '2025-09-08T11:21:31.220205', 'task_id': None}
[2025-09-08 11:21:31,304] [module_industry_chain_extension 14025] [semantic_search_service.search_similar_products: 136] INFO: 向量搜索完成，返回结果数量: 100
[2025-09-08 11:21:31,304] [module_industry_chain_extension 14025] [semantic_search_service.filter_by_similarity_threshold: 156] INFO: 相似度阈值过滤: 100 -> 100 (阈值: 0.5)
[2025-09-08 11:21:31,304] [module_industry_chain_extension 14025] [semantic_search_service.get_detailed_search_results: 300] INFO: 详细搜索完成: 测试仪器 -> 5 个结果
[2025-09-08 11:21:31,312] [module_industry_chain_extension 14025] [vector_update_service.ensure_collection_exists: 41] INFO: 向量集合已存在: product_words_vector
[2025-09-08 11:21:31,469] [module_industry_chain_extension 14025] [vector_update_service.get_products_from_mysql: 107] INFO: 从MySQL获取产品数据成功，数量: 11717
[2025-09-08 11:21:31,591] [module_industry_chain_extension 14025] [vector_update_service.get_existing_product_ids: 135] INFO: 向量数据库中已存在产品数量: 11717
[2025-09-08 11:21:31,592] [module_industry_chain_extension 14025] [vector_update_service.update_vectors_incremental: 243] INFO: 所有产品数据已是最新，无需更新
[2025-09-08 11:21:31,593] [module_industry_chain_extension 14025] [semantic_search_service.ensure_vector_database_updated: 44] INFO: 向量数据库更新完成: {'update_type': 'incremental', 'updated_count': 0, 'total_count': 11717, 'duration_seconds': 0.288484, 'update_time': '2025-09-08T11:21:31.593427', 'task_id': None}
[2025-09-08 11:21:31,692] [module_industry_chain_extension 14025] [semantic_search_service.search_similar_products: 136] INFO: 向量搜索完成，返回结果数量: 100
[2025-09-08 11:21:31,693] [module_industry_chain_extension 14025] [semantic_search_service.filter_by_similarity_threshold: 156] INFO: 相似度阈值过滤: 100 -> 100 (阈值: 0.5)
[2025-09-08 11:21:31,693] [module_industry_chain_extension 14025] [semantic_search_service.get_detailed_search_results: 300] INFO: 详细搜索完成: 用户解决方案 -> 5 个结果
[2025-09-08 11:21:31,693] [module_industry_chain_extension 14025] [comprehensive_search_service.get_llm_fallback_results: 205] INFO: LLM保底逻辑完成，获得18个结果
[2025-09-08 11:21:31,693] [module_industry_chain_extension 14025] [comprehensive_search_service.calculate_comprehensive_similarity: 341] INFO: 计算综合相似度完成，权重α: 0.7
[2025-09-08 11:21:31,693] [module_industry_chain_extension 14025] [comprehensive_search_service.filter_and_sort_results: 371] INFO: 过滤和排序结果: 18 -> 18 -> 10
[2025-09-08 11:21:31,693] [module_industry_chain_extension 14025] [comprehensive_search_service.comprehensive_search: 453] INFO: LLM保底逻辑搜索完成: 超级新型未知产品XYZ123 -> 10 个结果
[2025-09-08 11:21:31,693] [module_industry_chain_extension 14025] [label_search_router.comprehensive_search: 140] INFO: 综合相似度搜索成功: 超级新型未知产品XYZ123 -> 10 个结果
[2025-09-08 11:21:32,702] [module_industry_chain_extension 14025] [label_search_router.comprehensive_search: 108] INFO: 综合相似度搜索请求: {'product_name': '神秘量子材料ABC999', 'semantic_threshold': 0.5, 'overall_threshold': 0.3, 'limit': 10, 'alpha': 0.7, 'k_parameter': 1.0}
[2025-09-08 11:21:32,702] [module_industry_chain_extension 14025] [comprehensive_search_service.comprehensive_search: 416] INFO: 开始综合相似度搜索: 神秘量子材料ABC999
[2025-09-08 11:21:32,714] [module_industry_chain_extension 14025] [vector_update_service.ensure_collection_exists: 41] INFO: 向量集合已存在: product_words_vector
[2025-09-08 11:21:33,026] [module_industry_chain_extension 14025] [vector_update_service.get_products_from_mysql: 107] INFO: 从MySQL获取产品数据成功，数量: 11717
[2025-09-08 11:21:33,182] [module_industry_chain_extension 14025] [vector_update_service.get_existing_product_ids: 135] INFO: 向量数据库中已存在产品数量: 11717
[2025-09-08 11:21:33,183] [module_industry_chain_extension 14025] [vector_update_service.update_vectors_incremental: 243] INFO: 所有产品数据已是最新，无需更新
[2025-09-08 11:21:33,184] [module_industry_chain_extension 14025] [semantic_search_service.ensure_vector_database_updated: 44] INFO: 向量数据库更新完成: {'update_type': 'incremental', 'updated_count': 0, 'total_count': 11717, 'duration_seconds': 0.48197, 'update_time': '2025-09-08T11:21:33.184463', 'task_id': None}
[2025-09-08 11:21:33,278] [module_industry_chain_extension 14025] [semantic_search_service.search_similar_products: 136] INFO: 向量搜索完成，返回结果数量: 100
[2025-09-08 11:21:33,278] [module_industry_chain_extension 14025] [semantic_search_service.filter_by_similarity_threshold: 156] INFO: 相似度阈值过滤: 100 -> 43 (阈值: 0.5)
[2025-09-08 11:21:33,278] [module_industry_chain_extension 14025] [semantic_search_service.get_detailed_search_results: 300] INFO: 详细搜索完成: 神秘量子材料ABC999 -> 43 个结果
[2025-09-08 11:21:33,278] [module_industry_chain_extension 14025] [comprehensive_search_service.get_semantic_similarity_results: 66] INFO: 获取语义相似度结果: 4 个
[2025-09-08 11:21:33,348] [module_industry_chain_extension 14025] [industry_chain_distance_util.get_industry_chain_nodes: 68] INFO: 获取产业链节点信息成功，任务ID: 1, 节点数量: 214
[2025-09-08 11:21:33,351] [module_industry_chain_extension 14025] [industry_chain_distance_util.get_node_distances: 158] INFO: 计算节点距离成功，目标节点: IND1-000083, 连通节点数: 53
[2025-09-08 11:21:33,351] [module_industry_chain_extension 14025] [industry_chain_distance_util.get_industry_chain_similarities: 202] INFO: 计算产业链相似度成功，目标节点: IND1-000083, 相似节点数: 53
[2025-09-08 11:21:33,410] [module_industry_chain_extension 14025] [industry_chain_search_service.get_products_by_node_codes: 122] INFO: 根据节点代码获取产品信息成功，节点数: 53, 产品数: 1169
[2025-09-08 11:21:33,411] [module_industry_chain_extension 14025] [industry_chain_search_service.get_industry_chain_products_with_details: 257] INFO: 获取产业链详细信息完成: 超导材料 -> 1169 个结果
[2025-09-08 11:21:37,251] [module_industry_chain_extension 14025] [comprehensive_search_service.get_industry_similarity_results: 137] INFO: 获取产业链相似度结果: 19 个
[2025-09-08 11:21:37,252] [module_industry_chain_extension 14025] [comprehensive_search_service.comprehensive_search: 462] INFO: 产业链搜索已启用，使用优化算法避免无限循环
[2025-09-08 11:21:37,252] [module_industry_chain_extension 14025] [comprehensive_search_service.calculate_comprehensive_similarity: 341] INFO: 计算综合相似度完成，权重α: 0.7
[2025-09-08 11:21:37,252] [module_industry_chain_extension 14025] [comprehensive_search_service.filter_and_sort_results: 371] INFO: 过滤和排序结果: 23 -> 20 -> 10
[2025-09-08 11:21:37,252] [module_industry_chain_extension 14025] [comprehensive_search_service.comprehensive_search: 479] INFO: 综合相似度搜索完成: 神秘量子材料ABC999 -> 10 个结果
[2025-09-08 11:21:37,252] [module_industry_chain_extension 14025] [label_search_router.comprehensive_search: 140] INFO: 综合相似度搜索成功: 神秘量子材料ABC999 -> 10 个结果
[2025-09-08 11:21:38,261] [module_industry_chain_extension 14025] [label_search_router.comprehensive_search: 108] INFO: 综合相似度搜索请求: {'product_name': '未来科技产品DEF888', 'semantic_threshold': 0.5, 'overall_threshold': 0.3, 'limit': 10, 'alpha': 0.7, 'k_parameter': 1.0}
[2025-09-08 11:21:38,262] [module_industry_chain_extension 14025] [comprehensive_search_service.comprehensive_search: 416] INFO: 开始综合相似度搜索: 未来科技产品DEF888
[2025-09-08 11:21:38,273] [module_industry_chain_extension 14025] [vector_update_service.ensure_collection_exists: 41] INFO: 向量集合已存在: product_words_vector
[2025-09-08 11:21:38,527] [module_industry_chain_extension 14025] [vector_update_service.get_products_from_mysql: 107] INFO: 从MySQL获取产品数据成功，数量: 11717
[2025-09-08 11:21:38,668] [module_industry_chain_extension 14025] [vector_update_service.get_existing_product_ids: 135] INFO: 向量数据库中已存在产品数量: 11717
[2025-09-08 11:21:38,670] [module_industry_chain_extension 14025] [vector_update_service.update_vectors_incremental: 243] INFO: 所有产品数据已是最新，无需更新
[2025-09-08 11:21:38,670] [module_industry_chain_extension 14025] [semantic_search_service.ensure_vector_database_updated: 44] INFO: 向量数据库更新完成: {'update_type': 'incremental', 'updated_count': 0, 'total_count': 11717, 'duration_seconds': 0.408804, 'update_time': '2025-09-08T11:21:38.670911', 'task_id': None}
[2025-09-08 11:21:38,755] [module_industry_chain_extension 14025] [semantic_search_service.search_similar_products: 136] INFO: 向量搜索完成，返回结果数量: 100
[2025-09-08 11:21:38,755] [module_industry_chain_extension 14025] [semantic_search_service.filter_by_similarity_threshold: 156] INFO: 相似度阈值过滤: 100 -> 1 (阈值: 0.5)
[2025-09-08 11:21:38,755] [module_industry_chain_extension 14025] [semantic_search_service.get_detailed_search_results: 300] INFO: 详细搜索完成: 未来科技产品DEF888 -> 1 个结果
[2025-09-08 11:21:38,755] [module_industry_chain_extension 14025] [comprehensive_search_service.get_semantic_similarity_results: 66] INFO: 获取语义相似度结果: 1 个
[2025-09-08 11:21:38,820] [module_industry_chain_extension 14025] [industry_chain_distance_util.get_industry_chain_nodes: 68] INFO: 获取产业链节点信息成功，任务ID: 2, 节点数量: 94
[2025-09-08 11:21:38,821] [module_industry_chain_extension 14025] [industry_chain_distance_util.get_node_distances: 158] INFO: 计算节点距离成功，目标节点: IND2-000054, 连通节点数: 69
[2025-09-08 11:21:38,821] [module_industry_chain_extension 14025] [industry_chain_distance_util.get_industry_chain_similarities: 202] INFO: 计算产业链相似度成功，目标节点: IND2-000054, 相似节点数: 69
[2025-09-08 11:21:38,900] [module_industry_chain_extension 14025] [industry_chain_search_service.get_products_by_node_codes: 122] INFO: 根据节点代码获取产品信息成功，节点数: 69, 产品数: 3159
[2025-09-08 11:21:38,901] [module_industry_chain_extension 14025] [industry_chain_search_service.get_industry_chain_products_with_details: 257] INFO: 获取产业链详细信息完成: 智能化产品 -> 3159 个结果
[2025-09-08 11:21:42,391] [module_industry_chain_extension 14025] [comprehensive_search_service.get_industry_similarity_results: 137] INFO: 获取产业链相似度结果: 20 个
[2025-09-08 11:21:42,392] [module_industry_chain_extension 14025] [comprehensive_search_service.comprehensive_search: 462] INFO: 产业链搜索已启用，使用优化算法避免无限循环
[2025-09-08 11:21:42,392] [module_industry_chain_extension 14025] [comprehensive_search_service.calculate_comprehensive_similarity: 341] INFO: 计算综合相似度完成，权重α: 0.7
[2025-09-08 11:21:42,392] [module_industry_chain_extension 14025] [comprehensive_search_service.filter_and_sort_results: 371] INFO: 过滤和排序结果: 21 -> 21 -> 10
[2025-09-08 11:21:42,392] [module_industry_chain_extension 14025] [comprehensive_search_service.comprehensive_search: 479] INFO: 综合相似度搜索完成: 未来科技产品DEF888 -> 10 个结果
[2025-09-08 11:21:42,392] [module_industry_chain_extension 14025] [label_search_router.comprehensive_search: 140] INFO: 综合相似度搜索成功: 未来科技产品DEF888 -> 10 个结果
[2025-09-08 11:21:43,402] [module_industry_chain_extension 14025] [label_search_router.comprehensive_search: 108] INFO: 综合相似度搜索请求: {'product_name': '锂电池', 'semantic_threshold': 0.5, 'overall_threshold': 0.3, 'limit': 10, 'alpha': 0.7, 'k_parameter': 1.0}
[2025-09-08 11:21:43,402] [module_industry_chain_extension 14025] [comprehensive_search_service.comprehensive_search: 416] INFO: 开始综合相似度搜索: 锂电池
[2025-09-08 11:21:43,473] [module_industry_chain_extension 14025] [vector_update_service.ensure_collection_exists: 41] INFO: 向量集合已存在: product_words_vector
[2025-09-08 11:21:43,936] [module_industry_chain_extension 14025] [vector_update_service.get_products_from_mysql: 107] INFO: 从MySQL获取产品数据成功，数量: 11717
[2025-09-08 11:21:44,125] [module_industry_chain_extension 14025] [vector_update_service.get_existing_product_ids: 135] INFO: 向量数据库中已存在产品数量: 11717
[2025-09-08 11:21:44,126] [module_industry_chain_extension 14025] [vector_update_service.update_vectors_incremental: 243] INFO: 所有产品数据已是最新，无需更新
[2025-09-08 11:21:44,127] [module_industry_chain_extension 14025] [semantic_search_service.ensure_vector_database_updated: 44] INFO: 向量数据库更新完成: {'update_type': 'incremental', 'updated_count': 0, 'total_count': 11717, 'duration_seconds': 0.724506, 'update_time': '2025-09-08T11:21:44.127125', 'task_id': None}
[2025-09-08 11:21:44,228] [module_industry_chain_extension 14025] [semantic_search_service.search_similar_products: 136] INFO: 向量搜索完成，返回结果数量: 100
[2025-09-08 11:21:44,228] [module_industry_chain_extension 14025] [semantic_search_service.filter_by_similarity_threshold: 156] INFO: 相似度阈值过滤: 100 -> 100 (阈值: 0.5)
[2025-09-08 11:21:44,228] [module_industry_chain_extension 14025] [semantic_search_service.get_detailed_search_results: 300] INFO: 详细搜索完成: 锂电池 -> 50 个结果
[2025-09-08 11:21:44,229] [module_industry_chain_extension 14025] [comprehensive_search_service.get_semantic_similarity_results: 66] INFO: 获取语义相似度结果: 7 个
[2025-09-08 11:21:44,259] [module_industry_chain_extension 14025] [industry_chain_distance_util.get_node_distances: 158] INFO: 计算节点距离成功，目标节点: IND1-000073, 连通节点数: 6
[2025-09-08 11:21:44,260] [module_industry_chain_extension 14025] [industry_chain_distance_util.get_industry_chain_similarities: 202] INFO: 计算产业链相似度成功，目标节点: IND1-000073, 相似节点数: 6
[2025-09-08 11:21:44,309] [module_industry_chain_extension 14025] [industry_chain_search_service.get_products_by_node_codes: 122] INFO: 根据节点代码获取产品信息成功，节点数: 6, 产品数: 268
[2025-09-08 11:21:44,310] [module_industry_chain_extension 14025] [industry_chain_search_service.get_industry_chain_products_with_details: 257] INFO: 获取产业链详细信息完成: 锂电 -> 268 个结果
[2025-09-08 11:21:48,183] [module_industry_chain_extension 14025] [comprehensive_search_service.get_industry_similarity_results: 137] INFO: 获取产业链相似度结果: 20 个
[2025-09-08 11:21:48,184] [module_industry_chain_extension 14025] [comprehensive_search_service.comprehensive_search: 462] INFO: 产业链搜索已启用，使用优化算法避免无限循环
[2025-09-08 11:21:48,184] [module_industry_chain_extension 14025] [comprehensive_search_service.calculate_comprehensive_similarity: 341] INFO: 计算综合相似度完成，权重α: 0.7
[2025-09-08 11:21:48,184] [module_industry_chain_extension 14025] [comprehensive_search_service.filter_and_sort_results: 371] INFO: 过滤和排序结果: 27 -> 27 -> 10
[2025-09-08 11:21:48,184] [module_industry_chain_extension 14025] [comprehensive_search_service.comprehensive_search: 479] INFO: 综合相似度搜索完成: 锂电池 -> 10 个结果
[2025-09-08 11:21:48,184] [module_industry_chain_extension 14025] [label_search_router.comprehensive_search: 140] INFO: 综合相似度搜索成功: 锂电池 -> 10 个结果
[2025-09-08 11:21:49,190] [module_industry_chain_extension 14025] [label_search_router.comprehensive_search: 108] INFO: 综合相似度搜索请求: {'product_name': '电动汽车', 'semantic_threshold': 0.5, 'overall_threshold': 0.3, 'limit': 10, 'alpha': 0.7, 'k_parameter': 1.0}
[2025-09-08 11:21:49,191] [module_industry_chain_extension 14025] [comprehensive_search_service.comprehensive_search: 416] INFO: 开始综合相似度搜索: 电动汽车
[2025-09-08 11:21:49,203] [module_industry_chain_extension 14025] [vector_update_service.ensure_collection_exists: 41] INFO: 向量集合已存在: product_words_vector
[2025-09-08 11:21:49,524] [module_industry_chain_extension 14025] [vector_update_service.get_products_from_mysql: 107] INFO: 从MySQL获取产品数据成功，数量: 11717
[2025-09-08 11:21:49,710] [module_industry_chain_extension 14025] [vector_update_service.get_existing_product_ids: 135] INFO: 向量数据库中已存在产品数量: 11717
[2025-09-08 11:21:49,711] [module_industry_chain_extension 14025] [vector_update_service.update_vectors_incremental: 243] INFO: 所有产品数据已是最新，无需更新
[2025-09-08 11:21:49,712] [module_industry_chain_extension 14025] [semantic_search_service.ensure_vector_database_updated: 44] INFO: 向量数据库更新完成: {'update_type': 'incremental', 'updated_count': 0, 'total_count': 11717, 'duration_seconds': 0.520964, 'update_time': '2025-09-08T11:21:49.712834', 'task_id': None}
[2025-09-08 11:21:49,812] [module_industry_chain_extension 14025] [semantic_search_service.search_similar_products: 136] INFO: 向量搜索完成，返回结果数量: 100
[2025-09-08 11:21:49,812] [module_industry_chain_extension 14025] [semantic_search_service.filter_by_similarity_threshold: 156] INFO: 相似度阈值过滤: 100 -> 100 (阈值: 0.5)
[2025-09-08 11:21:49,812] [module_industry_chain_extension 14025] [semantic_search_service.get_detailed_search_results: 300] INFO: 详细搜索完成: 电动汽车 -> 50 个结果
[2025-09-08 11:21:49,812] [module_industry_chain_extension 14025] [comprehensive_search_service.get_semantic_similarity_results: 66] INFO: 获取语义相似度结果: 7 个
[2025-09-08 11:21:49,858] [module_industry_chain_extension 14025] [industry_chain_distance_util.get_node_distances: 158] INFO: 计算节点距离成功，目标节点: IND2-000070, 连通节点数: 8
[2025-09-08 11:21:49,858] [module_industry_chain_extension 14025] [industry_chain_distance_util.get_industry_chain_similarities: 202] INFO: 计算产业链相似度成功，目标节点: IND2-000070, 相似节点数: 8
[2025-09-08 11:21:49,913] [module_industry_chain_extension 14025] [industry_chain_search_service.get_products_by_node_codes: 122] INFO: 根据节点代码获取产品信息成功，节点数: 8, 产品数: 936
[2025-09-08 11:21:49,913] [module_industry_chain_extension 14025] [industry_chain_search_service.get_industry_chain_products_with_details: 257] INFO: 获取产业链详细信息完成: 新能源电动汽车 -> 936 个结果
[2025-09-08 11:21:53,681] [module_industry_chain_extension 14025] [comprehensive_search_service.get_industry_similarity_results: 137] INFO: 获取产业链相似度结果: 20 个
[2025-09-08 11:21:53,681] [module_industry_chain_extension 14025] [comprehensive_search_service.comprehensive_search: 462] INFO: 产业链搜索已启用，使用优化算法避免无限循环
[2025-09-08 11:21:53,681] [module_industry_chain_extension 14025] [comprehensive_search_service.calculate_comprehensive_similarity: 341] INFO: 计算综合相似度完成，权重α: 0.7
[2025-09-08 11:21:53,681] [module_industry_chain_extension 14025] [comprehensive_search_service.filter_and_sort_results: 371] INFO: 过滤和排序结果: 27 -> 27 -> 10
[2025-09-08 11:21:53,681] [module_industry_chain_extension 14025] [comprehensive_search_service.comprehensive_search: 479] INFO: 综合相似度搜索完成: 电动汽车 -> 10 个结果
[2025-09-08 11:21:53,682] [module_industry_chain_extension 14025] [label_search_router.comprehensive_search: 140] INFO: 综合相似度搜索成功: 电动汽车 -> 10 个结果
[2025-09-08 11:21:54,692] [module_industry_chain_extension 14025] [label_search_router.comprehensive_search: 108] INFO: 综合相似度搜索请求: {'product_name': '太阳能电池板', 'semantic_threshold': 0.5, 'overall_threshold': 0.3, 'limit': 10, 'alpha': 0.7, 'k_parameter': 1.0}
[2025-09-08 11:21:54,692] [module_industry_chain_extension 14025] [comprehensive_search_service.comprehensive_search: 416] INFO: 开始综合相似度搜索: 太阳能电池板
[2025-09-08 11:21:54,701] [module_industry_chain_extension 14025] [vector_update_service.ensure_collection_exists: 41] INFO: 向量集合已存在: product_words_vector
[2025-09-08 11:21:55,030] [module_industry_chain_extension 14025] [vector_update_service.get_products_from_mysql: 107] INFO: 从MySQL获取产品数据成功，数量: 11717
[2025-09-08 11:21:55,208] [module_industry_chain_extension 14025] [vector_update_service.get_existing_product_ids: 135] INFO: 向量数据库中已存在产品数量: 11717
[2025-09-08 11:21:55,209] [module_industry_chain_extension 14025] [vector_update_service.update_vectors_incremental: 243] INFO: 所有产品数据已是最新，无需更新
[2025-09-08 11:21:55,210] [module_industry_chain_extension 14025] [semantic_search_service.ensure_vector_database_updated: 44] INFO: 向量数据库更新完成: {'update_type': 'incremental', 'updated_count': 0, 'total_count': 11717, 'duration_seconds': 0.51764, 'update_time': '2025-09-08T11:21:55.210614', 'task_id': None}
[2025-09-08 11:21:55,294] [module_industry_chain_extension 14025] [semantic_search_service.search_similar_products: 136] INFO: 向量搜索完成，返回结果数量: 100
[2025-09-08 11:21:55,295] [module_industry_chain_extension 14025] [semantic_search_service.filter_by_similarity_threshold: 156] INFO: 相似度阈值过滤: 100 -> 100 (阈值: 0.5)
[2025-09-08 11:21:55,295] [module_industry_chain_extension 14025] [semantic_search_service.get_detailed_search_results: 300] INFO: 详细搜索完成: 太阳能电池板 -> 50 个结果
[2025-09-08 11:21:55,295] [module_industry_chain_extension 14025] [comprehensive_search_service.get_semantic_similarity_results: 66] INFO: 获取语义相似度结果: 18 个
[2025-09-08 11:21:55,330] [module_industry_chain_extension 14025] [industry_chain_distance_util.get_node_distances: 158] INFO: 计算节点距离成功，目标节点: IND2-000092, 连通节点数: 5
[2025-09-08 11:21:55,330] [module_industry_chain_extension 14025] [industry_chain_distance_util.get_industry_chain_similarities: 202] INFO: 计算产业链相似度成功，目标节点: IND2-000092, 相似节点数: 5
[2025-09-08 11:21:55,393] [module_industry_chain_extension 14025] [industry_chain_search_service.get_products_by_node_codes: 122] INFO: 根据节点代码获取产品信息成功，节点数: 5, 产品数: 673
[2025-09-08 11:21:55,394] [module_industry_chain_extension 14025] [industry_chain_search_service.get_industry_chain_products_with_details: 257] INFO: 获取产业链详细信息完成: 太阳能电池板 -> 673 个结果
[2025-09-08 11:21:59,151] [module_industry_chain_extension 14025] [comprehensive_search_service.get_industry_similarity_results: 137] INFO: 获取产业链相似度结果: 20 个
[2025-09-08 11:21:59,151] [module_industry_chain_extension 14025] [comprehensive_search_service.comprehensive_search: 462] INFO: 产业链搜索已启用，使用优化算法避免无限循环
[2025-09-08 11:21:59,151] [module_industry_chain_extension 14025] [comprehensive_search_service.calculate_comprehensive_similarity: 341] INFO: 计算综合相似度完成，权重α: 0.7
[2025-09-08 11:21:59,152] [module_industry_chain_extension 14025] [comprehensive_search_service.filter_and_sort_results: 371] INFO: 过滤和排序结果: 38 -> 38 -> 10
[2025-09-08 11:21:59,152] [module_industry_chain_extension 14025] [comprehensive_search_service.comprehensive_search: 479] INFO: 综合相似度搜索完成: 太阳能电池板 -> 10 个结果
[2025-09-08 11:21:59,154] [module_industry_chain_extension 14025] [label_search_router.comprehensive_search: 144] ERROR: 综合相似度搜索参数错误: 2 validation errors for ComprehensiveSearchResponse
overall_similarity
  Input should be less than or equal to 1 [type=less_than_equal, input_value=1.0000000834465026, input_type=float]
    For further information visit https://errors.pydantic.dev/2.8/v/less_than_equal
semantic_similarity
  Input should be less than or equal to 1 [type=less_than_equal, input_value=1.0000001192092896, input_type=float]
    For further information visit https://errors.pydantic.dev/2.8/v/less_than_equal
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/tiance-industry-finance/service_label_search/api/router/label_search_router.py", line 124, in comprehensive_search
    response_item = ComprehensiveSearchResponse(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/lib/python3.12/site-packages/pydantic/main.py", line 193, in __init__
    self.__pydantic_validator__.validate_python(data, self_instance=self)
pydantic_core._pydantic_core.ValidationError: 2 validation errors for ComprehensiveSearchResponse
overall_similarity
  Input should be less than or equal to 1 [type=less_than_equal, input_value=1.0000000834465026, input_type=float]
    For further information visit https://errors.pydantic.dev/2.8/v/less_than_equal
semantic_similarity
  Input should be less than or equal to 1 [type=less_than_equal, input_value=1.0000001192092896, input_type=float]
    For further information visit https://errors.pydantic.dev/2.8/v/less_than_equal
