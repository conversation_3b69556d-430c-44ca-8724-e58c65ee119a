[2025-09-08 11:15:57,051] [test_llm_industry_chain_service 9245] [llm_industry_chain_service._parse_llm_response: 94] INFO: 成功解析LLM响应，获得5个产品
[2025-09-08 11:16:12,620] [test_llm_industry_chain_service 9586] [llm_industry_chain_service.generate_industry_chain_products: 136] WARNING: 产品名称为空，无法生成产业链
[2025-09-08 11:16:12,720] [test_llm_industry_chain_service 9586] [llm_industry_chain_service.generate_industry_chain_products: 139] INFO: 开始为产品'锂电池'生成产业链相关产品
[2025-09-08 11:16:12,720] [test_llm_industry_chain_service 9586] [llm_industry_chain_service.generate_industry_chain_products: 152] INFO: LLM响应: 无法理解的响应内容
[2025-09-08 11:16:12,721] [test_llm_industry_chain_service 9586] [llm_industry_chain_service._parse_llm_response: 118] INFO: 通过备用解析方式获得1个产品
[2025-09-08 11:16:12,721] [test_llm_industry_chain_service 9586] [llm_industry_chain_service.generate_industry_chain_products: 160] INFO: 为产品'锂电池'生成了1个相关产品: ['无法理解的响应内容']
[2025-09-08 11:16:12,819] [test_llm_industry_chain_service 9586] [llm_industry_chain_service.generate_industry_chain_products: 139] INFO: 开始为产品'锂电池'生成产业链相关产品
[2025-09-08 11:16:12,819] [test_llm_industry_chain_service 9586] [llm_industry_chain_service.generate_industry_chain_products: 164] ERROR: 生成产业链产品失败: LLM调用失败
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/tiance-industry-finance/service_label_search/service/llm_industry_chain_service.py", line 146, in generate_industry_chain_products
    response = self.llm_service.answer_question(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/lib/python3.12/unittest/mock.py", line 1134, in __call__
    return self._mock_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/lib/python3.12/unittest/mock.py", line 1138, in _mock_call
    return self._execute_mock_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/lib/python3.12/unittest/mock.py", line 1193, in _execute_mock_call
    raise effect
Exception: LLM调用失败
[2025-09-08 11:16:12,918] [test_llm_industry_chain_service 9586] [llm_industry_chain_service.generate_industry_chain_products: 139] INFO: 开始为产品'锂电池'生成产业链相关产品
[2025-09-08 11:16:12,918] [test_llm_industry_chain_service 9586] [llm_industry_chain_service.generate_industry_chain_products: 152] INFO: LLM响应: \n        {\n            "products": [\n                "锂矿",\n                "电解液",\n                "正极材料",\n                "负极材料",\n                "电动汽车",\n                "储能系统"\n            ]\n        }\n        
[2025-09-08 11:16:12,918] [test_llm_industry_chain_service 9586] [llm_industry_chain_service._parse_llm_response: 94] INFO: 成功解析LLM响应，获得6个产品
[2025-09-08 11:16:12,918] [test_llm_industry_chain_service 9586] [llm_industry_chain_service.generate_industry_chain_products: 160] INFO: 为产品'锂电池'生成了6个相关产品: ['锂矿', '电解液', '正极材料', '负极材料', '电动汽车', '储能系统']
[2025-09-08 11:16:13,015] [test_llm_industry_chain_service 9586] [llm_industry_chain_service._parse_llm_response: 121] WARNING: 无法解析LLM响应，返回空列表
[2025-09-08 11:16:13,111] [test_llm_industry_chain_service 9586] [llm_industry_chain_service._parse_llm_response: 118] INFO: 通过备用解析方式获得5个产品
[2025-09-08 11:16:13,209] [test_llm_industry_chain_service 9586] [llm_industry_chain_service._parse_llm_response: 121] WARNING: 无法解析LLM响应，返回空列表
[2025-09-08 11:16:13,305] [test_llm_industry_chain_service 9586] [llm_industry_chain_service._parse_llm_response: 94] INFO: 成功解析LLM响应，获得5个产品
[2025-09-08 11:16:13,402] [test_llm_industry_chain_service 9586] [llm_industry_chain_service.validate_generated_products: 203] INFO: 产品验证完成: 9 -> 4
[2025-09-08 11:17:45,491] [test_llm_industry_chain_service 11034] [llm_industry_chain_service.generate_industry_chain_products: 136] WARNING: 产品名称为空，无法生成产业链
[2025-09-08 11:17:45,589] [test_llm_industry_chain_service 11034] [llm_industry_chain_service.generate_industry_chain_products: 139] INFO: 开始为产品'锂电池'生成产业链相关产品
[2025-09-08 11:17:45,589] [test_llm_industry_chain_service 11034] [llm_industry_chain_service.generate_industry_chain_products: 152] INFO: LLM响应: 无法理解的响应内容
[2025-09-08 11:17:45,590] [test_llm_industry_chain_service 11034] [llm_industry_chain_service._parse_llm_response: 118] INFO: 通过备用解析方式获得1个产品
[2025-09-08 11:17:45,590] [test_llm_industry_chain_service 11034] [llm_industry_chain_service.generate_industry_chain_products: 160] INFO: 为产品'锂电池'生成了1个相关产品: ['无法理解的响应内容']
[2025-09-08 11:17:45,695] [test_llm_industry_chain_service 11034] [llm_industry_chain_service.generate_industry_chain_products: 139] INFO: 开始为产品'锂电池'生成产业链相关产品
[2025-09-08 11:17:45,695] [test_llm_industry_chain_service 11034] [llm_industry_chain_service.generate_industry_chain_products: 164] ERROR: 生成产业链产品失败: LLM调用失败
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/tiance-industry-finance/service_label_search/service/llm_industry_chain_service.py", line 146, in generate_industry_chain_products
    response = self.llm_service.answer_question(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/lib/python3.12/unittest/mock.py", line 1134, in __call__
    return self._mock_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/lib/python3.12/unittest/mock.py", line 1138, in _mock_call
    return self._execute_mock_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/lib/python3.12/unittest/mock.py", line 1193, in _execute_mock_call
    raise effect
Exception: LLM调用失败
[2025-09-08 11:17:45,794] [test_llm_industry_chain_service 11034] [llm_industry_chain_service.generate_industry_chain_products: 139] INFO: 开始为产品'锂电池'生成产业链相关产品
[2025-09-08 11:17:45,794] [test_llm_industry_chain_service 11034] [llm_industry_chain_service.generate_industry_chain_products: 152] INFO: LLM响应: \n        {\n            "products": [\n                "锂矿",\n                "电解液",\n                "正极材料",\n                "负极材料",\n                "电动汽车",\n                "储能系统"\n            ]\n        }\n        
[2025-09-08 11:17:45,794] [test_llm_industry_chain_service 11034] [llm_industry_chain_service._parse_llm_response: 94] INFO: 成功解析LLM响应，获得6个产品
[2025-09-08 11:17:45,794] [test_llm_industry_chain_service 11034] [llm_industry_chain_service.generate_industry_chain_products: 160] INFO: 为产品'锂电池'生成了6个相关产品: ['锂矿', '电解液', '正极材料', '负极材料', '电动汽车', '储能系统']
[2025-09-08 11:17:45,890] [test_llm_industry_chain_service 11034] [llm_industry_chain_service._parse_llm_response: 121] WARNING: 无法解析LLM响应，返回空列表
[2025-09-08 11:17:45,986] [test_llm_industry_chain_service 11034] [llm_industry_chain_service._parse_llm_response: 118] INFO: 通过备用解析方式获得5个产品
[2025-09-08 11:17:46,084] [test_llm_industry_chain_service 11034] [llm_industry_chain_service._parse_llm_response: 121] WARNING: 无法解析LLM响应，返回空列表
[2025-09-08 11:17:46,182] [test_llm_industry_chain_service 11034] [llm_industry_chain_service._parse_llm_response: 94] INFO: 成功解析LLM响应，获得5个产品
[2025-09-08 11:17:46,281] [test_llm_industry_chain_service 11034] [llm_industry_chain_service.validate_generated_products: 203] INFO: 产品验证完成: 9 -> 3
[2025-09-08 11:18:39,785] [test_llm_industry_chain_service 11976] [llm_industry_chain_service.generate_industry_chain_products: 136] WARNING: 产品名称为空，无法生成产业链
[2025-09-08 11:18:39,884] [test_llm_industry_chain_service 11976] [llm_industry_chain_service.generate_industry_chain_products: 139] INFO: 开始为产品'锂电池'生成产业链相关产品
[2025-09-08 11:18:39,884] [test_llm_industry_chain_service 11976] [llm_industry_chain_service.generate_industry_chain_products: 152] INFO: LLM响应: 这是一个完全无法解析的响应，没有任何产品信息，也没有列表格式。
[2025-09-08 11:18:39,884] [test_llm_industry_chain_service 11976] [llm_industry_chain_service._parse_llm_response: 121] WARNING: 无法解析LLM响应，返回空列表
[2025-09-08 11:18:39,884] [test_llm_industry_chain_service 11976] [llm_industry_chain_service.generate_industry_chain_products: 160] INFO: 为产品'锂电池'生成了0个相关产品: []
[2025-09-08 11:18:39,980] [test_llm_industry_chain_service 11976] [llm_industry_chain_service.generate_industry_chain_products: 139] INFO: 开始为产品'锂电池'生成产业链相关产品
[2025-09-08 11:18:39,980] [test_llm_industry_chain_service 11976] [llm_industry_chain_service.generate_industry_chain_products: 164] ERROR: 生成产业链产品失败: LLM调用失败
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/tiance-industry-finance/service_label_search/service/llm_industry_chain_service.py", line 146, in generate_industry_chain_products
    response = self.llm_service.answer_question(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/lib/python3.12/unittest/mock.py", line 1134, in __call__
    return self._mock_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/lib/python3.12/unittest/mock.py", line 1138, in _mock_call
    return self._execute_mock_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/lib/python3.12/unittest/mock.py", line 1193, in _execute_mock_call
    raise effect
Exception: LLM调用失败
[2025-09-08 11:18:40,080] [test_llm_industry_chain_service 11976] [llm_industry_chain_service.generate_industry_chain_products: 139] INFO: 开始为产品'锂电池'生成产业链相关产品
[2025-09-08 11:18:40,080] [test_llm_industry_chain_service 11976] [llm_industry_chain_service.generate_industry_chain_products: 152] INFO: LLM响应: \n        {\n            "products": [\n                "锂矿",\n                "电解液",\n                "正极材料",\n                "负极材料",\n                "电动汽车",\n                "储能系统"\n            ]\n        }\n        
[2025-09-08 11:18:40,080] [test_llm_industry_chain_service 11976] [llm_industry_chain_service._parse_llm_response: 94] INFO: 成功解析LLM响应，获得6个产品
[2025-09-08 11:18:40,080] [test_llm_industry_chain_service 11976] [llm_industry_chain_service.generate_industry_chain_products: 160] INFO: 为产品'锂电池'生成了6个相关产品: ['锂矿', '电解液', '正极材料', '负极材料', '电动汽车', '储能系统']
[2025-09-08 11:18:40,176] [test_llm_industry_chain_service 11976] [llm_industry_chain_service._parse_llm_response: 121] WARNING: 无法解析LLM响应，返回空列表
[2025-09-08 11:18:40,272] [test_llm_industry_chain_service 11976] [llm_industry_chain_service._parse_llm_response: 118] INFO: 通过备用解析方式获得5个产品
[2025-09-08 11:18:40,370] [test_llm_industry_chain_service 11976] [llm_industry_chain_service._parse_llm_response: 121] WARNING: 无法解析LLM响应，返回空列表
[2025-09-08 11:18:40,476] [test_llm_industry_chain_service 11976] [llm_industry_chain_service._parse_llm_response: 94] INFO: 成功解析LLM响应，获得5个产品
[2025-09-08 11:18:40,621] [test_llm_industry_chain_service 11976] [llm_industry_chain_service.validate_generated_products: 203] INFO: 产品验证完成: 9 -> 3
