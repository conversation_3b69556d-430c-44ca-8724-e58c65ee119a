﻿import requests
from bs4 import BeautifulSoup
import re
import os
def extract_uniprot_ids(line):
    # print(line)
    if line.startswith('>'):
        print("match")
        match = re.search(r'\|([A-Z0-9]+)\|', line)
        # match = re.search(r'([A-Z0-9]+)', line)
        
        print(match)
        if match:
            return match.group(1)
    return None
def extract_uniparc_ids(line):
    # print(line)
    if line.startswith('>'):
        print("match")
        match = re.search(r'\|([A-Z0-9]+)\|', line)
        # match = re.search(r'([A-Z0-9]+)', line)
        
        print(match)
        if match:
            return match.group(1)
    return None
    # with open(fasta_file, 'r') as file:
    #     lines = file.readlines()
    
    # uniprot_ids = []
    
    # for line in lines:
    #     if line.startswith('>'):
    #         # 使用正则表达式提取 UniProt ID
    #         match = re.search(r'\|([A-Z0-9]+)\|', line)
    #         if match:
    #             uniprot_ids.append(match.group(1))
    
    return uniprot_ids

def get_alphafold_pdb(protein_name):
    # UniProt 搜索 URL
    # uniprot_search_url = f"https://www.uniprot.org/uniprotkb/?query={protein_name}&sort=score"
    uniprot_search_url = f"https://rest.uniprot.org/uniprotkb/search?query={protein_name}&format=fasta"
    # uniprot_search_url = f"https://rest.uniprot.org/uniparc/search?query={protein_name}&format=fasta"
    print(uniprot_search_url)
    # 发送请求
    response = requests.get(uniprot_search_url)
    if response.status_code != 200:
        print("Failed to retrieve data from UniProt.")
        return None
    print(response.text)
    
    # 解析 HTML
    # soup = BeautifulSoup(response.content, 'html.parser')
    
    # 查找第一个搜索结果的  IDUniProt
    uniprot_id = None
    uniprot_id = extract_uniprot_ids(response.text)
    # for entry in soup.find_all('a', class_='BqBnJ'):
    #     uniprot_id = entry.text.strip()
    #     break

    
    if not uniprot_id:
        print(protein_name)
        print("No UniProt ID found for the given protein name.")
        return None
    
    print(f"UniProt ID found: {uniprot_id}")
    
    # AlphaFold 数据库 URL
    alphafold_url = f"https://alphafold.ebi.ac.uk/api/prediction/{uniprot_id}"
    print(alphafold_url)
    # 发送请求到 AlphaFold 数据库
    response = requests.get(alphafold_url)
    
    if response.status_code != 200:
        print("Failed to retrieve data from AlphaFold.")
        return None
    print(response.json()[0]["pdbUrl"])
    # 解析 AlphaFold 页面
    # soup = BeautifulSoup(response.content, 'html.parser')
    
    # 查找 PDB 文件下载链接
    pdb_link = response.json()[0]["pdbUrl"]
    # for link in soup.find_all('a', href=True):
    #     print(link['href'])
    #     if link['href'].endswith('.pdb'):
    #         pdb_link = link['href']
    #         break
    
    if not pdb_link:
        print("protein_name")
        print("No PDB file found for the given UniProt ID.")
        return None
    
    print(f"PDB file link found: {pdb_link}")
    
    # 下载 PDB 文件
    pdb_response = requests.get(pdb_link)
    if pdb_response.status_code == 200:
        if not os.path.exists(f"./rice_pdb/{protein_name}.pdb"):
            with open(f"./rice_pdb/{protein_name}.pdb", 'wb') as pdb_file:
                pdb_file.write(pdb_response.content)
                print("saved pdb file")
        print(f"PDB file saved as {protein_name}.pdb")
    else:
        print("Failed to download PDB file.")


def download_pdb_from_uniparc(uniparc_id, output_dir='./test'):
    """
    根据 UniParc ID 下载对应的 AlphaFold2 PDB 文件。
    :param uniparc_id: UniParc ID，例如 'UPI00003FF26A'
    :param output_dir: 保存 PDB 文件的目录
    """
    # 将 UniParc ID 转换为 AlphaFold 数据库中的文件名格式
    # 注意：AlphaFold 数据库中的文件名通常基于 UniProt ID，需要先从 UniParc 映射到 UniProt ID[^45^]
    # 这里假设 UniParc ID 和 UniProt ID 映射关系已知，直接使用 UniParc ID 替代
    pdb_filename = f"AF-{uniparc_id}-F1-model_v4.pdb"
    url = f"https://alphafold.ebi.ac.uk/files/{pdb_filename}"

    # 发起请求
    response = requests.get(url, verify=False)  # 禁用 SSL 验证以避免警告[^42^]
    if response.status_code == 200:
        # 保存 PDB 文件
        os.makedirs(output_dir, exist_ok=True)
        with open(os.path.join(output_dir, pdb_filename), "w") as f:
            f.write(response.text)
        print(f"PDB 文件已下载到 {os.path.join(output_dir, pdb_filename)}")
    else:
        print(f"未找到 {uniparc_id} 对应的 PDB 文件。")


# 示例：查询蛋白质名称
# protein_name = "LOC_Os06g39110"
# protein_name = "UPI00003FF26A"
# download_pdb_from_uniparc(protein_name)


def predict_esmfold():
    sequence="MERGASFAVVMAAMAVVLATTSTAQAQTTATDIVNIHNAARSAVGVPALSWDDNLAAYAQGYANQRAGDCALRHSDRNNYQYGENLSWNPSVQAWTAASSVDQWVAEKGSYDYASNSCVGGAMCGHYTQVVWRDTTAVGCAAVACNANRGVFFICTYFPAGNVQNQRPY"
    for s in sequence:
        # print(s)
        if s not in {'Z', 'J', 'A', 'H', 'N', 'G', 'R', 'L', 'D', 'E', 'S', 'F', 'V', 'K', 'B', 'W', 'Q', 'I', 'P', 'C', 'T', 'Y', 'M', 'X'}:
            print("序列中含有非标准的氨基酸。")
            print(s)
    url = "https://api.esmatlas.com/foldSequence/v1/pdb/"
    response = requests.post(url, data={"sequence": sequence})
    print(response.text)
predict_esmfold()
# BqBnJ