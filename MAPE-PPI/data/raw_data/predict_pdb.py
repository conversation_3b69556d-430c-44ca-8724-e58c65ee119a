﻿import requests
import os
# from Bio import SeqIO

# AlphaFold Server API endpoint
ALPHAFOLD_SERVER_URL = "https://alphafoldserver.com/predict"

import requests
import time
import os

# 设置AlphaFold API的URL和你的序列
url = "https://alphafold.ebi.ac.uk/api/prediction"

# 假设你已经有了一个蛋白质序列
sequence = """>Example Sequence
MKTAYIAKQRQISFVKSHFSRQDILDH
"""  # 用你自己的蛋白质序列替换这里
def predict_structure(seq_id,sequences):
# 准备请求数据
    payload = {
        "sequence": sequence,
    }

    # 提交预测请求
    response = requests.post(url, json=payload)
    print(response)
    # 检查是否成功提交请求
    if response.status_code == 200:
        print(response.json())
        job_id = response.json().get('jobId')  # 获取任务ID
        print(f"Job submitted successfully, Job ID: {job_id}")
    else:
        print("Error submitting job:", response.text)
        exit()

    # 等待预测完成（你可以根据实际情况调整等待时间）
    # 这一步可能需要根据API的响应情况来调整
    print("Waiting for prediction to complete...")
    while True:
        status_response = requests.get(f"{url}/{job_id}/status")
        status_data = status_response.json()
        status = status_data['status']

        if status == 'SUCCESS':
            print("Prediction complete!")
            break
        elif status == 'FAILED':
            print("Prediction failed.")
            exit()
        else:
            print("Still processing...")
            time.sleep(60)  # 每分钟检查一次状态

    # 下载预测的PDB文件
    download_url = f"{url}/{job_id}/download"
    pdb_response = requests.get(download_url)

    # 保存PDB文件
    if pdb_response.status_code == 200:
        with open("predicted_structure.pdb", "wb") as f:
            f.write(pdb_response.content)
        print("PDB file saved successfully!")
    else:
        print("Error downloading PDB:", pdb_response.text)


def main():
    # Read the FASTA file
    # sequences = []
    seq_id = "LOC_Os07g03467.1 "
    sequences = "MERGASFAVVMAAMAVVLATTSTAQAQTTATDIVNIHNAARSAVGVPALSWDDNLAAYAQGYANQRAGDCALRHSDRNNYQYGENLSWNPSVQAWTAASSVDQWVAEKGSYDYASNSCVGGAMCGHYTQVVWRDTTAVGCAAVACNANRGVFFICTYFPAGNVQNQRPY"

    # with open(INPUT_FASTA_FILE, "r") as handle:
    #     for record in SeqIO.parse(handle, "fasta"):
    #         sequences.append((record.id, str(record.seq)))
    predict_structure(seq_id, sequences)

if __name__ == "__main__":
    main()