﻿import argparse
import requests
import os

def uniparc_to_uniprot(uniparc_id):
    url = f"https://www.uniprot.org/uniparc/?query={uniparc_id}&format=tab&columns=uniprot-id"
    response = requests.get(url)
    if response.status_code == 200:
        lines = response.text.split('\n')
        if len(lines) >= 2:
            parts = lines[1].split('\t')
            if len(parts) >= 2:
                uniprot_ids = parts[1].split('; ')
                return uniprot_ids
    return None

def download_alphafold_pdb(uniprot_id, output_file):
    url = f"https://alphafold.ebi.ac.uk/files/AF-{uniprot_id}-F1-model_v4.pdb"
    response = requests.get(url)
    if response.status_code == 200:
        with open(output_file, 'w') as f:
            f.write(response.text)
        return True
    return False

def predict_esmfold(fasta_file, output_file):
    with open(fasta_file, 'r') as f:
        sequence = ''.join(line.strip() for line in f if not line.startswith('>'))
    
    url = "https://api.esmatlas.com/foldSequence/v1/pdb/"
    response = requests.post(url, data={"sequence": sequence})
    
    if response.status_code == 200:
        with open(output_file, 'w') as f:
            f.write(response.text)
        return True
    return False

def main():
    parser = argparse.ArgumentParser(description='Generate PDB files from UniParc ID or FASTA sequence')
    parser.add_argument('--uniparc', type=str, help='UniParc ID (e.g. UPI0000000001)')
    parser.add_argument('--fasta', type=str, help='Path to FASTA file')
    parser.add_argument('--output', type=str, default='output.pdb', help='Output PDB file path')
    args = parser.parse_args()

    if args.uniparc:
        print(f"Processing UniParc ID: {args.uniparc}")
        uniprot_ids = uniparc_to_uniprot(args.uniparc)
        if not uniprot_ids:
            print("Failed to convert UniParc ID to UniProt ID")
            return
        
        print(f"Found UniProt IDs: {', '.join(uniprot_ids)}")
        success = False
        for uniprot_id in uniprot_ids:
            if download_alphafold_pdb(uniprot_id, args.output):
                print(f"Successfully downloaded AlphaFold model for {uniprot_id}")
                success = True
                break
        if not success:
            print("No AlphaFold model available for these UniProt IDs")

    elif args.fasta:
        print(f"Processing FASTA file: {args.fasta}")
        if predict_esmfold(args.fasta, args.output):
            print(f"ESMFold prediction saved to {args.output}")
        else:
            print("Failed to generate prediction from ESMFold API")

    else:
        print("Please provide either --uniparc or --fasta argument")

if __name__ == "__main__":
    main()