﻿def load_sequences2(file_path):
    seq_dict = {}
    with open(file_path, 'r') as rf:
        seq = ''
        for line in rf:
            line = line.strip()
            if line[0] == '>':
                item = line.split(" ")
                name = item[0][1:-2] 
                seq = ''
                print(name)
                seq_dict[name] = ""
            else:
                seq = line.upper().strip(" ").strip('*')
                if name in seq_dict:
                    seq_dict[name] += seq
                else:
                    seq_dict[name] = seq
    return seq_dict


load_sequences2("./rice/grind_pep_sequence.fasta.txt")