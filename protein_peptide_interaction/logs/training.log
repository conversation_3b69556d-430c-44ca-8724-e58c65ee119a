2025-08-26 17:17:31,248 - src.training.trainer - INFO - Training samples: 4
2025-08-26 17:17:31,248 - src.training.trainer - INFO - Validation samples: 4
2025-08-26 17:17:31,250 - src.training.trainer - INFO - Trainer initialized on device: cpu
2025-08-26 17:17:31,250 - src.training.trainer - INFO - Distributed training: False, World size: 1
2025-08-26 17:17:31,281 - src.training.trainer - INFO - Training samples: 4
2025-08-26 17:17:31,281 - src.training.trainer - INFO - Training samples: 4
2025-08-26 17:17:31,281 - src.training.trainer - INFO - Validation samples: 4
2025-08-26 17:17:31,281 - src.training.trainer - INFO - Validation samples: 4
2025-08-26 17:17:31,282 - src.training.trainer - INFO - Trainer initialized on device: cpu
2025-08-26 17:17:31,282 - src.training.trainer - INFO - Trainer initialized on device: cpu
2025-08-26 17:17:31,282 - src.training.trainer - INFO - Distributed training: False, World size: 1
2025-08-26 17:17:31,282 - src.training.trainer - INFO - Distributed training: False, World size: 1
2025-08-26 17:18:12,977 - src.training.trainer - INFO - Training samples: 4
2025-08-26 17:18:12,977 - src.training.trainer - INFO - Validation samples: 4
2025-08-26 17:18:12,978 - src.training.trainer - INFO - Trainer initialized on device: cpu
2025-08-26 17:18:12,978 - src.training.trainer - INFO - Distributed training: False, World size: 1
2025-08-26 17:18:13,008 - src.training.trainer - INFO - Training samples: 4
2025-08-26 17:18:13,008 - src.training.trainer - INFO - Training samples: 4
2025-08-26 17:18:13,008 - src.training.trainer - INFO - Validation samples: 4
2025-08-26 17:18:13,008 - src.training.trainer - INFO - Validation samples: 4
2025-08-26 17:18:13,009 - src.training.trainer - INFO - Trainer initialized on device: cpu
2025-08-26 17:18:13,009 - src.training.trainer - INFO - Trainer initialized on device: cpu
2025-08-26 17:18:13,009 - src.training.trainer - INFO - Distributed training: False, World size: 1
2025-08-26 17:18:13,009 - src.training.trainer - INFO - Distributed training: False, World size: 1
2025-08-26 17:18:53,407 - src.training.trainer - INFO - Training samples: 8
2025-08-26 17:18:53,408 - src.training.trainer - INFO - Validation samples: 8
2025-08-26 17:18:53,410 - src.training.trainer - INFO - Trainer initialized on device: cpu
2025-08-26 17:18:53,410 - src.training.trainer - INFO - Distributed training: False, World size: 1
2025-08-26 17:18:53,410 - src.training.trainer - INFO - Starting training...
2025-08-26 17:21:40,468 - src.training.trainer - INFO - Training samples: 8
2025-08-26 17:21:40,469 - src.training.trainer - INFO - Validation samples: 8
2025-08-26 17:21:40,470 - src.training.trainer - INFO - Trainer initialized on device: cpu
2025-08-26 17:21:40,470 - src.training.trainer - INFO - Distributed training: False, World size: 1
2025-08-26 17:21:40,470 - src.training.trainer - INFO - Starting training...
2025-08-26 17:21:40,710 - src.training.trainer - WARNING - No validation data available for metrics calculation
2025-08-26 17:21:40,710 - src.training.trainer - INFO - Epoch 0 - train_loss: 0.0000 - val_loss: 0.0000
2025-08-26 17:21:40,866 - src.training.trainer - INFO - Checkpoint saved: checkpoints/checkpoint_epoch_0.pth
2025-08-26 17:21:40,940 - src.training.trainer - INFO - Best model saved: checkpoints/best_model.pth
2025-08-26 17:21:43,466 - src.training.trainer - WARNING - No validation data available for metrics calculation
2025-08-26 17:21:43,467 - src.training.trainer - INFO - Epoch 1 - train_loss: 0.0000 - val_loss: 0.0000
2025-08-26 17:21:43,467 - src.training.trainer - INFO - Training completed!
2025-08-27 09:53:22,098 - src.training.trainer - INFO - Training samples: 100
2025-08-27 09:53:22,099 - src.training.trainer - INFO - Validation samples: 100
2025-08-27 09:54:56,385 - src.training.trainer - INFO - Training samples: 100
2025-08-27 09:54:56,386 - src.training.trainer - INFO - Validation samples: 100
2025-08-27 11:22:00,022 - src.training.trainer - INFO - Training samples: 10
2025-08-27 11:22:00,022 - src.training.trainer - INFO - Validation samples: 10
2025-08-27 11:22:00,024 - src.training.trainer - INFO - Trainer initialized on device: cpu
2025-08-27 11:22:00,024 - src.training.trainer - INFO - Distributed training: False, World size: 1
2025-08-27 11:22:00,024 - src.training.trainer - INFO - Starting training...
2025-08-27 11:22:00,281 - src.training.trainer - WARNING - No validation data available for metrics calculation
2025-08-27 11:22:00,281 - src.training.trainer - INFO - Epoch 0 - train_loss: 0.0000 - val_loss: 0.0000
2025-08-27 11:22:00,451 - src.training.trainer - INFO - Checkpoint saved: checkpoints/checkpoint_epoch_0.pth
2025-08-27 11:22:00,634 - src.training.trainer - INFO - Best model saved: checkpoints/best_model.pth
2025-08-27 11:22:03,769 - src.training.trainer - WARNING - No validation data available for metrics calculation
2025-08-27 11:22:03,770 - src.training.trainer - INFO - Epoch 1 - train_loss: 0.0000 - val_loss: 0.0000
2025-08-27 11:22:03,771 - src.training.trainer - INFO - Training completed!
2025-08-27 11:29:27,882 - src.training.trainer - INFO - Training samples: 10
2025-08-27 11:29:27,882 - src.training.trainer - INFO - Validation samples: 10
2025-08-27 11:29:27,884 - src.training.trainer - INFO - Trainer initialized on device: cpu
2025-08-27 11:29:27,884 - src.training.trainer - INFO - Distributed training: False, World size: 1
2025-08-27 11:29:27,884 - src.training.trainer - INFO - Starting training...
2025-08-27 11:29:28,128 - src.training.trainer - WARNING - No validation data available for metrics calculation
2025-08-27 11:29:28,129 - src.training.trainer - INFO - Epoch 0 - train_loss: 0.0000 - val_loss: 0.0000
2025-08-27 11:29:28,232 - src.training.trainer - INFO - Checkpoint saved: checkpoints/checkpoint_epoch_0.pth
2025-08-27 11:29:28,305 - src.training.trainer - INFO - Best model saved: checkpoints/best_model.pth
2025-08-27 11:29:30,813 - src.training.trainer - WARNING - No validation data available for metrics calculation
2025-08-27 11:29:30,813 - src.training.trainer - INFO - Epoch 1 - train_loss: 0.0000 - val_loss: 0.0000
2025-08-27 11:29:30,814 - src.training.trainer - INFO - Training completed!
2025-08-27 11:34:25,985 - src.training.trainer - INFO - Training samples: 100
2025-08-27 11:34:25,985 - src.training.trainer - INFO - Validation samples: 100
2025-08-27 11:34:26,591 - src.training.trainer - INFO - Trainer initialized on device: cpu
2025-08-27 11:34:26,591 - src.training.trainer - INFO - Distributed training: False, World size: 1
2025-08-27 11:34:26,591 - src.training.trainer - INFO - Starting training...
2025-08-27 11:35:25,595 - src.training.trainer - INFO - Training samples: 100
2025-08-27 11:35:25,595 - src.training.trainer - INFO - Validation samples: 100
2025-08-27 11:35:26,177 - src.training.trainer - INFO - Trainer initialized on device: cpu
2025-08-27 11:35:26,177 - src.training.trainer - INFO - Distributed training: False, World size: 1
2025-08-27 11:35:26,177 - src.training.trainer - INFO - Starting training...
2025-08-27 11:37:10,785 - src.training.trainer - INFO - Training samples: 100
2025-08-27 11:37:10,785 - src.training.trainer - INFO - Validation samples: 100
2025-08-27 11:37:11,485 - src.training.trainer - INFO - Trainer initialized on device: cpu
2025-08-27 11:37:11,485 - src.training.trainer - INFO - Distributed training: False, World size: 1
2025-08-27 11:37:11,485 - src.training.trainer - INFO - Starting training...
2025-08-27 11:38:22,302 - src.training.trainer - INFO - Training samples: 100
2025-08-27 11:38:22,302 - src.training.trainer - INFO - Validation samples: 100
2025-08-27 11:38:22,917 - src.training.trainer - INFO - Trainer initialized on device: cpu
2025-08-27 11:38:22,917 - src.training.trainer - INFO - Distributed training: False, World size: 1
2025-08-27 11:38:22,917 - src.training.trainer - INFO - Starting training...
2025-08-27 11:39:45,918 - src.training.trainer - INFO - Training samples: 100
2025-08-27 11:39:45,918 - src.training.trainer - INFO - Validation samples: 100
2025-08-27 11:39:46,529 - src.training.trainer - INFO - Trainer initialized on device: cpu
2025-08-27 11:39:46,529 - src.training.trainer - INFO - Distributed training: False, World size: 1
2025-08-27 11:39:46,529 - src.training.trainer - INFO - Starting training...
2025-08-27 11:41:26,645 - src.training.trainer - INFO - Training samples: 100
2025-08-27 11:41:26,645 - src.training.trainer - INFO - Validation samples: 100
2025-08-27 11:41:27,260 - src.training.trainer - INFO - Trainer initialized on device: cpu
2025-08-27 11:41:27,260 - src.training.trainer - INFO - Distributed training: False, World size: 1
2025-08-27 11:41:27,260 - src.training.trainer - INFO - Starting training...
2025-08-27 11:47:51,544 - src.training.trainer - INFO - Training samples: 100
2025-08-27 11:47:51,544 - src.training.trainer - INFO - Validation samples: 100
2025-08-27 11:47:52,236 - src.training.trainer - INFO - Trainer initialized on device: cpu
2025-08-27 11:47:52,236 - src.training.trainer - INFO - Distributed training: False, World size: 1
2025-08-27 11:47:52,236 - src.training.trainer - INFO - Starting training...
2025-08-27 11:49:16,905 - src.training.trainer - INFO - Training samples: 100
2025-08-27 11:49:16,905 - src.training.trainer - INFO - Validation samples: 100
2025-08-27 11:49:17,588 - src.training.trainer - INFO - Trainer initialized on device: cpu
2025-08-27 11:49:17,588 - src.training.trainer - INFO - Distributed training: False, World size: 1
2025-08-27 11:49:17,588 - src.training.trainer - INFO - Starting training...
2025-08-27 11:51:11,239 - src.training.trainer - INFO - Training samples: 100
2025-08-27 11:51:11,240 - src.training.trainer - INFO - Validation samples: 100
2025-08-27 11:51:12,040 - src.training.trainer - INFO - Trainer initialized on device: cpu
2025-08-27 11:51:12,040 - src.training.trainer - INFO - Distributed training: False, World size: 1
2025-08-27 11:51:12,040 - src.training.trainer - INFO - Starting training...
2025-08-27 11:51:18,019 - src.training.trainer - INFO - Epoch 0, Batch 0/6, Loss: 0.6931, LR: 0.001000
2025-08-27 11:51:24,059 - src.training.trainer - INFO - Epoch 0 - train_loss: 0.6931 - val_accuracy: 0.4583 - val_precision: 0.0000 - val_recall: 0.0000 - val_mcc: 0.0000 - val_loss: 0.6931
2025-08-27 11:53:18,196 - src.training.trainer - INFO - Training samples: 100
2025-08-27 11:53:18,196 - src.training.trainer - INFO - Validation samples: 100
2025-08-27 11:53:19,019 - src.training.trainer - INFO - Trainer initialized on device: cpu
2025-08-27 11:53:19,019 - src.training.trainer - INFO - Distributed training: False, World size: 1
2025-08-27 11:53:19,019 - src.training.trainer - INFO - Starting training...
2025-08-27 11:53:26,503 - src.training.trainer - INFO - Epoch 0, Batch 0/6, Loss: 0.6931, LR: 0.001000
2025-08-27 11:53:32,405 - src.training.trainer - INFO - Epoch 0 - train_loss: 0.6931 - val_accuracy: 0.5833 - val_precision: 0.0000 - val_recall: 0.0000 - val_mcc: 0.0000 - val_loss: 0.6931
2025-08-27 11:53:32,418 - src.training.trainer - INFO - Checkpoint saved: checkpoints/checkpoint_epoch_0.pth
2025-08-27 11:53:32,426 - src.training.trainer - INFO - Best model saved: checkpoints/best_model.pth
2025-08-27 11:53:32,436 - src.training.trainer - INFO - Epoch 1, Batch 0/6, Loss: 0.6931, LR: 0.001000
2025-08-27 11:53:32,498 - src.training.trainer - INFO - Epoch 1 - train_loss: 0.6931 - val_accuracy: 0.5833 - val_precision: 0.0000 - val_recall: 0.0000 - val_mcc: 0.0000 - val_loss: 0.6931
2025-08-27 11:53:32,498 - src.training.trainer - INFO - Training completed!
2025-08-27 11:59:47,964 - src.training.trainer - INFO - Training samples: 100
2025-08-27 11:59:47,964 - src.training.trainer - INFO - Validation samples: 100
2025-08-27 11:59:48,644 - src.training.trainer - INFO - Trainer initialized on device: cpu
2025-08-27 11:59:48,644 - src.training.trainer - INFO - Distributed training: False, World size: 1
2025-08-27 11:59:48,644 - src.training.trainer - INFO - Starting training...
2025-08-27 11:59:54,213 - src.training.trainer - INFO - Epoch 0, Batch 0/6, Loss: 0.6931, LR: 0.001000
2025-08-27 12:00:00,033 - src.training.trainer - INFO - Epoch 0 - train_loss: 0.6931 - val_accuracy: 0.5625 - val_precision: 0.0000 - val_recall: 0.0000 - val_mcc: 0.0000 - val_loss: 0.6931
2025-08-27 12:00:00,044 - src.training.trainer - INFO - Checkpoint saved: checkpoints/checkpoint_epoch_0.pth
2025-08-27 12:00:00,049 - src.training.trainer - INFO - Best model saved: checkpoints/best_model.pth
2025-08-27 12:00:00,064 - src.training.trainer - INFO - Epoch 1, Batch 0/6, Loss: 0.6931, LR: 0.001000
2025-08-27 12:00:00,139 - src.training.trainer - INFO - Epoch 1 - train_loss: 0.6931 - val_accuracy: 0.5625 - val_precision: 0.0000 - val_recall: 0.0000 - val_mcc: 0.0000 - val_loss: 0.6931
2025-08-27 12:00:00,139 - src.training.trainer - INFO - Training completed!
2025-08-28 16:43:49,255 - src.training.trainer - INFO - Training samples: 1011
2025-08-28 16:43:49,255 - src.training.trainer - INFO - Validation samples: 16
2025-08-28 16:43:49,881 - src.training.trainer - INFO - Trainer initialized on device: cuda
2025-08-28 16:43:49,881 - src.training.trainer - INFO - Distributed training: False, World size: 1
2025-08-28 16:43:49,881 - src.training.trainer - INFO - Starting training...
2025-08-29 14:57:16,773 - src.training.trainer - INFO - Training samples: 1011
2025-08-29 14:57:16,773 - src.training.trainer - INFO - Validation samples: 16
2025-08-29 14:57:17,290 - src.training.trainer - INFO - Trainer initialized on device: cuda
2025-08-29 14:57:17,290 - src.training.trainer - INFO - Distributed training: False, World size: 1
2025-08-29 14:57:17,290 - src.training.trainer - INFO - Starting training...
2025-09-06 19:14:51,755 - src.training.trainer - INFO - Training samples: 1011
2025-09-06 19:14:51,755 - src.training.trainer - INFO - Validation samples: 16
2025-09-06 19:14:52,099 - src.training.trainer - INFO - Trainer initialized on device: cuda
2025-09-06 19:14:52,099 - src.training.trainer - INFO - Distributed training: False, World size: 1
2025-09-06 19:14:52,099 - src.training.trainer - INFO - Starting training...
2025-09-08 17:43:12,212 - src.training.trainer - INFO - Training samples: 1011
2025-09-08 17:43:12,213 - src.training.trainer - INFO - Validation samples: 16
2025-09-08 17:43:12,547 - src.training.trainer - INFO - Trainer initialized on device: cuda
2025-09-08 17:43:12,547 - src.training.trainer - INFO - Distributed training: False, World size: 1
2025-09-08 17:43:12,547 - src.training.trainer - INFO - Starting training...
2025-09-08 21:09:51,074 - src.training.trainer - INFO - Training samples: 126
2025-09-08 21:09:51,074 - src.training.trainer - INFO - Validation samples: 23
2025-09-08 21:09:51,723 - src.training.trainer - INFO - Trainer initialized on device: cuda
2025-09-08 21:09:51,723 - src.training.trainer - INFO - Distributed training: False, World size: 1
2025-09-08 21:09:51,723 - src.training.trainer - INFO - Starting training...
2025-09-08 21:22:30,589 - src.training.trainer - INFO - Training samples: 126
2025-09-08 21:22:30,590 - src.training.trainer - INFO - Validation samples: 27
2025-09-08 21:22:30,947 - src.training.trainer - INFO - Trainer initialized on device: cuda
2025-09-08 21:22:30,947 - src.training.trainer - INFO - Distributed training: False, World size: 1
2025-09-08 21:22:30,947 - src.training.trainer - INFO - Starting training...
2025-09-09 15:42:17,120 - src.training.trainer - INFO - Training samples: 126
2025-09-09 15:42:17,121 - src.training.trainer - INFO - Validation samples: 27
2025-09-09 15:42:17,593 - src.training.trainer - INFO - Trainer initialized on device: cuda
2025-09-09 15:42:17,593 - src.training.trainer - INFO - Distributed training: False, World size: 1
2025-09-09 15:42:17,593 - src.training.trainer - INFO - Starting training...
