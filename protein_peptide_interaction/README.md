# Protein-Peptide Interaction Prediction Model

基于RAPiDock编码器的蛋白质-多肽相互作用预测模型，替换扩散模块为传统的交互预测网络。

## 项目结构

```
protein_peptide_interaction/
├── README.md                    # 项目说明
├── requirements.txt             # 依赖包
├── setup.py                     # 安装脚本
├── configs/                     # 配置文件
│   ├── default.yaml            # 默认配置
│   ├── rapidock_config.yaml    # RAPiDock集成配置
│   ├── encoders/               # 编码器配置
│   │   ├── protein_rapidock.yaml
│   │   ├── protein_alphafold2.yaml
│   │   ├── peptide_rapidock.yaml
│   │   ├── peptide_atomref.yaml
│   │   └── peptide_dmasif.yaml
│   ├── models/                 # 模型配置
│   │   ├── gin.yaml
│   │   ├── quinnet.yaml
│   │   ├── rapidock_diffusion.yaml
│   │   └── rapidock_interaction.yaml
│   └── training/               # 训练配置
│       ├── single_gpu.yaml
│       └── multi_gpu.yaml
├── src/                        # 源代码
│   ├── __init__.py
│   ├── config/                 # 配置管理
│   │   ├── __init__.py
│   │   ├── config_manager.py
│   │   └── rapidock_config_manager.py
│   ├── data/                   # 数据处理
│   │   ├── __init__.py
│   │   ├── dataset.py
│   │   ├── dataloader.py
│   │   └── rapidock_data_processor.py
│   ├── encoders/               # 编码器模块
│   │   ├── __init__.py
│   │   ├── protein/
│   │   │   ├── __init__.py
│   │   │   ├── rapidock_encoder.py
│   │   │   ├── rapidock_feature_extractor.py
│   │   │   └── alphafold2_encoder.py
│   │   └── peptide/
│   │       ├── __init__.py
│   │       ├── rapidock_encoder.py
│   │       ├── rapidock_feature_extractor.py
│   │       ├── atomref_encoder.py
│   │       ├── dmasif_encoder.py
│   │       └── vqvae_encoder.py
│   ├── models/                 # 交互模型
│   │   ├── __init__.py
│   │   ├── base_model.py
│   │   ├── gin_model.py
│   │   ├── quinnet_model.py
│   │   ├── rapidock_diffusion_model.py
│   │   └── rapidock_interaction_model.py
│   ├── training/               # 训练框架
│   │   ├── __init__.py
│   │   ├── trainer.py
│   │   ├── rapidock_trainer.py
│   │   ├── loss.py
│   │   └── metrics.py
│   ├── inference/              # 推理模块
│   │   ├── __init__.py
│   │   └── rapidock_inference.py
│   └── utils/                  # 工具函数
│       ├── __init__.py
│       ├── gpu_utils.py
│       └── memory_utils.py
├── scripts/                    # 脚本文件
│   ├── train.py               # 训练脚本
│   ├── evaluate.py            # 评估脚本
│   └── inference.py           # 推理脚本
├── tests/                      # 测试文件
│   ├── __init__.py
│   ├── test_encoders.py
│   ├── test_models.py
│   └── test_training.py
├── docs/                       # 文档
│   ├── api.md
│   ├── training_guide.md
│   └── model_architecture.md
└── deployment/                 # 部署相关
    ├── remote_setup.txt       # 远程环境设置
    └── conda_env.yaml         # Conda环境配置
```

## 核心特性

1. **灵活的编码器系统**
   - 蛋白质编码器：RAPiDock原生、RAPiDock特征提取器、AlphaFold2预训练
   - 多肽编码器：RAPiDock、RAPiDock特征提取器、AtomRef、dMaSIF、可选VQVAE降维
   - 支持原子级别和氨基酸级别编码

2. **可配置的交互模型**
   - GIN (Graph Isomorphism Network)
   - QuinNet (Quantum-inspired Network)
   - RAPiDock扩散模型 (Diffusion-based)
   - RAPiDock交互模型 (Interaction-based)
   - 通过配置文件切换架构

3. **高效的训练框架**
   - 多GPU训练支持
   - 显存优化
   - 单卡训练兼容
   - RAPiDock专用训练器

4. **统一的数据集接口**
   - 集成test_alphafold2.py和vqvae_ver2_0819.py数据集
   - RAPiDock数据处理管道
   - 标准化数据加载流程

5. **RAPiDock集成功能**
   - 扩散模型推理
   - 几何特征提取
   - 多构象生成
   - 置信度预测

## 快速开始

### 1. 环境配置

```bash
# 创建并激活Conda环境
conda create -n ppi_prediction python=3.8
conda activate ppi_prediction

# 安装依赖
pip install -r requirements.txt
```

### 2. 路径配置

**这是运行项目前最关键的一步。**

项目中所有的本地路径（包括外部代码库和数据集）都通过一个独立的文件 `configs/local_paths.yaml` 进行管理。这样做可以将您的本地环境配置与项目本身的超参数配置完全分离。

**操作步骤:**

1.  在 `configs/` 目录下，找到 `local_paths.yaml` 文件。
2.  **必须**用您本地的**绝对路径**替换文件中的所有占位符 (`/path/to/your/...`)。

`configs/local_paths.yaml` 文件内容示例：
```yaml
# ===============================================================
#  请将以下所有占位符路径修改为您本地环境的【绝对路径】
# ===============================================================

# 外部依赖的代码库路径
external_modules:
  rapidock: "/path/to/your/RAPiDock"
  customer_rice: "/path/to/your/customer_rice"
  quinnet: "/path/to/your/QuinNet"
  dmasif: "/path/to/your/dMaSIF"

# VQVAE数据集相关文件的路径
data:
  data_paths:
    pdb_dir: "/path/to/your/pdb_files/"
    positive_pairs: "customer_rice/PepPI dataset/Arabidopsis thaliana/PepPIs ind.txt"
    negative_pairs: "customer_rice/PepPI dataset/Arabidopsis thaliana/non-PepPIs ind.txt"
    protein_sequences: "customer_rice/PepPI dataset/Arabidopsis thaliana/protein sequences.fasta"
    peptide_sequences: "customer_rice/PepPI dataset/Arabidopsis thaliana/peptide sequences.fasta"
```

### 3. 训练模型

配置好路径后，您可以运行训练脚本。脚本会自动加载 `local_paths.yaml` 和您指定的模型配置文件。

```bash
# 使用RAPiDock配置进行训练
# 程序会自动合并 configs/rapidock_config.yaml 和 configs/local_paths.yaml
python scripts/train.py --config configs/rapidock_config.yaml

# 如需使用功能更强大的训练脚本（带断点续训、更详细日志等）
python scripts/train_enhanced.py --config configs/rapidock_config.yaml
```

### 4. 评估模型

```bash
python scripts/evaluate.py --model_path checkpoints/best_model.pth
```

## 配置说明

本项目的配置系统分为两部分：

1.  **本地环境配置 (`configs/local_paths.yaml`)**:
    *   **作用**: 存放所有与您本地机器相关的绝对路径，例如外部代码库、数据集、PDB文件等。
    *   **原则**: 这个文件**不应该**被提交到版本控制中。每个用户根据自己的环境来修改。

2.  **实验参数配置 (`configs/*.yaml`)**:
    *   **作用**: 存放模型的超参数、训练设置、使用的编码器和模型架构等。
    *   **原则**: 这些文件定义了实验本身，可以被团队共享和版本控制。

在运行时，程序会自动合并这两个配置文件，从而获得完整的运行配置。

## 相对路径引用

项目使用相对路径引用外部模块：

```python
# 引用RAPiDock模块
sys.path.append('../RAPiDock')
from modules.protein_encoder import ProteinEncoder

# 引用customer_rice模块
sys.path.append('../customer_rice')
from vqvae_ver2_0819 import VQVAE

# 引用QuinNet模块
sys.path.append('../QuinNet')
from models.quinnet import QuinNet

# 引用dMaSIF模块
sys.path.append('../dMaSIF')
from models.dmasif_encoder import dMaSIFEncoder
```

## 远程环境部署

目标服务器：hongweihao@10.8.21.161
Conda环境：customer_rice_gnn

详细部署说明见 `deployment/remote_setup.txt`

## 开发指南

1. 所有新功能需要编写对应的测试
2. 遵循PEP 8代码规范
3. 添加详细的文档字符串
4. 使用类型提示

## 许可证

本项目基于MIT许可证开源。
