#!/usr/bin/env python3
"""
Dataset module for protein-peptide interaction prediction.

Integrates datasets from test_alphafold2.py and vqvae_ver2_0819.py
with unified data loading and preprocessing.
"""

import os
import sys
import torch
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Union, Any
from torch.utils.data import Dataset as TorchDataset
import logging
from pathlib import Path
import pickle
import json
from Bio.PDB import PDBParser

# Add external module paths
current_dir = Path(__file__).parent.parent.parent.parent
sys.path.append(str(current_dir / "customer_rice"))

logger = logging.getLogger(__name__)

class ProteinPeptideDataset(TorchDataset):
    """
    Unified dataset for protein-peptide interaction prediction.
    
    Integrates data from multiple sources:
    - test_alphafold2.py dataset
    - vqvae_ver2_0819.py dataset
    """
    
    def __init__(self, 
                 config: Dict[str, Any],
                 split: str = "train",
                 transform: Optional[callable] = None):
        """
        Initialize the dataset. 
        
        Args:
            config: Dataset configuration from the main config file (e.g., data section)
            split: Data split ("train", "val", "test")
            transform: Optional data transformation function
        """
        self.config = config
        self.split = split
        self.transform = transform
        
        # DEBUG: Check the received config
        logger.debug(f"[dataset.py] Initializing with config: {self.config}")
        if 'datasets' in self.config:
            logger.debug(f"[dataset.py] Type of self.config['datasets']: {type(self.config['datasets'])}")
            logger.debug(f"[dataset.py] Content of self.config['datasets']: {self.config['datasets']}")
        else:
            logger.debug("[dataset.py] 'datasets' key not found in received config.")

        # Initialize data containers
        self.data = []
        self.labels = []
        self.metadata = []
        
        # Load datasets
        self._load_datasets()
        
        logger.info(f"Loaded {len(self.data)} samples for {self.split} split")
    
    def _load_datasets(self):
        """Load and integrate datasets from different sources."""
        logger.info(f"Starting to load datasets for split: '{self.split}'")
        dataset_config = self.config.get("datasets", [])
        dataset_config = list(dataset_config)
        if not isinstance(dataset_config, list):
            logger.warning("'datasets' config is not a list. Skipping dataset loading.")
            logger.warning(f"'datasets' config {dataset_config}")
            logger.warning(f"'datasets' config {type(dataset_config)}")
            
            return

        dataset_names = [ds.get("name") for ds in dataset_config]

        # Load AlphaFold2 dataset
        if "alphafold2_dataset" in dataset_names:
            self._load_alphafold2_dataset()
        
        # Load VQVAE dataset
        if "vqvae_dataset" in dataset_names:
            self._load_vqvae_dataset()
        
        # Apply data splitting
        self._apply_data_split()
        
        # Apply preprocessing
        self._apply_preprocessing()

        # If no data was loaded, raise an error.
        if not self.data:
            raise ValueError("No data could be loaded from the specified sources. Please check data paths and file integrity.")
    
    def _load_alphafold2_dataset(self):
        """Loads data from AlphaFold2-related sources and pre-computed embeddings."""
        logger.info("Attempting to load AlphaFold2 dataset with pre-computed embeddings...")
        
        embedding_base_path = Path("/Users/<USER>/Documents/GitHub/colab/single_represent/")
        if not embedding_base_path.exists():
            logger.error(f"Embedding path does not exist: {embedding_base_path}")
            return

        def _load_sequences_from_fasta(file_path):
            if not os.path.exists(file_path):
                logger.warning(f"FASTA file not found: {file_path}")
                return {}
            seq_dict = {}
            with open(file_path, 'r') as rf:
                name, seq = '', ''
                for line in rf:
                    line = line.strip()
                    if line.startswith('>'):
                        if name:
                            seq_dict[name] = seq
                        name = line[1:]
                        seq = ''
                    else:
                        seq += line.upper()
                if name:
                    seq_dict[name] = seq
            return seq_dict

        try:
            base_path = Path(__file__).parent.parent.parent.parent
            dataset_name = self.config.get("alphafold2_dataset_name", "DeepPeppi")
            customer_rice_path = base_path / "customer_rice"
            logger.debug(f"AlphaFold2 dataset name: {dataset_name}")

            if dataset_name == "DeepPeppi":
                positive_csv = customer_rice_path / "PepPI dataset/Arabidopsis thaliana/PepPIs ind.txt"
                negative_csv = customer_rice_path / "PepPI dataset/Arabidopsis thaliana/non-PepPIs ind.txt"
                peptide_seq_file = customer_rice_path / "PepPI dataset/Arabidopsis thaliana/peptide sequences.fasta"
                protein_seq_file = customer_rice_path / "PepPI dataset/Arabidopsis thaliana/protein sequences.fasta"
                pos_sep, neg_sep = '\t', '\t'
            else:
                logger.warning(f"Configuration for AlphaFold2 dataset '{dataset_name}' is not fully defined.")
                return

            protein_sequences = _load_sequences_from_fasta(protein_seq_file)
            peptide_sequences = _load_sequences_from_fasta(peptide_seq_file)

            positive_pairs = pd.read_csv(positive_csv, sep=pos_sep, header=None, engine='python')
            negative_pairs = pd.read_csv(negative_csv, sep=neg_sep, header=None, engine='python')

            positive_pairs['label'] = 1
            negative_pairs['label'] = 0
            all_pairs = pd.concat([positive_pairs, negative_pairs], ignore_index=True)

            initial_count = len(self.data)
            for _, row in all_pairs.iterrows():
                peptide_id, protein_id, label = str(row[0]), str(row[1]), row['label']
                peptide_seq, protein_seq = peptide_sequences.get(peptide_id), protein_sequences.get(protein_id)

                if not (peptide_seq and protein_seq):
                    continue

                # Load pre-computed embedding
                embedding_path1 = embedding_base_path / f"{peptide_id}.{protein_id}.npy"
                embedding_path2 = embedding_base_path / f"{protein_id}.{peptide_id}.npy"
                
                embedding = None
                if embedding_path1.exists():
                    embedding = torch.from_numpy(np.load(embedding_path1))
                elif embedding_path2.exists():
                    embedding = torch.from_numpy(np.load(embedding_path2))
                else:
                    logger.warning(f"Embedding not found for pair: peptide='{peptide_id}', protein='{protein_id}'. Searched for {embedding_path1} and {embedding_path2}. Skipping.")
                    continue

                sample = {
                    "peptide_id": peptide_id, "protein_id": protein_id,
                    "peptide_sequence": peptide_seq, "protein_sequence": protein_seq,
                    "interaction_label": label,
                    "embedding": embedding
                }
                processed_sample = self._process_alphafold2_sample(sample)
                if processed_sample is not None:
                    self.data.append(processed_sample)
                    self.labels.append(label)
                    self.metadata.append({"source": "alphafold2", "protein_id": protein_id, "peptide_id": peptide_id})

            logger.info(f"Loaded {len(self.data) - initial_count} samples from AlphaFold2 source with embeddings.")

        except FileNotFoundError as e:
            logger.error(f"Could not load AlphaFold2 dataset. File not found: {e}")
        except Exception as e:
            logger.error(f"Error loading AlphaFold2 dataset: {e}", exc_info=True)
    
    def _load_vqvae_dataset(self):
        """Loads data and extracts coordinates from PDB files for VQVAE-related sources."""
        logger.info("Attempting to load VQVAE dataset with coordinate extraction...")

        def _extract_ca_coordinates(pdb_path: str, pdb_id: str) -> Optional[np.ndarray]:
            logger.debug(f"Parsing PDB file for '{pdb_id}' at: {pdb_path}")
            if not os.path.exists(pdb_path):
                logger.warning(f"PDB file not found for '{pdb_id}': {pdb_path}")
                return None
            try:
                parser = PDBParser(QUIET=True)
                structure = parser.get_structure(pdb_id, pdb_path)
                coords = [residue["CA"].get_coord() for model in structure for chain in model for residue in chain if "CA" in residue]
                if not coords:
                    logger.warning(f"No CA atoms found in PDB for '{pdb_id}'.")
                    return None
                logger.debug(f"Successfully extracted {len(coords)} CA coordinates for '{pdb_id}'.")
                return np.array(coords)
            except Exception as e:
                logger.error(f"Could not parse PDB file for '{pdb_id}': {e}")
                return None

        def _load_sequences_from_fasta(file_path):
            if not os.path.exists(file_path):
                logger.warning(f"FASTA file not found: {file_path}")
                return {}
            seq_dict = {}
            with open(file_path, 'r') as rf:
                name, seq = '', ''
                for line in rf:
                    line = line.strip()
                    if line.startswith('>'):
                        if name:
                            seq_dict[name] = seq
                        name = line[1:]
                        seq = ''
                    else:
                        seq += line.upper()
                if name:
                    seq_dict[name] = seq
            return seq_dict

        try:
            base_path = Path(__file__).parent.parent.parent.parent
            dataset_name = self.config.get("vqvae_dataset_name", "DeepPeppi")
            customer_rice_path = base_path / "customer_rice"
            logger.debug(f"VQVAE dataset name: {dataset_name}")

            if dataset_name == "DeepPeppi":
                PDB_DIR = "/home/<USER>/code/MAPE-PPI/data/raw_data/all_pdbs"
                positive_file = customer_rice_path / "PepPI dataset/Arabidopsis thaliana/PepPIs ind.txt"
                negative_file = customer_rice_path / "PepPI dataset/Arabidopsis thaliana/non-PepPIs ind.txt"
                protein_seq_file = customer_rice_path / "PepPI dataset/Arabidopsis thaliana/protein sequences.fasta"
                peptide_seq_file = customer_rice_path / "PepPI dataset/Arabidopsis thaliana/peptide sequences.fasta"
                pos_sep, neg_sep = '\t', '\t'
            else:
                logger.warning(f"Path configuration for VQVAE dataset '{dataset_name}' is not defined.")
                return
            
            logger.info(f"PDB Directory for VQVAE: {PDB_DIR}")

            protein_sequences = _load_sequences_from_fasta(protein_seq_file)
            peptide_sequences = _load_sequences_from_fasta(peptide_seq_file)

            positive_pairs = pd.read_csv(positive_file, sep=pos_sep, header=None, engine='python')
            negative_pairs = pd.read_csv(negative_file, sep=neg_sep, header=None, engine='python')

            positive_pairs['label'] = 1
            negative_pairs['label'] = 0
            all_pairs = pd.concat([positive_pairs, negative_pairs], ignore_index=True)

            initial_count = len(self.data)
            for _, row in all_pairs.iterrows():
                peptide_id, protein_id, label = str(row[0]), str(row[1]), row['label']
                logger.debug(f"Processing pair: protein='{protein_id}', peptide='{peptide_id}'")

                peptide_seq, protein_seq = peptide_sequences.get(peptide_id), protein_sequences.get(protein_id)
                
                if not (peptide_seq and protein_seq):
                    logger.warning(f"Sequence not found for pair: protein='{protein_id}', peptide='{peptide_id}'. Skipping.")
                    continue

                protein_pdb_path = os.path.join(PDB_DIR, f"{protein_id}.pdb")
                protein_coords = _extract_ca_coordinates(protein_pdb_path, protein_id)

                peptide_pdb_path = os.path.join(PDB_DIR, f"{peptide_id}.pdb")
                peptide_coords = _extract_ca_coordinates(peptide_pdb_path, peptide_id)

                if protein_coords is None or peptide_coords is None:
                    logger.warning(f"Skipping pair due to missing coordinates: protein='{protein_id}', peptide='{peptide_id}'.")
                    continue

                sample = {
                    "peptide_id": peptide_id, "protein_id": protein_id,
                    "peptide_sequence": peptide_seq, "protein_sequence": protein_seq,
                    "peptide_pdb": peptide_pdb_path, "protein_pdb": protein_pdb_path,
                    "interaction_score": float(label),
                    "protein_coords": protein_coords, "peptide_coords": peptide_coords
                }
                processed_sample = self._process_vqvae_sample(sample)
                if processed_sample is not None:
                    self.data.append(processed_sample)
                    self.labels.append(label)
                    self.metadata.append({"source": "vqvae", "protein_id": protein_id, "peptide_id": peptide_id})
            
            logger.info(f"Loaded {len(self.data) - initial_count} samples from VQVAE source with coordinates.")

        except FileNotFoundError as e:
            logger.error(f"Could not load VQVAE dataset. File not found: {e}")
        except Exception as e:
            logger.error(f"An unexpected error occurred while loading VQVAE dataset: {e}", exc_info=True)
    
    def _process_alphafold2_sample(self, sample: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Process a sample from the AlphaFold2 dataset."""
        # This function now also passes the pre-computed embedding
        return {
            "protein": {
                "sequence": sample.get("protein_sequence"),
                "embedding": sample.get("embedding") # Pass the loaded embedding
            },
            "peptide": {
                "sequence": sample.get("peptide_sequence"),
                "embedding": sample.get("embedding") # Pass the loaded embedding
            },
            "interaction": {
                "interaction_label": sample.get("interaction_label")
            }
        }
    
    def _process_vqvae_sample(self, sample: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Process a sample from the VQVAE dataset."""
        return {
            "protein": {
                "sequence": sample.get("protein_sequence"),
                "coordinates": sample.get("protein_coords")
            },
            "peptide": {
                "sequence": sample.get("peptide_sequence"),
                "coordinates": sample.get("peptide_coords")
            },
            "interaction": {
                "interaction_score": sample.get("interaction_score")
            }
        }
    
    def _apply_data_split(self):
        """Apply train/val/test split to the data."""
        if not self.data:
            return
        
        split_config = self.config.get("splits", {})
        train_ratio, val_ratio = split_config.get("train", 0.8), split_config.get("val", 0.1)
        np.random.seed(self.config.get("random_seed", 42))
        
        indices = np.random.permutation(len(self.data))
        train_end = int(len(self.data) * train_ratio)
        val_end = train_end + int(len(self.data) * val_ratio)
        
        if self.split == "train":
            selected_indices = indices[:train_end]
        elif self.split == "val":
            selected_indices = indices[train_end:val_end]
        else: # test
            selected_indices = indices[val_end:]
        
        self.data = [self.data[i] for i in selected_indices]
        self.labels = [self.labels[i] for i in selected_indices]
        self.metadata = [self.metadata[i] for i in selected_indices]
    
    def _apply_preprocessing(self):
        """Apply preprocessing like length filtering and deduplication."""
        if not self.data:
            return
        
        # Length filtering
        max_prot_len = self.config.get("max_protein_length", 1000)
        max_pep_len = self.config.get("max_peptide_length", 50)
        
        original_count = len(self.data)
        filtered_indices = [
            i for i, sample in enumerate(self.data)
            if len(sample["protein"]["sequence"]) <= max_prot_len and len(sample["peptide"]["sequence"]) <= max_pep_len
        ]
        
        self.data = [self.data[i] for i in filtered_indices]
        self.labels = [self.labels[i] for i in filtered_indices]
        self.metadata = [self.metadata[i] for i in filtered_indices]
        logger.info(f"After length filtering: {len(self.data)} samples remaining (removed {original_count - len(self.data)}). ")

        # Deduplication
        if self.config.get("remove_duplicates", True):
            self._remove_duplicates()
    
    def _remove_duplicates(self):
        """Remove duplicate samples based on protein and peptide sequences."""
        seen = set()
        unique_indices = []
        for i, sample in enumerate(self.data):
            key = (sample["protein"]["sequence"], sample["peptide"]["sequence"])
            if key not in seen:
                seen.add(key)
                unique_indices.append(i)
        
        original_count = len(self.data)
        self.data = [self.data[i] for i in unique_indices]
        self.labels = [self.labels[i] for i in unique_indices]
        self.metadata = [self.metadata[i] for i in unique_indices]
        logger.info(f"After deduplication: {len(self.data)} samples remaining (removed {original_count - len(self.data)}). ")

    def __len__(self) -> int:
        return len(self.data)
    
    def __getitem__(self, idx: int) -> Dict[str, Any]:
        if idx >= len(self.data):
            raise IndexError(f"Index {idx} out of range for dataset of size {len(self.data)}")
        
        sample_data = self.data[idx]
        label = self.labels[idx]
        metadata = self.metadata[idx]

        # Prepare the output for the trainer/model
        # This structure matches what the trainer expects
        return {
            "protein": sample_data.get("protein", {}),
            "peptide": sample_data.get("peptide", {}),
            "label": label,
            "metadata": metadata,
            "index": idx
        }

# Factory function
def create_dataset(config: Dict[str, Any], 
                  split: str = "train",
                  transform: Optional[callable] = None) -> ProteinPeptideDataset:
    return ProteinPeptideDataset(config, split, transform)
