import numpy as np
from sklearn.metrics import f1_score as sk_f1_score, roc_auc_score, average_precision_score

def f1_score(y_true, y_pred):
    y_true = np.asarray(y_true)
    y_pred = np.asarray(y_pred)
    # 二分类概率转为标签
    if y_pred.ndim > 1 and y_pred.shape[1] > 1:
        y_pred = np.argmax(y_pred, axis=1)
    else:
        y_pred = (y_pred > 0.5).astype(int)
    return sk_f1_score(y_true, y_pred)

def auc_roc(y_true, y_pred):
    y_true = np.asarray(y_true)
    y_pred = np.asarray(y_pred)
    # 只支持二分类概率
    if y_pred.ndim > 1 and y_pred.shape[1] > 1:
        y_pred = y_pred[:, 1]
    return roc_auc_score(y_true, y_pred)

def auc_pr(y_true, y_pred):
    y_true = np.asarray(y_true)
    y_pred = np.asarray(y_pred)
    # 只支持二分类概率
    if y_pred.ndim > 1 and y_pred.shape[1] > 1:
        y_pred = y_pred[:, 1]
    return average_precision_score(y_true, y_pred)
#!/usr/bin/env python3
"""
Metrics Calculator for Protein-Peptide Interaction Prediction

Implements various evaluation metrics for interaction prediction tasks
including classification metrics, ranking metrics, and specialized metrics.
"""

import torch
import numpy as np
from typing import Dict, List, Tuple, Optional, Any, Union
from sklearn.metrics import (
    accuracy_score, precision_score, recall_score, f1_score,
    roc_auc_score, average_precision_score, matthews_corrcoef,
    confusion_matrix, classification_report
)
import logging

logger = logging.getLogger(__name__)

class MetricsCalculator:
    """
    Comprehensive metrics calculator for protein-peptide interaction prediction.
    
    Supports various metrics:
    - Classification metrics (accuracy, precision, recall, F1, AUC, AP)
    - Ranking metrics (NDCG, MRR)
    - Regression metrics (MSE, MAE, R²)
    - Specialized metrics (MCC, specificity, sensitivity)
    """
    
    def __init__(self, metric_names: List[str]):
        """
        Initialize the metrics calculator.
        
        Args:
            metric_names: List of metric names to calculate
        """
        self.metric_names = metric_names
        self.supported_metrics = {
            "accuracy", "precision", "recall", "f1", "auc", "ap", "mcc",
            "specificity", "sensitivity", "mse", "mae", "r2", "ndcg", "mrr","f1_score","auc_roc","auc_pr"
        }
        
        # Validate metric names
        for metric in metric_names:
            if metric not in self.supported_metrics:
                logger.warning(f"Unsupported metric: {metric}")
        
        logger.info(f"Initialized metrics calculator with metrics: {metric_names}")
    
    def calculate_metrics(self, 
                         predictions: torch.Tensor, 
                         targets: torch.Tensor,
                         threshold: float = 0.5) -> Dict[str, float]:
        """
        Calculate all specified metrics.
        
        Args:
            predictions: Model predictions
            targets: Ground truth targets
            threshold: Classification threshold for binary predictions
            
        Returns:
            Dictionary of metric values
        """
        metrics = {}
        
        # Convert to numpy arrays
        if isinstance(predictions, torch.Tensor):
            predictions = predictions.detach().cpu().numpy()
        if isinstance(targets, torch.Tensor):
            targets = targets.detach().cpu().numpy()
        
        # Ensure predictions are probabilities for classification metrics
        if predictions.ndim > 1 and predictions.shape[1] > 1:
            # Multi-class predictions
            pred_probs = predictions
            pred_classes = np.argmax(predictions, axis=1)
        else:
            # Binary predictions
            if predictions.ndim > 1:
                predictions = predictions.squeeze()
            
            # Apply sigmoid if predictions are logits
            if np.any(predictions < 0) or np.any(predictions > 1):
                pred_probs = 1 / (1 + np.exp(-predictions))  # Sigmoid
            else:
                pred_probs = predictions
            
            pred_classes = (pred_probs >= threshold).astype(int)
        
        # Calculate classification metrics
        if "accuracy" in self.metric_names:
            metrics["accuracy"] = self._calculate_accuracy(pred_classes, targets)
        
        if "precision" in self.metric_names:
            metrics["precision"] = self._calculate_precision(pred_classes, targets)
        
        if "recall" in self.metric_names:
            metrics["recall"] = self._calculate_recall(pred_classes, targets)
        
        if "f1" in self.metric_names:
            metrics["f1"] = self._calculate_f1(pred_classes, targets)
        
        if "auc" in self.metric_names:
            metrics["auc"] = self._calculate_auc(pred_probs, targets)
        
        if "ap" in self.metric_names:
            metrics["ap"] = self._calculate_average_precision(pred_probs, targets)
        
        if "mcc" in self.metric_names:
            metrics["mcc"] = self._calculate_mcc(pred_classes, targets)
        
        if "specificity" in self.metric_names:
            metrics["specificity"] = self._calculate_specificity(pred_classes, targets)
        
        if "sensitivity" in self.metric_names:
            metrics["sensitivity"] = self._calculate_sensitivity(pred_classes, targets)
        
        # Calculate regression metrics (if applicable)
        if "mse" in self.metric_names:
            metrics["mse"] = self._calculate_mse(predictions, targets)
        
        if "mae" in self.metric_names:
            metrics["mae"] = self._calculate_mae(predictions, targets)
        
        if "r2" in self.metric_names:
            metrics["r2"] = self._calculate_r2(predictions, targets)
        
        # Calculate ranking metrics
        if "ndcg" in self.metric_names:
            metrics["ndcg"] = self._calculate_ndcg(pred_probs, targets)
        
        if "mrr" in self.metric_names:
            metrics["mrr"] = self._calculate_mrr(pred_probs, targets)
        
        return metrics
    
    def _calculate_accuracy(self, predictions: np.ndarray, targets: np.ndarray) -> float:
        """Calculate accuracy."""
        try:
            return float(accuracy_score(targets, predictions))
        except Exception as e:
            logger.warning(f"Error calculating accuracy: {e}")
            return 0.0
    
    def _calculate_precision(self, predictions: np.ndarray, targets: np.ndarray) -> float:
        """Calculate precision."""
        try:
            return float(precision_score(targets, predictions, average='binary', zero_division=0))
        except Exception as e:
            logger.warning(f"Error calculating precision: {e}")
            return 0.0
    
    def _calculate_recall(self, predictions: np.ndarray, targets: np.ndarray) -> float:
        """Calculate recall."""
        try:
            return float(recall_score(targets, predictions, average='binary', zero_division=0))
        except Exception as e:
            logger.warning(f"Error calculating recall: {e}")
            return 0.0
    
    def _calculate_f1(self, predictions: np.ndarray, targets: np.ndarray) -> float:
        """Calculate F1 score."""
        try:
            return float(f1_score(targets, predictions, average='binary', zero_division=0))
        except Exception as e:
            logger.warning(f"Error calculating F1: {e}")
            return 0.0
    
    def _calculate_auc(self, predictions: np.ndarray, targets: np.ndarray) -> float:
        """Calculate AUC-ROC."""
        try:
            if len(np.unique(targets)) < 2:
                logger.warning("Cannot calculate AUC with only one class")
                return 0.0
            return float(roc_auc_score(targets, predictions))
        except Exception as e:
            logger.warning(f"Error calculating AUC: {e}")
            return 0.0
    
    def _calculate_average_precision(self, predictions: np.ndarray, targets: np.ndarray) -> float:
        """Calculate Average Precision."""
        try:
            if len(np.unique(targets)) < 2:
                logger.warning("Cannot calculate AP with only one class")
                return 0.0
            return float(average_precision_score(targets, predictions))
        except Exception as e:
            logger.warning(f"Error calculating AP: {e}")
            return 0.0
    
    def _calculate_mcc(self, predictions: np.ndarray, targets: np.ndarray) -> float:
        """Calculate Matthews Correlation Coefficient."""
        try:
            return float(matthews_corrcoef(targets, predictions))
        except Exception as e:
            logger.warning(f"Error calculating MCC: {e}")
            return 0.0
    
    def _calculate_specificity(self, predictions: np.ndarray, targets: np.ndarray) -> float:
        """Calculate specificity (true negative rate)."""
        try:
            tn, fp, fn, tp = confusion_matrix(targets, predictions).ravel()
            if tn + fp == 0:
                return 0.0
            return float(tn / (tn + fp))
        except Exception as e:
            logger.warning(f"Error calculating specificity: {e}")
            return 0.0
    
    def _calculate_sensitivity(self, predictions: np.ndarray, targets: np.ndarray) -> float:
        """Calculate sensitivity (true positive rate, same as recall)."""
        return self._calculate_recall(predictions, targets)
    
    def _calculate_mse(self, predictions: np.ndarray, targets: np.ndarray) -> float:
        """Calculate Mean Squared Error."""
        try:
            return float(np.mean((predictions - targets) ** 2))
        except Exception as e:
            logger.warning(f"Error calculating MSE: {e}")
            return float('inf')
    
    def _calculate_mae(self, predictions: np.ndarray, targets: np.ndarray) -> float:
        """Calculate Mean Absolute Error."""
        try:
            return float(np.mean(np.abs(predictions - targets)))
        except Exception as e:
            logger.warning(f"Error calculating MAE: {e}")
            return float('inf')
    
    def _calculate_r2(self, predictions: np.ndarray, targets: np.ndarray) -> float:
        """Calculate R-squared."""
        try:
            ss_res = np.sum((targets - predictions) ** 2)
            ss_tot = np.sum((targets - np.mean(targets)) ** 2)
            if ss_tot == 0:
                return 0.0
            return float(1 - (ss_res / ss_tot))
        except Exception as e:
            logger.warning(f"Error calculating R²: {e}")
            return 0.0
    
    def _calculate_ndcg(self, predictions: np.ndarray, targets: np.ndarray, k: int = 10) -> float:
        """Calculate Normalized Discounted Cumulative Gain."""
        try:
            # Sort by predictions (descending)
            sorted_indices = np.argsort(predictions)[::-1][:k]
            sorted_targets = targets[sorted_indices]
            
            # Calculate DCG
            dcg = 0.0
            for i, relevance in enumerate(sorted_targets):
                dcg += relevance / np.log2(i + 2)
            
            # Calculate IDCG (ideal DCG)
            ideal_targets = np.sort(targets)[::-1][:k]
            idcg = 0.0
            for i, relevance in enumerate(ideal_targets):
                idcg += relevance / np.log2(i + 2)
            
            if idcg == 0:
                return 0.0
            
            return float(dcg / idcg)
        except Exception as e:
            logger.warning(f"Error calculating NDCG: {e}")
            return 0.0
    
    def _calculate_mrr(self, predictions: np.ndarray, targets: np.ndarray) -> float:
        """Calculate Mean Reciprocal Rank."""
        try:
            # Find the rank of the first relevant item
            sorted_indices = np.argsort(predictions)[::-1]
            sorted_targets = targets[sorted_indices]
            
            # Find first relevant item (assuming binary relevance)
            relevant_indices = np.where(sorted_targets > 0)[0]
            
            if len(relevant_indices) == 0:
                return 0.0
            
            # Rank is 1-indexed
            first_relevant_rank = relevant_indices[0] + 1
            return float(1.0 / first_relevant_rank)
        except Exception as e:
            logger.warning(f"Error calculating MRR: {e}")
            return 0.0
    
    def calculate_confusion_matrix(self, 
                                 predictions: torch.Tensor, 
                                 targets: torch.Tensor,
                                 threshold: float = 0.5) -> np.ndarray:
        """Calculate confusion matrix."""
        # Convert to numpy
        if isinstance(predictions, torch.Tensor):
            predictions = predictions.detach().cpu().numpy()
        if isinstance(targets, torch.Tensor):
            targets = targets.detach().cpu().numpy()
        
        # Apply threshold
        if predictions.ndim > 1:
            predictions = predictions.squeeze()
        
        # Apply sigmoid if needed
        if np.any(predictions < 0) or np.any(predictions > 1):
            predictions = 1 / (1 + np.exp(-predictions))
        
        pred_classes = (predictions >= threshold).astype(int)
        
        return confusion_matrix(targets, pred_classes)
    
    def generate_classification_report(self, 
                                     predictions: torch.Tensor, 
                                     targets: torch.Tensor,
                                     threshold: float = 0.5,
                                     target_names: Optional[List[str]] = None) -> str:
        """Generate detailed classification report."""
        # Convert to numpy
        if isinstance(predictions, torch.Tensor):
            predictions = predictions.detach().cpu().numpy()
        if isinstance(targets, torch.Tensor):
            targets = targets.detach().cpu().numpy()
        
        # Apply threshold
        if predictions.ndim > 1:
            predictions = predictions.squeeze()
        
        # Apply sigmoid if needed
        if np.any(predictions < 0) or np.any(predictions > 1):
            predictions = 1 / (1 + np.exp(-predictions))
        
        pred_classes = (predictions >= threshold).astype(int)
        
        return classification_report(
            targets, pred_classes, 
            target_names=target_names,
            zero_division=0
        )
    
    def calculate_per_class_metrics(self, 
                                  predictions: torch.Tensor, 
                                  targets: torch.Tensor,
                                  threshold: float = 0.5) -> Dict[str, Dict[str, float]]:
        """Calculate metrics for each class separately."""
        # Convert to numpy
        if isinstance(predictions, torch.Tensor):
            predictions = predictions.detach().cpu().numpy()
        if isinstance(targets, torch.Tensor):
            targets = targets.detach().cpu().numpy()
        
        # Apply threshold
        if predictions.ndim > 1:
            predictions = predictions.squeeze()
        
        # Apply sigmoid if needed
        if np.any(predictions < 0) or np.any(predictions > 1):
            predictions = 1 / (1 + np.exp(-predictions))
        
        pred_classes = (predictions >= threshold).astype(int)
        
        # Calculate confusion matrix
        cm = confusion_matrix(targets, pred_classes)
        
        if cm.shape == (2, 2):
            tn, fp, fn, tp = cm.ravel()
            
            # Class 0 (negative) metrics
            class_0_metrics = {
                "precision": tn / (tn + fn) if (tn + fn) > 0 else 0.0,
                "recall": tn / (tn + fp) if (tn + fp) > 0 else 0.0,
                "f1": 0.0,
                "support": tn + fn
            }
            if class_0_metrics["precision"] + class_0_metrics["recall"] > 0:
                class_0_metrics["f1"] = 2 * (class_0_metrics["precision"] * class_0_metrics["recall"]) / (class_0_metrics["precision"] + class_0_metrics["recall"])
            
            # Class 1 (positive) metrics
            class_1_metrics = {
                "precision": tp / (tp + fp) if (tp + fp) > 0 else 0.0,
                "recall": tp / (tp + fn) if (tp + fn) > 0 else 0.0,
                "f1": 0.0,
                "support": tp + fn
            }
            if class_1_metrics["precision"] + class_1_metrics["recall"] > 0:
                class_1_metrics["f1"] = 2 * (class_1_metrics["precision"] * class_1_metrics["recall"]) / (class_1_metrics["precision"] + class_1_metrics["recall"])
            
            return {
                "class_0": class_0_metrics,
                "class_1": class_1_metrics
            }
        
        return {}
    
    def calculate_threshold_metrics(self, 
                                  predictions: torch.Tensor, 
                                  targets: torch.Tensor,
                                  thresholds: List[float] = None) -> Dict[float, Dict[str, float]]:
        """Calculate metrics for different thresholds."""
        if thresholds is None:
            thresholds = [0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9]
        
        threshold_metrics = {}
        
        for threshold in thresholds:
            metrics = self.calculate_metrics(predictions, targets, threshold)
            threshold_metrics[threshold] = metrics
        
        return threshold_metrics
