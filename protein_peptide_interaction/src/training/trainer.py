#!/usr/bin/env python3
"""
Training Framework for Protein-Peptide Interaction Model

Supports single and multi-GPU training with memory optimization,
gradient accumulation, and mixed precision training.
"""

import os
import sys
import time
import logging
import torch
import torch.nn as nn
import torch.distributed as dist
from torch.nn.parallel import DistributedDataParallel as DDP
from torch.cuda.amp import GradScaler, autocast
from typing import Dict, List, Tuple, Optional, Any, Union
import numpy as np
from pathlib import Path
import json
try:
    import wandb
    WANDB_AVAILABLE = True
except ImportError:
    WANDB_AVAILABLE = False
    wandb = None
try:
    from torch.utils.tensorboard import SummaryWriter
    TENSORBOARD_AVAILABLE = True
except ImportError:
    TENSORBOARD_AVAILABLE = False
    SummaryWriter = None

from ..config import ConfigManager
from ..data import create_dataset, create_dataloader
from ..encoders.protein import ProteinEncoderFactory
from ..encoders.peptide import PeptideEncoderFactory
from ..models import ModelFactory
from .loss import LossFunction
from .metrics import MetricsCalculator
from ..utils import GPUManager, MemoryOptimizer

logger = logging.getLogger(__name__)

class Trainer:
    """
    Main training framework for protein-peptide interaction prediction.
    
    Features:
    - Single and multi-GPU training
    - Mixed precision training
    - Gradient accumulation
    - Memory optimization
    - Checkpointing and resuming
    - Logging and monitoring
    """
    
    def __init__(self, config: Dict[str, Any], resume_from: Optional[str] = None):
        """
        Initialize the trainer.
        
        Args:
            config: Training configuration
            resume_from: Path to checkpoint to resume from
        """
        self.config = config
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # Initialize distributed training
        self.distributed = self._init_distributed()
        self.local_rank = int(os.environ.get("LOCAL_RANK", 0))
        self.world_size = int(os.environ.get("WORLD_SIZE", 1))
        self.is_main_process = self.local_rank == 0
        
        # Initialize components
        self._init_logging()
        self._init_models()
        self._init_data()
        self._init_optimizer()
        self._init_loss_and_metrics()
        self._init_monitoring()
        
        # Training state
        self.current_epoch = 0
        self.global_step = 0

        # Initialize best_metric based on monitoring mode
        early_stopping_config = self.config.get("validation", {}).get("early_stopping", {})
        mode = str(early_stopping_config.get("mode", "max"))
        self.best_metric = float('-inf') if mode == "max" else float('inf')
        
        # Memory optimization
        self.memory_optimizer = MemoryOptimizer(config.get("hardware", {}))
        self.scaler = GradScaler() if config.get("hardware", {}).get("mixed_precision", True) else None
        
        # Resume from checkpoint if specified
        if resume_from:
            self.load_checkpoint(resume_from)
        
        logger.info(f"Trainer initialized on device: {self.device}")
        logger.info(f"Distributed training: {self.distributed}, World size: {self.world_size}")
    
    def _init_distributed(self) -> bool:
        """Initialize distributed training."""
        if "RANK" in os.environ and "WORLD_SIZE" in os.environ:
            rank = int(os.environ["RANK"])
            world_size = int(os.environ["WORLD_SIZE"])
            local_rank = int(os.environ["LOCAL_RANK"])
            
            # Initialize process group
            dist.init_process_group(
                backend=self.config.get("hardware", {}).get("distributed", {}).get("backend", "nccl"),
                init_method=self.config.get("hardware", {}).get("distributed", {}).get("init_method", "env://"),
                world_size=world_size,
                rank=rank
            )
            
            # Set device
            torch.cuda.set_device(local_rank)
            self.device = torch.device(f"cuda:{local_rank}")
            
            return True
        
        return False
    
    def _init_logging(self):
        """Initialize logging."""
        if self.is_main_process:
            # Console logging
            logging.basicConfig(
                level=getattr(logging, self.config.get("logging", {}).get("level", "INFO")),
                format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            
            # File logging
            log_config = self.config.get("logging", {})
            if log_config.get("file", {}).get("enabled", True):
                log_dir = Path(log_config.get("file", {}).get("log_dir", "logs"))
                log_dir.mkdir(parents=True, exist_ok=True)
                
                file_handler = logging.FileHandler(log_dir / "training.log")
                file_handler.setLevel(getattr(logging, log_config.get("file", {}).get("level", "DEBUG")))
                formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
                file_handler.setFormatter(formatter)
                logger.addHandler(file_handler)
    
    def _init_models(self):
        """Initialize models and encoders."""
        # Initialize encoders
        protein_encoder_config = self.config.get("encoders", {}).get("protein", {})
        peptide_encoder_config = self.config.get("encoders", {}).get("peptide", {})
        
        self.protein_encoder = ProteinEncoderFactory.create(
            protein_encoder_config.get("type", "rapidock"),
            protein_encoder_config
        ).to(self.device)
        
        self.peptide_encoder = PeptideEncoderFactory.create(
            peptide_encoder_config.get("type", "rapidock"),
            peptide_encoder_config
        ).to(self.device)
        
        # Initialize interaction model
        model_config = self.config.get("model", {})
        self.model = ModelFactory.create(
            model_config.get("type", "gin"),
            model_config
        ).to(self.device)
        
        # Wrap with DDP if distributed
        if self.distributed:
            self.protein_encoder = DDP(
                self.protein_encoder,
                device_ids=[self.local_rank],
                output_device=self.local_rank,
                find_unused_parameters=self.config.get("hardware", {}).get("distributed", {}).get("find_unused_parameters", False)
            )
            self.peptide_encoder = DDP(
                self.peptide_encoder,
                device_ids=[self.local_rank],
                output_device=self.local_rank,
                find_unused_parameters=False
            )
            self.model = DDP(
                self.model,
                device_ids=[self.local_rank],
                output_device=self.local_rank,
                find_unused_parameters=False
            )
        
        # Model compilation (PyTorch 2.0+)
        compilation_config = self.config.get("hardware", {}).get("compilation", {})
        if compilation_config.get("enabled", False):
            try:
                self.protein_encoder = torch.compile(
                    self.protein_encoder,
                    backend=compilation_config.get("backend", "inductor"),
                    mode=compilation_config.get("mode", "default")
                )
                self.peptide_encoder = torch.compile(self.peptide_encoder)
                self.model = torch.compile(self.model)
                logger.info("Models compiled successfully")
            except Exception as e:
                logger.warning(f"Model compilation failed: {e}")
    
    def _init_data(self):
        """Initialize data loaders."""
        data_config = self.config.get("data", {})
        
        # DEBUG: Check the type and content of datasets config before passing
        if 'datasets' in data_config:
            logger.debug(f"[trainer.py] Type of data_config['datasets']: {type(data_config['datasets'])}")
            logger.debug(f"[trainer.py] Content of data_config['datasets']: {data_config['datasets']}")
        else:
            logger.debug("[trainer.py] 'datasets' key not found in data_config.")

        # Create datasets
        self.train_dataset = create_dataset(data_config, split="train")
        self.val_dataset = create_dataset(data_config, split="val")
        
        # Create data loaders
        self.train_loader = create_dataloader(
            self.train_dataset, data_config, split="train", distributed=self.distributed
        )
        self.val_loader = create_dataloader(
            self.val_dataset, data_config, split="val", distributed=self.distributed
        )
        
        logger.info(f"Training samples: {len(self.train_dataset)}")
        logger.info(f"Validation samples: {len(self.val_dataset)}")
    
    def _init_optimizer(self):
        """Initialize optimizer and scheduler."""
        training_config = self.config.get("training", {})
        
        # Collect parameters
        params = []
        params.extend(self.protein_encoder.parameters())
        params.extend(self.peptide_encoder.parameters())
        params.extend(self.model.parameters())
        
        # Optimizer
        optimizer_config = training_config.get("optimizer", {})
        optimizer_type = optimizer_config.get("type", "adamw")
        
        if optimizer_type == "adamw":
            self.optimizer = torch.optim.AdamW(
                params,
                lr=float(training_config.get("learning_rate", 0.001)),
                weight_decay=float(training_config.get("weight_decay", 0.0001)),
                betas=optimizer_config.get("betas", [0.9, 0.999]),
                eps=float(optimizer_config.get("eps", 1e-8))
            )
        elif optimizer_type == "adam":
            self.optimizer = torch.optim.Adam(
                params,
                lr=float(training_config.get("learning_rate", 0.001)),
                weight_decay=float(training_config.get("weight_decay", 0.0001)),
                betas=optimizer_config.get("betas", [0.9, 0.999]),
                eps=float(optimizer_config.get("eps", 1e-8))
            )
        elif optimizer_type == "sgd":
            self.optimizer = torch.optim.SGD(
                params,
                lr=float(training_config.get("learning_rate", 0.001)),
                weight_decay=float(training_config.get("weight_decay", 0.0001)),
                momentum=float(optimizer_config.get("momentum", 0.9))
            )
        else:
            raise ValueError(f"Unknown optimizer type: {optimizer_type}")
        
        # Scheduler
        scheduler_config = training_config.get("scheduler", {})
        scheduler_type = scheduler_config.get("type", "cosine_annealing")
        
        if scheduler_type == "cosine_annealing":
            T_max_value = scheduler_config.get("T_max", training_config.get("num_epochs", 100))
            eta_min_value = scheduler_config.get("eta_min", 1e-6)

            self.scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
                self.optimizer,
                T_max=int(T_max_value) if T_max_value is not None else 100,
                eta_min=float(eta_min_value) if eta_min_value is not None else 1e-6
            )
        elif scheduler_type == "step":
            step_size_value = scheduler_config.get("step_size", 30)
            gamma_value = scheduler_config.get("gamma", 0.1)

            self.scheduler = torch.optim.lr_scheduler.StepLR(
                self.optimizer,
                step_size=int(step_size_value) if step_size_value is not None else 30,
                gamma=float(gamma_value) if gamma_value is not None else 0.1
            )
        else:
            self.scheduler = None
        
        # Gradient clipping
        gradient_clipping_value = training_config.get("gradient_clipping", 1.0)
        self.gradient_clipping = float(gradient_clipping_value) if gradient_clipping_value is not None else 1.0

        accumulation_steps = training_config.get("gradient_accumulation_steps", 1)
        self.gradient_accumulation_steps = int(accumulation_steps) if accumulation_steps is not None else 1
    
    def _init_loss_and_metrics(self):
        """Initialize loss function and metrics."""
        loss_config = self.config.get("training", {}).get("loss", {})
        self.loss_function = LossFunction(loss_config)
        
        metrics_config = self.config.get("evaluation", {}).get("metrics", [])
        self.metrics_calculator = MetricsCalculator(metrics_config)
    
    def _init_monitoring(self):
        """Initialize monitoring and logging."""
        if not self.is_main_process:
            return
        
        # Weights & Biases
        wandb_config = self.config.get("logging", {}).get("wandb", {})
        if wandb_config.get("enabled", False) and WANDB_AVAILABLE:
            wandb.init(
                project=wandb_config.get("project", "protein_peptide_interaction"),
                entity=wandb_config.get("entity"),
                config=self.config,
                tags=wandb_config.get("tags", [])
            )
        
        # TensorBoard
        tensorboard_config = self.config.get("logging", {}).get("tensorboard", {})
        if tensorboard_config.get("enabled", True) and TENSORBOARD_AVAILABLE:
            log_dir = Path(tensorboard_config.get("log_dir", "runs"))
            log_dir.mkdir(parents=True, exist_ok=True)
            self.writer = SummaryWriter(log_dir)
        else:
            self.writer = None
    
    def train_epoch(self) -> Dict[str, float]:
        """Train for one epoch."""
        self.protein_encoder.train()
        self.peptide_encoder.train()
        self.model.train()
        
        epoch_metrics = {}
        total_loss = 0.0
        num_batches = 0
        
        # Set epoch for distributed sampler
        if self.distributed and hasattr(self.train_loader, 'set_epoch'):
            self.train_loader.set_epoch(self.current_epoch)
        
        for batch_idx, batch in enumerate(self.train_loader):
            # Move batch to device
            batch = self._move_batch_to_device(batch)
            
            # Forward pass
            with autocast(enabled=self.scaler is not None):
                loss, predictions, targets = self._forward_pass(batch)
                loss = loss / self.gradient_accumulation_steps
            
            # Backward pass
            if self.scaler is not None:
                self.scaler.scale(loss).backward()
            else:
                loss.backward()
            
            # Gradient accumulation
            if (batch_idx + 1) % self.gradient_accumulation_steps == 0:
                # Gradient clipping
                if self.gradient_clipping > 0:
                    if self.scaler is not None:
                        self.scaler.unscale_(self.optimizer)
                    torch.nn.utils.clip_grad_norm_(
                        list(self.protein_encoder.parameters()) + 
                        list(self.peptide_encoder.parameters()) + 
                        list(self.model.parameters()),
                        self.gradient_clipping
                    )
                
                # Optimizer step
                if self.scaler is not None:
                    self.scaler.step(self.optimizer)
                    self.scaler.update()
                else:
                    self.optimizer.step()
                
                self.optimizer.zero_grad()
                self.global_step += 1
            
            # Update metrics
            total_loss += loss.item() * self.gradient_accumulation_steps
            num_batches += 1
            
            # Logging
            if self.is_main_process and batch_idx % self.config.get("logging", {}).get("console", {}).get("frequency", 10) == 0:
                logger.info(
                    f"Epoch {self.current_epoch}, Batch {batch_idx}/{len(self.train_loader)}, "
                    f"Loss: {loss.item() * self.gradient_accumulation_steps:.4f}, "
                    f"LR: {self.optimizer.param_groups[0]['lr']:.6f}"
                )
        
        # Calculate epoch metrics
        epoch_metrics["train_loss"] = total_loss / max(num_batches, 1)  # Avoid division by zero
        
        return epoch_metrics
    
    def validate(self) -> Dict[str, float]:
        """Validate the model."""
        self.protein_encoder.eval()
        self.peptide_encoder.eval()
        self.model.eval()
        
        val_metrics = {}
        total_loss = 0.0
        all_predictions = []
        all_targets = []
        
        with torch.no_grad():
            for batch in self.val_loader:
                batch = self._move_batch_to_device(batch)
                
                with autocast(enabled=self.scaler is not None):
                    loss, predictions, targets = self._forward_pass(batch)
                
                total_loss += loss.item()
                all_predictions.append(predictions)
                all_targets.append(targets)
        
        # Calculate metrics
        if all_predictions and all_targets:
            all_predictions = torch.cat(all_predictions, dim=0)
            all_targets = torch.cat(all_targets, dim=0)

            # Calculate additional metrics
            metrics = self.metrics_calculator.calculate_metrics(all_predictions, all_targets)
            val_metrics.update({f"val_{k}": v for k, v in metrics.items()})
        else:
            logger.warning("No validation data available for metrics calculation")

        val_metrics["val_loss"] = total_loss / max(len(self.val_loader), 1)  # Avoid division by zero
        
        return val_metrics
    
    def _forward_pass(self, batch: Dict[str, Any]) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """Forward pass through the model."""
        # Extract data from batch
        protein_data = batch["protein"]
        peptide_data = batch["peptide"]
        labels = batch["labels"]
        
        # Encode protein
        protein_outputs = self.protein_encoder(
            sequences=protein_data.get("sequences"),
            coordinates=protein_data.get("coordinates"),
            features=protein_data.get("features"),
            masks=protein_data.get("coordinate_masks")
        )
        
        # Encode peptide
        peptide_outputs = self.peptide_encoder(
            sequences=peptide_data.get("sequences"),
            coordinates=peptide_data.get("coordinates"),
            atom_data=peptide_data.get("atom_data"),
            conformations=peptide_data.get("conformations"),
            masks=peptide_data.get("masks")
        )
        
        # Interaction prediction
        interaction_outputs = self.model(
            protein_features=protein_outputs["node_features"],
            peptide_features=peptide_outputs["node_features"],
            protein_coords=protein_data.get("coordinates"),
            peptide_coords=peptide_data.get("coordinates"),
            protein_masks=protein_data.get("coordinate_masks"),
            peptide_masks=peptide_data.get("coordinate_masks")
        )
        
        # Get predictions (handle different model output formats)
        if "predictions" in interaction_outputs:
            predictions = interaction_outputs["predictions"]
            main_prediction = predictions.get("interaction_prediction", predictions[list(predictions.keys())[0]])
        elif "interaction_probability" in interaction_outputs:
            # RAPiDock model format
            main_prediction = interaction_outputs["interaction_probability"]
            predictions = {"interaction_prediction": main_prediction}
        elif "logits" in interaction_outputs:
            # Logits format
            main_prediction = torch.sigmoid(interaction_outputs["logits"])
            predictions = {"interaction_prediction": main_prediction}
        else:
            # Fallback: use first available output
            available_keys = list(interaction_outputs.keys())
            main_prediction = interaction_outputs[available_keys[0]]
            predictions = {"interaction_prediction": main_prediction}

        # 保证 prediction 和 labels shape 一致
        if main_prediction.shape != labels.shape:
            # 常见情况 prediction 为 [batch, 1]，labels 为 [batch]
            if main_prediction.dim() == 2 and main_prediction.shape[1] == 1:
                main_prediction = main_prediction.view(-1)
            if labels.dim() == 2 and labels.shape[1] == 1:
                labels = labels.view(-1)
        loss = self.loss_function(main_prediction, labels)
        return loss, main_prediction, labels
    
    def _move_batch_to_device(self, batch: Dict[str, Any]) -> Dict[str, Any]:
        """Move batch to device."""
        def move_to_device(obj):
            if isinstance(obj, torch.Tensor):
                return obj.to(self.device, non_blocking=True)
            elif isinstance(obj, dict):
                return {k: move_to_device(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [move_to_device(item) for item in obj]
            else:
                return obj
        batch_on_device = move_to_device(batch)
        # 额外确保 features、coords、labels 都在 device 上
        for key in ["protein", "peptide"]:
            if key in batch_on_device:
                for subkey in ["features", "coordinates", "atom_data", "conformations", "masks", "coordinate_masks"]:
                    if subkey in batch_on_device[key]:
                        val = batch_on_device[key][subkey]
                        if isinstance(val, torch.Tensor):
                            batch_on_device[key][subkey] = val.to(self.device, non_blocking=True)
        if "labels" in batch_on_device and isinstance(batch_on_device["labels"], torch.Tensor):
            batch_on_device["labels"] = batch_on_device["labels"].to(self.device, non_blocking=True)
        return batch_on_device
    
    def save_checkpoint(self, filepath: str, is_best: bool = False):
        """Save training checkpoint."""
        if not self.is_main_process:
            return
        
        checkpoint = {
            "epoch": self.current_epoch,
            "global_step": self.global_step,
            "best_metric": self.best_metric,
            "config": self.config,
            "protein_encoder_state_dict": self._get_model_state_dict(self.protein_encoder),
            "peptide_encoder_state_dict": self._get_model_state_dict(self.peptide_encoder),
            "model_state_dict": self._get_model_state_dict(self.model),
            "optimizer_state_dict": self.optimizer.state_dict(),
            "scheduler_state_dict": self.scheduler.state_dict() if self.scheduler else None,
            "scaler_state_dict": self.scaler.state_dict() if self.scaler else None
        }
        
        # Create checkpoint directory
        checkpoint_dir = Path(filepath).parent
        checkpoint_dir.mkdir(parents=True, exist_ok=True)
        
        # Save checkpoint
        torch.save(checkpoint, filepath)
        logger.info(f"Checkpoint saved: {filepath}")
        
        # Save best model
        if is_best:
            best_path = checkpoint_dir / "best_model.pth"
            torch.save(checkpoint, best_path)
            logger.info(f"Best model saved: {best_path}")
    
    def load_checkpoint(self, filepath: str):
        """Load training checkpoint."""
        checkpoint = torch.load(filepath, map_location=self.device)
        
        self.current_epoch = checkpoint["epoch"]
        self.global_step = checkpoint["global_step"]
        self.best_metric = checkpoint["best_metric"]
        
        # Load model states
        self._load_model_state_dict(self.protein_encoder, checkpoint["protein_encoder_state_dict"])
        self._load_model_state_dict(self.peptide_encoder, checkpoint["peptide_encoder_state_dict"])
        self._load_model_state_dict(self.model, checkpoint["model_state_dict"])
        
        # Load optimizer and scheduler
        self.optimizer.load_state_dict(checkpoint["optimizer_state_dict"])
        if self.scheduler and checkpoint["scheduler_state_dict"]:
            self.scheduler.load_state_dict(checkpoint["scheduler_state_dict"])
        if self.scaler and checkpoint["scaler_state_dict"]:
            self.scaler.load_state_dict(checkpoint["scaler_state_dict"])
        
        logger.info(f"Checkpoint loaded: {filepath}")
    
    def _get_model_state_dict(self, model):
        """Get model state dict, handling DDP wrapper."""
        if isinstance(model, DDP):
            return model.module.state_dict()
        else:
            return model.state_dict()
    
    def _load_model_state_dict(self, model, state_dict):
        """Load model state dict, handling DDP wrapper."""
        if isinstance(model, DDP):
            model.module.load_state_dict(state_dict)
        else:
            model.load_state_dict(state_dict)
    
    def train(self):
        """Main training loop."""
        logger.info("Starting training...")
        
        training_config = self.config.get("training", {})
        num_epochs = int(training_config.get("num_epochs", 100))
        
        # Early stopping
        early_stopping_config = self.config.get("validation", {}).get("early_stopping", {})
        patience = int(early_stopping_config.get("patience", 10))
        min_delta = float(early_stopping_config.get("min_delta", 1e-4))
        monitor_metric = str(early_stopping_config.get("metric", "val_auc"))
        mode = str(early_stopping_config.get("mode", "max"))
        
        patience_counter = 0
        
        for epoch in range(self.current_epoch, num_epochs):
            self.current_epoch = epoch
            
            # Training
            train_metrics = self.train_epoch()
            
            # Validation
            val_metrics = self.validate()
            
            # Scheduler step
            if self.scheduler:
                self.scheduler.step()
            
            # Combine metrics
            all_metrics = {**train_metrics, **val_metrics}
            
            # Logging
            if self.is_main_process:
                logger.info(f"Epoch {epoch} - " + " - ".join([f"{k}: {v:.4f}" for k, v in all_metrics.items()]))
                
                # TensorBoard logging
                if self.writer:
                    for key, value in all_metrics.items():
                        self.writer.add_scalar(key, value, epoch)
                    self.writer.add_scalar("learning_rate", self.optimizer.param_groups[0]["lr"], epoch)
                
                # Weights & Biases logging
                if WANDB_AVAILABLE and wandb.run:
                    wandb.log(all_metrics, step=epoch)
            
            # Checkpointing
            current_metric = val_metrics.get(monitor_metric, val_metrics.get("val_loss", 0))
            current_metric = float(current_metric) if current_metric is not None else 0.0
            is_best = False

            if mode == "max":
                if current_metric > self.best_metric + min_delta:
                    self.best_metric = current_metric
                    is_best = True
                    patience_counter = 0
                else:
                    patience_counter += 1
            else:  # mode == "min"
                if current_metric < self.best_metric - min_delta:
                    self.best_metric = current_metric
                    is_best = True
                    patience_counter = 0
                else:
                    patience_counter += 1
            
            # Save checkpoint
            checkpoint_config = self.config.get("checkpointing", {})
            save_frequency = int(checkpoint_config.get("save_frequency", 5))
            if epoch % save_frequency == 0:
                checkpoint_path = Path(checkpoint_config.get("save_dir", "checkpoints")) / f"checkpoint_epoch_{epoch}.pth"
                self.save_checkpoint(str(checkpoint_path), is_best)
            
            # Early stopping
            if patience_counter >= patience:
                logger.info(f"Early stopping triggered after {patience} epochs without improvement")
                break
        
        logger.info("Training completed!")
        
        # Cleanup
        if self.distributed:
            dist.destroy_process_group()
        
        if self.writer:
            self.writer.close()
        
        if WANDB_AVAILABLE and wandb.run:
            wandb.finish()