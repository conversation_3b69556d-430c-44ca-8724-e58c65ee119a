#!/usr/bin/env python3
"""
Configuration Manager for Protein-Peptide Interaction Model

Handles loading and merging of YAML configuration files with support for
modular configuration and environment-specific overrides.
"""

import os
import yaml
from typing import Dict, Any, Optional, Union
from pathlib import Path
import logging
from omegaconf import OmegaConf, DictConfig

logger = logging.getLogger(__name__)

class ConfigManager:
    """
    Manages configuration loading and merging for the protein-peptide interaction model.
    
    Features:
    - Hierarchical configuration loading
    - Environment-specific overrides
    - Modular encoder and model configurations
    - Validation and type checking
    """
    
    def __init__(self, config_dir: Optional[str] = None):
        """
        Initialize the configuration manager.
        
        Args:
            config_dir: Path to configuration directory. If None, uses default.
        """
        self.project_root = Path(__file__).parent.parent.parent
        if config_dir is None:
            config_dir = self.project_root / "configs"
        
        self.config_dir = Path(config_dir)
        self.config_cache = {}
        
        if not self.config_dir.exists():
            raise FileNotFoundError(f"Configuration directory not found: {self.config_dir}")
    
    def load_config(self, config_path: str, use_cache: bool = True) -> DictConfig:
        """
        Load a configuration file.
        
        Args:
            config_path: Path to config file (can be absolute or relative to project root)
            use_cache: Whether to use cached configuration
            
        Returns:
            Loaded configuration as OmegaConf DictConfig
        """
        # Convert to absolute path if relative
        if not os.path.isabs(config_path):
            # Check if the path is relative to the config dir (e.g., 'default.yaml')
            # or relative to the project root (e.g., 'configs/default.yaml')
            potential_path = self.config_dir / config_path
            if potential_path.exists():
                config_path = potential_path
            else:
                config_path = self.project_root / config_path
        else:
            config_path = Path(config_path)
        
        # Check cache
        cache_key = str(config_path)
        if use_cache and cache_key in self.config_cache:
            return self.config_cache[cache_key]
        
        # Load configuration
        if not config_path.exists():
            raise FileNotFoundError(f"Configuration file not found: {config_path}")
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config_dict = yaml.safe_load(f)
            
            config = OmegaConf.create(config_dict)
            
            # Cache the configuration
            if use_cache:
                self.config_cache[cache_key] = config
            
            logger.info(f"Loaded configuration from {config_path}")
            return config
            
        except Exception as e:
            logger.error(f"Failed to load configuration from {config_path}: {e}")
            raise
    
    def merge_configs(self, *config_paths: str) -> DictConfig:
        """
        Merge multiple configuration files.
        
        Args:
            *config_paths: Paths to configuration files to merge
            
        Returns:
            Merged configuration
        """
        merged_config = OmegaConf.create({})
        
        for config_path in config_paths:
            config = self.load_config(config_path)
            merged_config = OmegaConf.merge(merged_config, config)
        
        return merged_config
    
    def load_encoder_config(self, encoder_type: str, encoder_name: str) -> DictConfig:
        """
        Load encoder-specific configuration.
        
        Args:
            encoder_type: Type of encoder ('protein' or 'peptide')
            encoder_name: Name of the encoder (e.g., 'rapidock', 'alphafold2')
            
        Returns:
            Encoder configuration
        """
        config_path = f"encoders/{encoder_type}_{encoder_name}.yaml"
        return self.load_config(config_path)
    
    def load_model_config(self, model_name: str) -> DictConfig:
        """
        Load model-specific configuration.
        
        Args:
            model_name: Name of the model (e.g., 'gin', 'quinnet')
            
        Returns:
            Model configuration
        """
        config_path = f"models/{model_name}.yaml"
        return self.load_config(config_path)
    
    def load_training_config(self, training_type: str = "single_gpu") -> DictConfig:
        """
        Load training configuration.
        
        Args:
            training_type: Type of training ('single_gpu' or 'multi_gpu')
            
        Returns:
            Training configuration
        """
        config_path = f"training/{training_type}.yaml"
        return self.load_config(config_path)
    
    def build_full_config(self, 
                         base_config: str = "default.yaml",
                         protein_encoder: str = "rapidock",
                         peptide_encoder: str = "rapidock", 
                         model: str = "gin",
                         training_type: str = "single_gpu",
                         overrides: Optional[Dict[str, Any]] = None) -> DictConfig:
        """
        Build a complete configuration by merging base config with specific components.
        
        Args:
            base_config: Base configuration file
            protein_encoder: Protein encoder type
            peptide_encoder: Peptide encoder type
            model: Interaction model type
            training_type: Training configuration type
            overrides: Additional configuration overrides
            
        Returns:
            Complete merged configuration
        """
        # Load base configuration
        config = self.load_config(base_config)
        
        # Load and merge encoder configurations
        protein_enc_config = self.load_encoder_config("protein", protein_encoder)
        peptide_enc_config = self.load_encoder_config("peptide", peptide_encoder)
        
        # Load model configuration
        model_config = self.load_model_config(model)
        
        # Load training configuration
        training_config = self.load_training_config(training_type)
        
        # Merge all configurations
        config = OmegaConf.merge(
            config,
            {"encoders": {"protein": protein_enc_config, "peptide": peptide_enc_config}},
            {"model": model_config},
            {"training": training_config}
        )
        
        # Apply overrides
        if overrides:
            override_config = OmegaConf.create(overrides)
            config = OmegaConf.merge(config, override_config)
        
        # Validate configuration
        self.validate_config(config)

        # Ensure numeric types are correct
        self._ensure_numeric_types(config)

        return config
    
    def validate_config(self, config: DictConfig) -> None:
        """
        Validate the configuration for required fields and consistency.
        
        Args:
            config: Configuration to validate
            
        Raises:
            ValueError: If configuration is invalid
        """
        required_sections = ["encoders", "model", "training", "data"]
        
        for section in required_sections:
            if section not in config:
                raise ValueError(f"Missing required configuration section: {section}")
        
        # Validate encoder configurations
        if "protein" not in config.encoders or "peptide" not in config.encoders:
            raise ValueError("Both protein and peptide encoder configurations required")
        
        # Validate model configuration
        if "type" not in config.model:
            raise ValueError("Model type must be specified")
        
        # Validate training configuration
        if "batch_size" not in config.training:
            raise ValueError("Training batch size must be specified")
        
        logger.info("Configuration validation passed")

    def _ensure_numeric_types(self, config: DictConfig) -> None:
        """Ensure numeric configuration values have correct types."""
        try:
            # Training numeric parameters
            if hasattr(config, 'training'):
                training = config.training
                if hasattr(training, 'batch_size'):
                    training.batch_size = int(training.batch_size)
                if hasattr(training, 'learning_rate'):
                    training.learning_rate = float(training.learning_rate)
                if hasattr(training, 'num_epochs'):
                    training.num_epochs = int(training.num_epochs)
                if hasattr(training, 'weight_decay'):
                    training.weight_decay = float(training.weight_decay)
                if hasattr(training, 'gradient_clipping'):
                    training.gradient_clipping = float(training.gradient_clipping)
                if hasattr(training, 'gradient_accumulation_steps'):
                    training.gradient_accumulation_steps = int(training.gradient_accumulation_steps)

                # Optimizer parameters
                if hasattr(training, 'optimizer'):
                    optimizer = training.optimizer
                    if hasattr(optimizer, 'eps'):
                        optimizer.eps = float(optimizer.eps)
                    if hasattr(optimizer, 'momentum'):
                        optimizer.momentum = float(optimizer.momentum)

                # Scheduler parameters
                if hasattr(training, 'scheduler'):
                    scheduler = training.scheduler
                    if hasattr(scheduler, 'T_max'):
                        scheduler.T_max = int(scheduler.T_max)
                    if hasattr(scheduler, 'eta_min'):
                        scheduler.eta_min = float(scheduler.eta_min)
                    if hasattr(scheduler, 'step_size'):
                        scheduler.step_size = int(scheduler.step_size)
                    if hasattr(scheduler, 'gamma'):
                        scheduler.gamma = float(scheduler.gamma)

            # Validation parameters
            if hasattr(config, 'validation'):
                validation = config.validation
                if hasattr(validation, 'early_stopping'):
                    early_stopping = validation.early_stopping
                    if hasattr(early_stopping, 'patience'):
                        early_stopping.patience = int(early_stopping.patience)
                    if hasattr(early_stopping, 'min_delta'):
                        early_stopping.min_delta = float(early_stopping.min_delta)

            # Checkpointing parameters
            if hasattr(config, 'checkpointing'):
                checkpointing = config.checkpointing
                if hasattr(checkpointing, 'save_frequency'):
                    checkpointing.save_frequency = int(checkpointing.save_frequency)

        except (ValueError, TypeError) as e:
            logger.warning(f"Type conversion warning: {e}")
            # Continue with original values if conversion fails

    def save_config(self, config: DictConfig, output_path: str) -> None:
        """
        Save configuration to file.
        
        Args:
            config: Configuration to save
            output_path: Output file path
        """
        output_path = Path(output_path)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            OmegaConf.save(config, f)
        
        logger.info(f"Configuration saved to {output_path}")
    
    def get_relative_path_config(self) -> Dict[str, str]:
        """
        Get relative paths for external modules.

        Returns:
            Dictionary of module names to relative paths
        """
        # Get the GitHub directory (where external modules are located)
        # Current file: /path/to/GitHub/protein_peptide_interaction/src/config/config_manager.py
        # Target: /path/to/GitHub/RAPiDock, /path/to/GitHub/customer_rice, etc.
        github_dir = Path(__file__).resolve().parent.parent.parent.parent

        return {
            "rapidock": str(github_dir / "RAPiDock"),
            "customer_rice": str(github_dir / "customer_rice"),
            "quinnet": str(github_dir / "QuinNet"),
            "dmasif": str(github_dir / "dMaSIF"),
        }
    
    def setup_module_paths(self) -> None:
        """
        Setup module paths for importing external modules.
        """
        import sys
        
        paths = self.get_relative_path_config()
        
        for module_name, module_path in paths.items():
            if os.path.exists(module_path) and module_path not in sys.path:
                sys.path.append(module_path)
                logger.info(f"Added {module_name} path: {module_path}")
            elif module_path in sys.path :
                logger.info(f"Module already found in : {module_path}")
            else:
                logger.warning(f"Module path not found: {module_path}")
                # Try alternative paths for customer_rice
                if module_name == "customer_rice":
                    alternative_paths = [
                        str(Path(__file__).parent.parent.parent.parent.parent / "customer_rice"),
                        str(Path.home() / "code" / "customer_rice"),
                        "/home/<USER>/code/customer_rice"
                    ]
                    for alt_path in alternative_paths:
                        if os.path.exists(alt_path) and alt_path not in sys.path:
                            sys.path.append(alt_path)
                            logger.info(f"Added {module_name} alternative path: {alt_path}")
                            break
                    else:
                        logger.error(f"Could not find {module_name} in any alternative paths")


# Global configuration manager instance
config_manager = ConfigManager()

def get_config_manager() -> ConfigManager:
    """Get the global configuration manager instance."""
    return config_manager
