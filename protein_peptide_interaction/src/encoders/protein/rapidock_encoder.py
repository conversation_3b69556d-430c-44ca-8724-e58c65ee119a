#!/usr/bin/env python3
"""
RAPiDock Protein Encoder

Integrates the original RAPiDock protein encoder with ESM embeddings
for protein-peptide interaction prediction.
"""

import os
import sys
import torch
import torch.nn as nn
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
import logging

# Add RAPiDock path
current_dir = os.path.dirname(os.path.abspath(__file__))
rapidock_path = os.path.join(current_dir, "../../../../../RAPiDock")
sys.path.append(rapidock_path)

logger = logging.getLogger(__name__)

class RAPiDockProteinEncoder(nn.Module):
    """
    RAPiDock protein encoder with ESM embeddings.
    
    Integrates the original RAPiDock protein encoding pipeline:
    - ESM sequence embeddings
    - Residue-level features
    - Geometric features
    - Graph neural network processing
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the RAPiDock protein encoder.
        
        Args:
            config: Encoder configuration
        """
        super().__init__()
        self.config = config
        
        # Get configuration sections
        self.residue_config = config.get("residue_encoding", {})
        self.graph_config = config.get("graph", {})
        self.arch_config = config.get("architecture", {})
        
        # Initialize components
        self._init_esm_encoder()
        self._init_residue_encoder()
        self._init_graph_encoder()
        self._init_output_projection()
        
        logger.info("Initialized RAPiDock protein encoder")
    
    def _init_esm_encoder(self):
        """Initialize ESM encoder."""
        esm_config = self.residue_config.get("esm", {})
        
        if esm_config.get("use_pretrained", True):
            try:
                import esm
                
                # Load pre-trained ESM model
                model_name = esm_config.get("model_name", "esm2_t33_650M_UR50D")
                self.esm_model, self.esm_alphabet = esm.pretrained.load_model_and_alphabet(model_name)
                self.esm_batch_converter = self.esm_alphabet.get_batch_converter()
                
                # Freeze ESM weights if specified
                if esm_config.get("freeze_weights", False):
                    for param in self.esm_model.parameters():
                        param.requires_grad = False
                
                self.esm_embedding_dim = esm_config.get("embedding_dim", 1280)
                
                logger.info(f"Loaded ESM model: {model_name}")
                
            except ImportError:
                logger.warning("ESM not available, using random embeddings")
                self.esm_model = None
                self.esm_embedding_dim = esm_config.get("embedding_dim", 1280)
                self.esm_embedding = nn.Embedding(21, self.esm_embedding_dim)  # 20 AA + unknown
        else:
            self.esm_model = None
            self.esm_embedding_dim = esm_config.get("embedding_dim", 1280)
            self.esm_embedding = nn.Embedding(21, self.esm_embedding_dim)
    
    def _init_residue_encoder(self):
        """Initialize residue-level encoder."""
        # Residue type embedding
        residue_config = self.residue_config.get("residue_type", {})
        self.residue_embedding = nn.Embedding(
            residue_config.get("num_types", 21),
            residue_config.get("embedding_dim", 64)
        )
        
        # Geometric feature processor
        geometric_config = self.residue_config.get("geometric", {})
        if geometric_config.get("use_ca_coordinates", True):
            self.geometric_processor = nn.Sequential(
                nn.Linear(3, 32),  # CA coordinates
                nn.ReLU(),
                nn.Linear(32, 64)
            )
        
        # Feature fusion
        total_dim = (
            self.esm_embedding_dim +
            residue_config.get("embedding_dim", 64) +
            (64 if geometric_config.get("use_ca_coordinates", True) else 0)
        )
        
        output_dim = self.residue_config.get("output_dim", 256)
        self.feature_fusion = nn.Sequential(
            nn.Linear(total_dim, output_dim * 2),
            nn.ReLU(),
            nn.Dropout(self.residue_config.get("dropout", 0.1)),
            nn.Linear(output_dim * 2, output_dim)
        )
        
        if self.residue_config.get("use_layer_norm", True):
            self.layer_norm = nn.LayerNorm(output_dim)
    
    def _init_graph_encoder(self):
        """Initialize graph neural network encoder."""
        try:
            # Try to import RAPiDock modules with different possible paths
            rapidock_imported = False

            # Try different import paths
            import_paths = [
                "modules.protein_encoder",
                "RAPiDock.modules.protein_encoder",
                "protein_encoder",
                "src.protein_encoder"
            ]

            for import_path in import_paths:
                try:
                    module = __import__(f"{import_path}", fromlist=["ProteinEncoder"])
                    RAPiDockProteinEncoder = getattr(module, "ProteinEncoder")

                    # Create RAPiDock encoder instance
                    self.rapidock_encoder = RAPiDockProteinEncoder(device="cpu")
                    rapidock_imported = True
                    logger.info(f"Loaded RAPiDock protein encoder from {import_path}")
                    break
                except (ImportError, AttributeError):
                    continue

            if not rapidock_imported:
                raise ImportError("Could not import RAPiDock modules")

        except ImportError:
            logger.warning("RAPiDock modules not available, using simple GNN")
            
            # Fallback to simple graph neural network
            from torch_geometric.nn import GCNConv, global_mean_pool
            
            hidden_dim = self.arch_config.get("graph_conv", {}).get("hidden_dim", 256)
            num_layers = self.arch_config.get("graph_conv", {}).get("num_layers", 4)
            
            self.graph_convs = nn.ModuleList([
                GCNConv(hidden_dim, hidden_dim) for _ in range(num_layers)
            ])
            self.graph_activation = nn.ReLU()
            self.graph_dropout = nn.Dropout(
                self.arch_config.get("graph_conv", {}).get("dropout", 0.1)
            )
    
    def _init_output_projection(self):
        """Initialize output projection layers."""
        output_config = self.arch_config.get("output_projection", {})
        hidden_dims = output_config.get("hidden_dims", [256, 256])
        activation = output_config.get("activation", "relu")
        dropout = output_config.get("dropout", 0.1)
        
        layers = []
        for i in range(len(hidden_dims) - 1):
            layers.extend([
                nn.Linear(hidden_dims[i], hidden_dims[i + 1]),
                nn.ReLU() if activation == "relu" else nn.GELU(),
                nn.Dropout(dropout)
            ])
        
        self.output_projection = nn.Sequential(*layers)
    
    def _get_esm_embeddings(self, sequences: List[str]) -> torch.Tensor:
        """Get ESM embeddings for protein sequences."""
        if self.esm_model is not None:
            # Prepare data for ESM
            data = [(f"protein_{i}", seq) for i, seq in enumerate(sequences)]
            batch_labels, batch_strs, batch_tokens = self.esm_batch_converter(data)
            
            # Move to same device as model
            batch_tokens = batch_tokens.to(next(self.parameters()).device)
            
            # Get embeddings
            with torch.no_grad():
                results = self.esm_model(batch_tokens, repr_layers=[33], return_contacts=False)
                embeddings = results["representations"][33]
            
            # Remove start and end tokens
            embeddings = embeddings[:, 1:-1, :]
            
            return embeddings
        else:
            # Use learned embeddings
            # Convert sequences to indices (simplified)
            aa_to_idx = {aa: i for i, aa in enumerate("ACDEFGHIKLMNPQRSTVWY")}
            aa_to_idx["X"] = 20  # Unknown
            
            batch_indices = []
            max_len = max(len(seq) for seq in sequences)
            
            for seq in sequences:
                indices = [aa_to_idx.get(aa, 20) for aa in seq]
                # Pad sequence
                indices.extend([0] * (max_len - len(indices)))
                batch_indices.append(indices)
            
            batch_indices = torch.tensor(batch_indices, device=next(self.parameters()).device)
            return self.esm_embedding(batch_indices)
    
    def _compute_geometric_features(self, coordinates: torch.Tensor) -> torch.Tensor:
        """Compute geometric features from coordinates."""
        # Simple geometric features (can be enhanced)
        if hasattr(self, "geometric_processor"):
            return self.geometric_processor(coordinates)
        else:
            return torch.zeros(coordinates.shape[0], coordinates.shape[1], 64, 
                             device=coordinates.device)
    
    def _build_residue_graph(self, coordinates: torch.Tensor, 
                           batch_size: int) -> Tuple[torch.Tensor, torch.Tensor]:
        """Build residue-level graph based on distance."""
        edge_config = self.graph_config.get("residue_edges", {})
        distance_cutoff = edge_config.get("distance_cutoff", 10.0)
        max_neighbors = edge_config.get("max_neighbors", 32)
        
        edge_indices = []
        edge_attrs = []
        
        for b in range(batch_size):
            coords = coordinates[b]  # [seq_len, 3]
            
            # Compute pairwise distances
            distances = torch.cdist(coords, coords)
            
            # Find edges within cutoff
            mask = (distances < distance_cutoff) & (distances > 0)
            src, dst = torch.where(mask)
            
            # Limit number of neighbors
            if len(src) > max_neighbors * coords.shape[0]:
                # Keep closest neighbors
                edge_distances = distances[src, dst]
                _, indices = torch.topk(edge_distances, 
                                      min(max_neighbors * coords.shape[0], len(src)), 
                                      largest=False)
                src = src[indices]
                dst = dst[indices]
            
            # Add batch offset
            src = src + b * coords.shape[0]
            dst = dst + b * coords.shape[0]
            
            edge_indices.append(torch.stack([src, dst]))
            
            # Edge attributes (distances)
            edge_attr = distances[src - b * coords.shape[0], dst - b * coords.shape[0]]
            edge_attrs.append(edge_attr)
        
        edge_index = torch.cat(edge_indices, dim=1)
        edge_attr = torch.cat(edge_attrs, dim=0)
        
        return edge_index, edge_attr
    
    def forward(self, 
                sequences: List[str],
                coordinates: Optional[torch.Tensor] = None,
                features: Optional[torch.Tensor] = None,
                masks: Optional[torch.Tensor] = None,
                embedding: Optional[torch.Tensor] = None) -> Dict[str, torch.Tensor]:
        """
        Forward pass of the protein encoder.
        
        Args:
            sequences: List of protein sequences
            coordinates: Protein coordinates [batch, seq_len, 3]
            features: Additional features [batch, seq_len, feat_dim]
            masks: Sequence masks [batch, seq_len]
            embedding: Pre-computed embedding [batch, seq_len, embed_dim]
            
        Returns:
            Dictionary containing encoded features
        """
        # If a pre-computed embedding is provided, use it directly.
        if embedding is not None:
            # The embedding is expected to be of shape [seq_len, embed_dim]
            # We need to unsqueeze it to add the batch dimension -> [1, seq_len, embed_dim]
            if embedding.dim() == 2:
                embedding = embedding.unsqueeze(0)
            return {
                "node_features": embedding,
                "sequence_features": torch.mean(embedding, dim=1)
            }

        batch_size = len(sequences)
        device = next(self.parameters()).device
        
        # Get ESM embeddings
        esm_embeddings = self._get_esm_embeddings(sequences)  # [batch, seq_len, esm_dim]
        
        # Get residue type embeddings
        # Convert sequences to residue indices (simplified)
        aa_to_idx = {aa: i for i, aa in enumerate("ACDEFGHIKLMNPQRSTVWY")}
        aa_to_idx["X"] = 20
        
        max_len = esm_embeddings.shape[1]
        residue_indices = []
        
        for seq in sequences:
            indices = [aa_to_idx.get(aa, 20) for aa in seq[:max_len]]
            # Pad if necessary
            indices.extend([0] * (max_len - len(indices)))
            residue_indices.append(indices)
        
        residue_indices = torch.tensor(residue_indices, device=device)
        residue_embeddings = self.residue_embedding(residue_indices)
        
        # Combine features
        combined_features = [esm_embeddings, residue_embeddings]
        
        # Add geometric features if coordinates provided
        if coordinates is not None:
            geometric_features = self._compute_geometric_features(coordinates)
            combined_features.append(geometric_features)
        
        # Concatenate all features
        node_features = torch.cat(combined_features, dim=-1)
        
        # Apply feature fusion
        node_features = self.feature_fusion(node_features)
        
        if hasattr(self, "layer_norm"):
            node_features = self.layer_norm(node_features)
        
        # Apply masks if provided
        if masks is not None:
            node_features = node_features * masks.unsqueeze(-1)
        
        # Graph processing (if available)
        if hasattr(self, "rapidock_encoder"):
            # Use RAPiDock encoder
            try:
                # This would need adaptation based on RAPiDock's actual interface
                encoded_features = node_features  # Placeholder
            except Exception as e:
                logger.warning(f"RAPiDock encoder failed: {e}")
                encoded_features = node_features
        else:
            # Use simple graph convolution
            if coordinates is not None:
                # Build graph
                edge_index, edge_attr = self._build_residue_graph(coordinates, batch_size)
                
                # Flatten for graph processing
                flat_features = node_features.view(-1, node_features.shape[-1])
                
                # Apply graph convolutions
                x = flat_features
                for conv in self.graph_convs:
                    x = conv(x, edge_index)
                    x = self.graph_activation(x)
                    x = self.graph_dropout(x)
                
                # Reshape back
                encoded_features = x.view(batch_size, max_len, -1)
            else:
                encoded_features = node_features
        
        # Apply output projection
        output_features = self.output_projection(encoded_features)
        
        return {
            "node_features": output_features,
            "sequence_features": torch.mean(output_features, dim=1),  # Global representation
            "esm_embeddings": esm_embeddings,
            "residue_embeddings": residue_embeddings,
            "masks": masks
        }
