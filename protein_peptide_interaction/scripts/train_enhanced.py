#!/usr/bin/env python3
"""
Enhanced Training Script for Protein-Peptide Interaction Model

Enhanced version with better error handling, debugging, and user experience.
"""

import os
import sys
import argparse
import logging
import torch
import torch.distributed as dist
from pathlib import Path
import traceback
import time
import signal
import json

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.config import ConfigManager
from src.training import Trainer
from src.utils import GPUManager

class TrainingSession:
    """Enhanced training session with better error handling."""
    
    def __init__(self, args):
        self.args = args
        self.config = None
        self.trainer = None
        self.start_time = None
        self.setup_signal_handlers()
    
    def setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown."""
        def signal_handler(signum, frame):
            logging.info(f"Received signal {signum}, shutting down gracefully...")
            if self.trainer:
                self.trainer.save_checkpoint("emergency_checkpoint.pth", is_best=False)
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    def setup_logging(self):
        """Enhanced logging setup."""
        log_level = getattr(logging, self.args.log_level.upper())
        
        # Create logs directory
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        # Setup formatters
        detailed_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s'
        )
        simple_formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s'
        )
        
        # Setup handlers
        handlers = []
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(log_level)
        console_handler.setFormatter(simple_formatter if not self.args.debug else detailed_formatter)
        handlers.append(console_handler)
        
        # File handler
        log_file = log_dir / f"training_{time.strftime('%Y%m%d_%H%M%S')}.log"
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(detailed_formatter)
        handlers.append(file_handler)
        
        # Error file handler
        error_file = log_dir / f"training_errors_{time.strftime('%Y%m%d_%H%M%S')}.log"
        error_handler = logging.FileHandler(error_file)
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(detailed_formatter)
        handlers.append(error_handler)
        
        # Configure root logger
        logging.basicConfig(
            level=log_level,
            handlers=handlers,
            force=True
        )
        
        logging.info(f"Logging setup completed - Log file: {log_file}")
    
    def validate_environment(self):
        """Validate the training environment."""
        logger = logging.getLogger(__name__)
        
        # Check Python version
        if sys.version_info < (3, 8):
            raise RuntimeError(f"Python 3.8+ required, got {sys.version}")
        
        # Check PyTorch
        logger.info(f"PyTorch version: {torch.__version__}")
        
        # Check CUDA
        if torch.cuda.is_available():
            logger.info(f"CUDA version: {torch.version.cuda}")
            logger.info(f"Available GPUs: {torch.cuda.device_count()}")
            for i in range(torch.cuda.device_count()):
                props = torch.cuda.get_device_properties(i)
                logger.info(f"GPU {i}: {props.name} ({props.total_memory / 1024**3:.1f} GB)")
        else:
            logger.warning("CUDA not available, training will use CPU")
        
        # Check disk space
        import shutil
        free_space = shutil.disk_usage('.').free / 1024**3
        if free_space < 1.0:  # Less than 1GB
            logger.warning(f"Low disk space: {free_space:.1f} GB available")
        
        # Check memory
        import psutil
        memory = psutil.virtual_memory()
        logger.info(f"System memory: {memory.total / 1024**3:.1f} GB total, "
                   f"{memory.available / 1024**3:.1f} GB available")
        
        if memory.percent > 90:
            logger.warning(f"High memory usage: {memory.percent:.1f}%")
    
    def load_and_validate_config(self):
        """Load and validate configuration with detailed error reporting."""
        logger = logging.getLogger(__name__)
        
        try:
            config_manager = ConfigManager()
            
            if self.args.config.endswith('.yaml'):
                self.config = config_manager.load_config(self.args.config)
            else:
                # Build configuration from components
                self.config = config_manager.build_full_config(
                    base_config="default.yaml",
                    protein_encoder=self.args.protein_encoder or "rapidock",
                    peptide_encoder=self.args.peptide_encoder or "rapidock",
                    model=self.args.model or "gin",
                    training_type="multi_gpu" if self.is_distributed else "single_gpu"
                )
            
            # Override with command line arguments
            self.config = self.override_config_with_args()
            
            # Validate configuration
            self.validate_config()
            
            # Setup module paths
            config_manager.setup_module_paths()
            
            logger.info("Configuration loaded and validated successfully")
            
        except FileNotFoundError as e:
            logger.error(f"Configuration file not found: {e}")
            self.suggest_config_fix()
            raise
        except Exception as e:
            logger.error(f"Configuration error: {e}")
            if self.args.debug:
                traceback.print_exc()
            raise
    
    def suggest_config_fix(self):
        """Suggest fixes for common configuration issues."""
        logger = logging.getLogger(__name__)
        
        logger.info("Configuration troubleshooting suggestions:")
        logger.info("1. Check if the config file path is correct")
        logger.info("2. Verify the config file format (YAML)")
        logger.info("3. Use --dry-run to test configuration without training")
        logger.info("4. Try using default configuration: --config configs/default.yaml")
    
    def override_config_with_args(self):
        """Override configuration with command line arguments."""
        config = self.config.copy()
        
        # Model overrides
        if self.args.protein_encoder:
            config["encoders"]["protein"]["type"] = self.args.protein_encoder
        
        if self.args.peptide_encoder:
            config["encoders"]["peptide"]["type"] = self.args.peptide_encoder
        
        if self.args.model:
            config["model"]["type"] = self.args.model
        
        # Training overrides
        if self.args.batch_size:
            config["training"]["batch_size"] = self.args.batch_size
        
        if self.args.learning_rate:
            config["training"]["learning_rate"] = self.args.learning_rate
        
        if self.args.num_epochs:
            config["training"]["num_epochs"] = self.args.num_epochs
        
        # Hardware overrides
        if self.args.device:
            config["hardware"]["device"] = self.args.device
        
        if self.args.num_gpus:
            config["hardware"]["num_gpus"] = self.args.num_gpus
        
        if self.args.mixed_precision:
            config["hardware"]["mixed_precision"] = True
        elif self.args.no_mixed_precision:
            config["hardware"]["mixed_precision"] = False
        
        # Logging overrides
        if self.args.wandb:
            config["logging"]["wandb"]["enabled"] = True
        
        if self.args.tensorboard:
            config["logging"]["tensorboard"]["enabled"] = True
        
        # Checkpointing overrides
        if self.args.checkpoint_dir:
            config["checkpointing"]["save_dir"] = self.args.checkpoint_dir
        
        return config
    
    def validate_config(self):
        """Validate configuration with detailed error messages."""
        logger = logging.getLogger(__name__)
        
        required_sections = ["encoders", "model", "training", "data"]
        
        for section in required_sections:
            if section not in self.config:
                raise ValueError(f"Missing required configuration section: {section}")
        
        # Validate encoder types
        try:
            protein_encoder = self.config["encoders"]["protein"]["type"]
            peptide_encoder = self.config["encoders"]["peptide"]["type"]
            model_type = self.config["model"]["type"]
        except KeyError as e:
            raise ValueError(f"Missing configuration key: {e}")
        
        valid_protein_encoders = ["rapidock", "alphafold2"]
        valid_peptide_encoders = ["rapidock", "atomref", "dmasif", "vqvae"]
        valid_models = ["gin", "quinnet"]
        
        if protein_encoder not in valid_protein_encoders:
            logger.error(f"Invalid protein encoder: {protein_encoder}")
            logger.info(f"Valid options: {valid_protein_encoders}")
            raise ValueError(f"Invalid protein encoder: {protein_encoder}")
        
        if peptide_encoder not in valid_peptide_encoders:
            logger.error(f"Invalid peptide encoder: {peptide_encoder}")
            logger.info(f"Valid options: {valid_peptide_encoders}")
            raise ValueError(f"Invalid peptide encoder: {peptide_encoder}")
        
        if model_type not in valid_models:
            logger.error(f"Invalid model type: {model_type}")
            logger.info(f"Valid options: {valid_models}")
            raise ValueError(f"Invalid model type: {model_type}")
        
        logger.info("Configuration validation passed")
    
    def setup_debug_mode(self):
        """Setup debug mode with reduced parameters."""
        if self.args.debug:
            logger = logging.getLogger(__name__)
            
            # Reduce training parameters for faster debugging
            self.config["training"]["num_epochs"] = 2
            self.config["training"]["batch_size"] = min(self.config["training"]["batch_size"], 4)
            
            # Enable detailed logging
            self.config["logging"]["level"] = "DEBUG"
            
            # Reduce dataset size if possible
            if "dummy_samples" in self.config.get("data", {}):
                self.config["data"]["dummy_samples"] = 5
            
            logger.info("Debug mode enabled - reduced parameters for faster testing")
            logger.info(f"Epochs: {self.config['training']['num_epochs']}")
            logger.info(f"Batch size: {self.config['training']['batch_size']}")
    
    def run_dry_run(self):
        """Perform dry run validation."""
        logger = logging.getLogger(__name__)
        
        logger.info("=== DRY RUN MODE ===")
        logger.info("Validating configuration and setup without actual training")
        
        # Print configuration summary
        logger.info("Configuration Summary:")
        logger.info(f"  Protein Encoder: {self.config['encoders']['protein']['type']}")
        logger.info(f"  Peptide Encoder: {self.config['encoders']['peptide']['type']}")
        logger.info(f"  Model: {self.config['model']['type']}")
        logger.info(f"  Batch Size: {self.config['training']['batch_size']}")
        logger.info(f"  Learning Rate: {self.config['training']['learning_rate']}")
        logger.info(f"  Epochs: {self.config['training']['num_epochs']}")
        
        # Test GPU setup
        try:
            gpu_manager = GPUManager(self.config.get("hardware", {}))
            gpu_info = gpu_manager.get_gpu_info()
            logger.info(f"GPU Setup: {gpu_info['num_gpus']} GPUs available")
        except Exception as e:
            logger.error(f"GPU setup failed: {e}")
        
        # Test model initialization
        try:
            logger.info("Testing model initialization...")
            # This would test model creation without actually creating it
            logger.info("Model initialization test passed")
        except Exception as e:
            logger.error(f"Model initialization test failed: {e}")
        
        logger.info("=== DRY RUN COMPLETED ===")
        logger.info("Configuration is valid. Remove --dry-run to start actual training.")
    
    def run_training(self):
        """Run the actual training."""
        logger = logging.getLogger(__name__)
        
        try:
            # Initialize GPU manager
            gpu_manager = GPUManager(self.config.get("hardware", {}))
            gpu_info = gpu_manager.get_gpu_info()
            logger.info(f"GPU Info: {gpu_info}")
            
            # Initialize trainer
            logger.info("Initializing trainer...")
            self.trainer = Trainer(self.config, resume_from=self.args.resume)
            
            # Setup profiling if requested
            if self.args.profile:
                logger.info("Profiling mode enabled")
                self.config["profiling"] = {"enabled": True}
            
            # Start training
            logger.info("Starting training...")
            self.start_time = time.time()
            
            self.trainer.train()
            
            # Calculate training time
            training_time = time.time() - self.start_time
            logger.info(f"Training completed successfully in {training_time:.2f} seconds!")
            
        except KeyboardInterrupt:
            logger.info("Training interrupted by user")
            if self.trainer:
                logger.info("Saving emergency checkpoint...")
                self.trainer.save_checkpoint("interrupted_checkpoint.pth", is_best=False)
            raise
        except Exception as e:
            logger.error(f"Training failed: {e}")
            if self.args.debug:
                logger.error("Full traceback:")
                traceback.print_exc()
            
            # Save debug information
            self.save_debug_info(e)
            raise
    
    def save_debug_info(self, error):
        """Save debug information for troubleshooting."""
        debug_info = {
            "error": str(error),
            "traceback": traceback.format_exc(),
            "config": self.config,
            "args": vars(self.args),
            "system_info": {
                "python_version": sys.version,
                "pytorch_version": torch.__version__,
                "cuda_available": torch.cuda.is_available(),
                "cuda_version": torch.version.cuda if torch.cuda.is_available() else None,
            }
        }
        
        debug_file = f"debug_info_{time.strftime('%Y%m%d_%H%M%S')}.json"
        with open(debug_file, 'w') as f:
            json.dump(debug_info, f, indent=2, default=str)
        
        logging.error(f"Debug information saved to {debug_file}")


def parse_arguments():
    """Parse command line arguments with enhanced help."""
    parser = argparse.ArgumentParser(
        description="Enhanced Training Script for Protein-Peptide Interaction Model",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Basic training
  python scripts/train_enhanced.py --config configs/default.yaml
  
  # Debug mode with reduced parameters
  python scripts/train_enhanced.py --config configs/default.yaml --debug
  
  # Dry run to validate configuration
  python scripts/train_enhanced.py --config configs/default.yaml --dry-run
  
  # Custom model configuration
  python scripts/train_enhanced.py --protein-encoder alphafold2 --peptide-encoder atomref --model gin
        """
    )
    
    # Configuration
    parser.add_argument("--config", type=str, default="configs/default.yaml",
                       help="Path to configuration file")
    
    # Model configuration
    parser.add_argument("--protein-encoder", type=str, choices=["rapidock", "alphafold2"],
                       help="Protein encoder type")
    parser.add_argument("--peptide-encoder", type=str, choices=["rapidock", "atomref", "dmasif", "vqvae"],
                       help="Peptide encoder type")
    parser.add_argument("--model", type=str, choices=["gin", "quinnet"],
                       help="Interaction model type")
    
    # Training configuration
    parser.add_argument("--batch-size", type=int, help="Batch size")
    parser.add_argument("--learning-rate", type=float, help="Learning rate")
    parser.add_argument("--num-epochs", type=int, help="Number of epochs")
    
    # Hardware configuration
    parser.add_argument("--device", type=str, choices=["auto", "cpu", "cuda"], default="auto",
                       help="Device to use for training")
    parser.add_argument("--num-gpus", type=int, help="Number of GPUs to use")
    parser.add_argument("--mixed-precision", action="store_true", help="Enable mixed precision training")
    parser.add_argument("--no-mixed-precision", action="store_true", help="Disable mixed precision training")
    
    # Checkpointing
    parser.add_argument("--resume", type=str, help="Path to checkpoint to resume from")
    parser.add_argument("--checkpoint-dir", type=str, default="checkpoints",
                       help="Directory to save checkpoints")
    
    # Logging
    parser.add_argument("--log-level", type=str, choices=["DEBUG", "INFO", "WARNING", "ERROR"],
                       default="INFO", help="Logging level")
    parser.add_argument("--wandb", action="store_true", help="Enable Weights & Biases logging")
    parser.add_argument("--tensorboard", action="store_true", help="Enable TensorBoard logging")
    
    # Debug options
    parser.add_argument("--debug", action="store_true", help="Enable debug mode with reduced parameters")
    parser.add_argument("--profile", action="store_true", help="Enable profiling")
    parser.add_argument("--dry-run", action="store_true", help="Validate configuration without training")
    
    # Distributed training
    parser.add_argument("--local_rank", type=int, default=0, help="Local rank for distributed training")
    
    return parser.parse_args()


def setup_distributed_training():
    """Setup distributed training environment."""
    if "RANK" not in os.environ or "WORLD_SIZE" not in os.environ:
        return False, 0, 1, 0
    
    rank = int(os.environ["RANK"])
    world_size = int(os.environ["WORLD_SIZE"])
    local_rank = int(os.environ.get("LOCAL_RANK", 0))
    
    # Initialize process group
    dist.init_process_group(backend="nccl")
    
    # Set device
    torch.cuda.set_device(local_rank)
    
    return True, rank, world_size, local_rank


def main():
    """Enhanced main function with comprehensive error handling."""
    args = parse_arguments()
    
    # Create training session
    session = TrainingSession(args)
    
    try:
        # Setup logging
        session.setup_logging()
        logger = logging.getLogger(__name__)
        
        logger.info("=== Enhanced Training Script Started ===")
        logger.info(f"Arguments: {vars(args)}")
        
        # Validate environment
        session.validate_environment()
        
        # Setup distributed training
        session.is_distributed, rank, world_size, local_rank = setup_distributed_training()
        
        if session.is_distributed:
            logger.info(f"Distributed training - Rank: {rank}, World size: {world_size}")
        
        # Load and validate configuration
        session.load_and_validate_config()
        
        # Setup debug mode
        session.setup_debug_mode()
        
        # Dry run mode
        if args.dry_run:
            session.run_dry_run()
            return
        
        # Run training
        session.run_training()
        
        logger.info("=== Training Session Completed Successfully ===")
        
    except KeyboardInterrupt:
        logger.info("Training interrupted by user")
        sys.exit(130)  # Standard exit code for Ctrl+C
    except Exception as e:
        logger.error(f"Training session failed: {e}")
        sys.exit(1)
    finally:
        # Cleanup distributed training
        if session.is_distributed:
            dist.destroy_process_group()


if __name__ == "__main__":
    main()