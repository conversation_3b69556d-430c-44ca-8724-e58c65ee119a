#!/usr/bin/env python3
"""
Training Script for Protein-Peptide Interaction Model

Main script for training the protein-peptide interaction prediction model
with support for single and multi-GPU training.
"""

import os
import sys
import argparse
import logging
import torch
import torch.distributed as dist
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.config import ConfigManager
from src.training import Trainer
from src.utils import GPUManager

def setup_logging(log_level: str = "INFO"):
    """Setup logging configuration."""
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('training.log')
        ]
    )

def validate_environment():
    """Validate the training environment."""
    logger = logging.getLogger(__name__)
    logger.info("Validating environment...")
    
    # Check Python version
    if sys.version_info < (3, 8):
        raise RuntimeError(f"Python 3.8+ required, got {sys.version}")
    
    # Check PyTorch
    logger.info(f"PyTorch version: {torch.__version__}")
    
    # Check CUDA
    if torch.cuda.is_available():
        logger.info(f"CUDA version: {torch.version.cuda}")
        logger.info(f"Available GPUs: {torch.cuda.device_count()}")
        for i in range(torch.cuda.device_count()):
            props = torch.cuda.get_device_properties(i)
            logger.info(f"GPU {i}: {props.name} ({props.total_memory / 1024**3:.1f} GB)")
    else:
        logger.warning("CUDA not available, training will use CPU")
    
    # Check disk space
    try:
        import shutil
        free_space = shutil.disk_usage('.').free / 1024**3
        if free_space < 1.0:  # Less than 1GB
            logger.warning(f"Low disk space: {free_space:.1f} GB available")
    except ImportError:
        logger.warning("Could not import 'shutil' to check disk space.")

    # Check memory
    try:
        import psutil
        memory = psutil.virtual_memory()
        logger.info(f"System memory: {memory.total / 1024**3:.1f} GB total, "
                   f"{memory.available / 1024**3:.1f} GB available")
        if memory.percent > 90:
            logger.warning(f"High memory usage: {memory.percent:.1f}%")
    except ImportError:
        logger.warning("Could not import 'psutil' to check memory usage.")
    logger.info("Environment validation complete.")

def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Train Protein-Peptide Interaction Model")
    
    # Configuration
    parser.add_argument(
        "--config", 
        type=str, 
        default="default.yaml",
        help="Path to configuration file"
    )
    
    # Model configuration
    parser.add_argument(
        "--protein-encoder",
        type=str,
        choices=["rapidock", "alphafold2"],
        help="Protein encoder type (overrides config)"
    )
    
    parser.add_argument(
        "--peptide-encoder",
        type=str,
        choices=["rapidock", "atomref", "dmasif", "vqvae"],
        help="Peptide encoder type (overrides config)"
    )
    
    parser.add_argument(
        "--model",
        type=str,
        choices=["gin", "quinnet"],
        help="Interaction model type (overrides config)"
    )
    
    # Training configuration
    parser.add_argument(
        "--batch-size",
        type=int,
        help="Batch size (overrides config)"
    )
    
    parser.add_argument(
        "--learning-rate",
        type=float,
        help="Learning rate (overrides config)"
    )
    
    parser.add_argument(
        "--num-epochs",
        type=int,
        help="Number of epochs (overrides config)"
    )
    
    # Hardware configuration
    parser.add_argument(
        "--device",
        type=str,
        choices=["auto", "cpu", "cuda"],
        default="auto",
        help="Device to use for training"
    )
    
    parser.add_argument(
        "--num-gpus",
        type=int,
        help="Number of GPUs to use (overrides config)"
    )
    
    parser.add_argument(
        "--mixed-precision",
        action="store_true",
        help="Enable mixed precision training"
    )
    
    parser.add_argument(
        "--no-mixed-precision",
        action="store_true",
        help="Disable mixed precision training"
    )
    
    # Checkpointing
    parser.add_argument(
        "--resume",
        type=str,
        help="Path to checkpoint to resume from"
    )
    
    parser.add_argument(
        "--checkpoint-dir",
        type=str,
        default="checkpoints",
        help="Directory to save checkpoints"
    )
    
    # Logging
    parser.add_argument(
        "--log-level",
        type=str,
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Logging level"
    )
    
    parser.add_argument(
        "--wandb",
        action="store_true",
        help="Enable Weights & Biases logging"
    )
    
    parser.add_argument(
        "--tensorboard",
        action="store_true",
        help="Enable TensorBoard logging"
    )
    
    # Distributed training
    parser.add_argument(
        "--local_rank",
        type=int,
        default=0,
        help="Local rank for distributed training"
    )
    
    # Debug options
    parser.add_argument(
        "--debug",
        action="store_true",
        help="Enable debug mode"
    )
    
    parser.add_argument(
        "--profile",
        action="store_true",
        help="Enable profiling"
    )
    
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Perform dry run without actual training"
    )
    
    return parser.parse_args()

def override_config(config: dict, args: argparse.Namespace) -> dict:
    """Override configuration with command line arguments."""
    # Model overrides
    if args.protein_encoder:
        config["encoders"]["protein"]["type"] = args.protein_encoder
    
    if args.peptide_encoder:
        config["encoders"]["peptide"]["type"] = args.peptide_encoder
    
    if args.model:
        config["model"]["type"] = args.model
    
    # Training overrides
    if args.batch_size:
        config["training"]["batch_size"] = args.batch_size
    
    if args.learning_rate:
        config["training"]["learning_rate"] = args.learning_rate
    
    if args.num_epochs:
        config["training"]["num_epochs"] = args.num_epochs
    
    # Hardware overrides
    if args.device:
        config["hardware"]["device"] = args.device
    
    if args.num_gpus:
        config["hardware"]["num_gpus"] = args.num_gpus
    
    if args.mixed_precision:
        config["hardware"]["mixed_precision"] = True
    elif args.no_mixed_precision:
        config["hardware"]["mixed_precision"] = False
    
    # Logging overrides
    if args.wandb:
        config["logging"]["wandb"]["enabled"] = True
    
    if args.tensorboard:
        config["logging"]["tensorboard"]["enabled"] = True
    
    # Checkpointing overrides
    if args.checkpoint_dir:
        config["checkpointing"]["save_dir"] = args.checkpoint_dir
    
    return config

def setup_distributed_training():
    """Setup distributed training environment."""
    if "RANK" in os.environ and "WORLD_SIZE" in os.environ:
        rank = int(os.environ["RANK"])
        world_size = int(os.environ["WORLD_SIZE"])
        local_rank = int(os.environ.get("LOCAL_RANK", 0))
        
        # Initialize process group
        dist.init_process_group(backend="nccl")
        
        # Set device
        torch.cuda.set_device(local_rank)
        
        return True, rank, world_size, local_rank
    
    return False, 0, 1, 0

def validate_config(config: dict):
    """Validate configuration."""
    required_sections = ["encoders", "model", "training", "data"]
    
    for section in required_sections:
        if section not in config:
            raise ValueError(f"Missing required configuration section: {section}")
    
    # Validate encoder types
    protein_encoder = config["encoders"]["protein"]["type"]
    peptide_encoder = config["encoders"]["peptide"]["type"]
    model_type = config["model"]["type"]
    
    valid_protein_encoders = ["rapidock", "rapidock_features", "alphafold2"]
    valid_peptide_encoders = ["rapidock", "rapidock_features", "atomref", "dmasif", "vqvae"]
    valid_models = ["gin", "quinnet", "rapidock_interaction", "rapidock_diffusion"]
    
    if protein_encoder not in valid_protein_encoders:
        raise ValueError(f"Invalid protein encoder: {protein_encoder}")
    
    if peptide_encoder not in valid_peptide_encoders:
        raise ValueError(f"Invalid peptide encoder: {peptide_encoder}")
    
    if model_type not in valid_models:
        raise ValueError(f"Invalid model type: {model_type}")

def main():
    """Main training function."""
    # Parse arguments
    args = parse_arguments()
    
    # Setup logging
    setup_logging(args.log_level)
    logger = logging.getLogger(__name__)
    
    # Validate environment at the beginning
    validate_environment()

    # Setup distributed training
    is_distributed, rank, world_size, local_rank = setup_distributed_training()
    
    if is_distributed:
        logger.info(f"Distributed training - Rank: {rank}, World size: {world_size}")
    
    try:
        # Load configuration
        config_manager = ConfigManager()
        
        if args.config.endswith('.yaml'):
            config = config_manager.load_config(args.config)
        else:
            # Build configuration from components
            config = config_manager.build_full_config(
                base_config="default.yaml",
                protein_encoder=args.protein_encoder or "rapidock",
                peptide_encoder=args.peptide_encoder or "rapidock",
                model=args.model or "gin",
                training_type="multi_gpu" if is_distributed else "single_gpu"
            )
        
        # Override with command line arguments
        config = override_config(config, args)
        
        # DEBUG: Check the type and content of datasets config
        if 'data' in config and 'datasets' in config['data']:
            logger.debug(f"[train.py] Type of config.data.datasets: {type(config.data.datasets)}")
            logger.debug(f"[train.py] Content of config.data.datasets: {config.data.datasets}")
        else:
            logger.debug("[train.py] config.data.datasets not found after override.")

        # Validate configuration
        validate_config(config)
        
        # Setup module paths
        config_manager.setup_module_paths()
        
        # Debug mode
        if args.debug:
            config["logging"]["level"] = "DEBUG"
            config["training"]["num_epochs"] = 2
            config["training"]["batch_size"] = min(config["training"]["batch_size"], 4)
            logger.info("Debug mode enabled")
        
        # Dry run
        if args.dry_run:
            logger.info("Dry run mode - configuration validation completed")
            logger.info(f"Configuration: {config}")
            return
        
        # Initialize GPU manager
        gpu_manager = GPUManager(config.get("hardware", {}))
        gpu_info = gpu_manager.get_gpu_info()
        logger.info(f"GPU Info: {gpu_info}")
        
        # Initialize trainer
        trainer = Trainer(config, resume_from=args.resume)
        
        # Profiling mode
        if args.profile:
            logger.info("Profiling mode enabled")
            # Add profiling configuration
            config["profiling"] = {"enabled": True}
        
        # Start training
        logger.info("Starting training...")
        trainer.train()
        
        logger.info("Training completed successfully!")
        
    except Exception as e:
        logger.error(f"Training failed: {e}")
        if args.debug:
            import traceback
            traceback.print_exc()
        sys.exit(1)
    
    finally:
        # Cleanup
        if is_distributed:
            dist.destroy_process_group()

if __name__ == "__main__":
    main()