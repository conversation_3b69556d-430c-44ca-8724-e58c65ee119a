{"cells": [{"cell_type": "markdown", "metadata": {"id": "Ts8Kh2P1U2Th"}, "source": ["##Important Notes:\n", "If you want to get ProtT5 embedding through Colab, you may need to upgrade Colab Pro.\n", "1. ProtT5-XL-UniRef50 has both encoder and decoder, for feature extraction we only load and use the encoder part.\n", "2. Loading only the encoder part, reduces the inference speed and the GPU memory requirements by half.\n", "3. In order to use ProtT5-XL-UniRef50 encoder, you must install the latest huggingface transformers version from their GitHub repo.\n", "4. If you are intersted in both the encoder and decoder, you should use TFT5Model rather than TFT5EncoderModel.\n", "5. CPU is also allowed if you do not have GPUs. (About 20GB memory)"]}, {"cell_type": "markdown", "metadata": {"id": "LQcCEtrC6Udr"}, "source": ["<h3>Extracting protein sequences' features using ProtT5-XL-UniRef50 pretrained-model</h3>"]}, {"cell_type": "markdown", "metadata": {"id": "9wCEAM9F5wTA"}, "source": ["\n", "**1. Load necessry libraries including huggingface transformers**"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "GXAKFATm-mbs", "outputId": "d1f0cf69-ba5e-421f-e19d-ec7c8e58932f"}, "outputs": [], "source": ["!pip install -q SentencePiece transformers"]}, {"cell_type": "code", "execution_count": 71, "metadata": {"id": "0UhF4ih050mu"}, "outputs": [], "source": ["import torch\n", "from transformers import TFT5EncoderModel, T5Tokenizer, T5EncoderModel\n", "import numpy as np\n", "import re\n", "import gc"]}, {"cell_type": "markdown", "metadata": {"id": "hAKCMu_2-h2V"}, "source": ["<b>2. <PERSON>ad the vocabulary and ProtT5-XL-UniRef50 Model<b>"]}, {"cell_type": "code", "execution_count": 72, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 164, "referenced_widgets": ["625c498af5e54243a74d0e1442d76448", "d5348e015a184d4db05dc48e1581ef44", "856432cdffae474d8e8c6fafd79a3937", "a8b5fc22711745f99e61bed9e834872e", "ff459b2aa7114caca0d1ef25bf6e1790", "e2a70fb106874689be06dacfa4d6d306", "763e98c23a574dc2bf451adbbc58a9a4", "ad3d15cf7b07413588c5f3d80288288a", "b21c919fb3944acf815c975826688eca", "01b49e8c145b4a8bae89d8430e3fbdc6", "d3e4ec9dec5a471881c66ea6f32a142b", "a943933f18a64c6d96a0248a5ce9bfd6", "7e2c2223c67042f9ba2a1b62ac908f18", "e91f71f474b74f39a5fb1155741a8d74", "a79c843eaaeb4df8a46f7c077a09899f", "662dd8a497d64ee8b310197e72d66977", "650ed09b93e84503a78917000039254a", "947c7ac18bae4d72a942fe114df012d9", "06c281c8f6b54433beeee404a0cdcc69", "32856cc01ae248b98dc4527034ae3398", "98ef6cdfca424b74b0f8cbc51630e0a8", "88ab217893134f6997a317264c676064", "203fa60c4f4a49a38b9aaab941c863e9", "255336e889924999b3378102cc007c95"]}, "id": "HS8i5sOJ-h2W", "outputId": "9a4e394b-ea05-434e-cc8b-b762f16287f8"}, "outputs": [], "source": ["tokenizer = T5Tokenizer.from_pretrained(\"Rostlab/prot_t5_xl_uniref50\", do_lower_case=False )"]}, {"cell_type": "code", "execution_count": 21, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 220, "referenced_widgets": ["6039c46157134a998e9e2b1ccec25b63", "71af1636b5f840a2a07324812235db10", "8b06d496d4704beebea534ddb5ad2330", "e699ece0feca440387d082c2b233b58d", "b056a2433f154dbaa41f6840015ecf87", "19b60494fe2443698785e34808f2027e", "7a63fb9f3a2d4fc3b74c85fb7fcbcf9f", "039fedc16a9e417c94beea59f5561d1a", "1a4ffa8252bb4b5584ad7bdd581cb6da", "ebac9c962e824d7794c40ac4d0332b89", "982e1e391da849d992f28a7c8ea473fb", "5494f8dece5c4a10b5f6af426a760067", "d885ef1286954116aabbcdca34303124", "8cde50d61e224d50aaffb55ad8323680", "04585a7bab58488990342e4081dfdd51", "b7dfd3ea823c46a9b65bd8cc7bad793d"]}, "id": "ERtkR05t-h2c", "outputId": "4c051838-eb47-430a-a738-3adfecdfd7a5"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Some weights of the PyTorch model were not used when initializing the TF 2.0 model TFT5EncoderModel: ['decoder.block.5.layer.0.SelfAttention.v.weight', 'decoder.block.1.layer.0.SelfAttention.q.weight', 'decoder.block.12.layer.0.SelfAttention.o.weight', 'decoder.embed_tokens.weight', 'decoder.block.22.layer.0.SelfAttention.q.weight', 'decoder.block.6.layer.0.layer_norm.weight', 'decoder.block.18.layer.0.SelfAttention.q.weight', 'decoder.block.6.layer.1.EncDecAttention.o.weight', 'decoder.block.23.layer.0.SelfAttention.q.weight', 'decoder.block.14.layer.0.SelfAttention.k.weight', 'decoder.block.7.layer.1.EncDecAttention.o.weight', 'decoder.block.20.layer.1.EncDecAttention.v.weight', 'decoder.block.20.layer.2.DenseReluDense.wi.weight', 'decoder.block.21.layer.1.layer_norm.weight', 'decoder.block.22.layer.1.EncDecAttention.o.weight', 'decoder.block.9.layer.2.layer_norm.weight', 'decoder.block.0.layer.0.SelfAttention.o.weight', 'decoder.block.15.layer.1.EncDecAttention.k.weight', 'decoder.block.7.layer.1.layer_norm.weight', 'decoder.block.5.layer.1.EncDecAttention.v.weight', 'decoder.block.7.layer.0.SelfAttention.k.weight', 'decoder.block.22.layer.0.SelfAttention.k.weight', 'decoder.block.7.layer.2.layer_norm.weight', 'decoder.block.3.layer.2.DenseReluDense.wi.weight', 'decoder.block.7.layer.1.EncDecAttention.v.weight', 'decoder.block.9.layer.1.EncDecAttention.v.weight', 'decoder.block.11.layer.0.SelfAttention.k.weight', 'decoder.block.2.layer.0.SelfAttention.v.weight', 'decoder.block.10.layer.1.layer_norm.weight', 'decoder.block.7.layer.1.EncDecAttention.q.weight', 'decoder.block.8.layer.1.layer_norm.weight', 'decoder.block.0.layer.0.layer_norm.weight', 'decoder.block.7.layer.0.SelfAttention.q.weight', 'decoder.block.3.layer.2.layer_norm.weight', 'decoder.block.4.layer.1.EncDecAttention.k.weight', 'decoder.block.4.layer.2.DenseReluDense.wo.weight', 'decoder.block.9.layer.0.layer_norm.weight', 'decoder.block.19.layer.0.SelfAttention.q.weight', 'decoder.block.13.layer.0.SelfAttention.k.weight', 'decoder.block.21.layer.1.EncDecAttention.k.weight', 'decoder.block.6.layer.2.DenseReluDense.wo.weight', 'decoder.block.12.layer.0.SelfAttention.k.weight', 'decoder.block.14.layer.2.DenseReluDense.wo.weight', 'decoder.block.18.layer.1.EncDecAttention.k.weight', 'decoder.block.9.layer.0.SelfAttention.k.weight', 'decoder.block.19.layer.1.layer_norm.weight', 'decoder.block.19.layer.0.SelfAttention.o.weight', 'decoder.block.8.layer.0.SelfAttention.k.weight', 'decoder.block.5.layer.2.DenseReluDense.wi.weight', 'decoder.block.0.layer.2.DenseReluDense.wo.weight', 'decoder.block.6.layer.2.layer_norm.weight', 'decoder.block.12.layer.1.layer_norm.weight', 'decoder.block.16.layer.0.SelfAttention.v.weight', 'decoder.block.5.layer.1.EncDecAttention.q.weight', 'decoder.block.2.layer.0.SelfAttention.o.weight', 'decoder.block.13.layer.1.EncDecAttention.v.weight', 'decoder.block.5.layer.0.SelfAttention.q.weight', 'decoder.block.1.layer.1.EncDecAttention.k.weight', 'decoder.block.11.layer.1.layer_norm.weight', 'decoder.block.2.layer.1.EncDecAttention.k.weight', 'decoder.block.3.layer.1.EncDecAttention.k.weight', 'decoder.block.4.layer.0.SelfAttention.v.weight', 'decoder.block.16.layer.2.layer_norm.weight', 'decoder.block.9.layer.1.EncDecAttention.q.weight', 'encoder.embed_tokens.weight', 'decoder.block.8.layer.0.SelfAttention.v.weight', 'decoder.block.19.layer.0.SelfAttention.k.weight', 'decoder.block.23.layer.1.EncDecAttention.v.weight', 'decoder.block.22.layer.2.DenseReluDense.wi.weight', 'decoder.block.16.layer.1.EncDecAttention.q.weight', 'decoder.block.22.layer.0.SelfAttention.v.weight', 'decoder.block.2.layer.2.DenseReluDense.wo.weight', 'lm_head.weight', 'decoder.block.8.layer.0.SelfAttention.o.weight', 'decoder.block.23.layer.0.layer_norm.weight', 'decoder.block.13.layer.2.DenseReluDense.wi.weight', 'decoder.block.0.layer.1.EncDecAttention.q.weight', 'decoder.block.9.layer.2.DenseReluDense.wi.weight', 'decoder.block.4.layer.1.EncDecAttention.q.weight', 'decoder.block.18.layer.0.SelfAttention.o.weight', 'decoder.block.18.layer.1.EncDecAttention.v.weight', 'decoder.block.0.layer.1.layer_norm.weight', 'decoder.block.4.layer.1.EncDecAttention.v.weight', 'decoder.block.4.layer.0.layer_norm.weight', 'decoder.block.1.layer.0.SelfAttention.o.weight', 'decoder.block.2.layer.2.DenseReluDense.wi.weight', 'decoder.block.11.layer.1.EncDecAttention.o.weight', 'decoder.block.18.layer.2.layer_norm.weight', 'decoder.block.1.layer.0.SelfAttention.k.weight', 'decoder.block.19.layer.1.EncDecAttention.k.weight', 'decoder.block.20.layer.2.layer_norm.weight', 'decoder.block.20.layer.2.DenseReluDense.wo.weight', 'decoder.block.15.layer.1.EncDecAttention.v.weight', 'decoder.block.2.layer.1.layer_norm.weight', 'decoder.block.0.layer.1.EncDecAttention.v.weight', 'decoder.block.1.layer.2.layer_norm.weight', 'decoder.block.7.layer.0.SelfAttention.o.weight', 'decoder.block.1.layer.1.EncDecAttention.v.weight', 'decoder.block.23.layer.2.layer_norm.weight', 'decoder.block.22.layer.0.SelfAttention.o.weight', 'decoder.block.17.layer.1.layer_norm.weight', 'decoder.block.18.layer.1.EncDecAttention.q.weight', 'decoder.block.0.layer.1.EncDecAttention.o.weight', 'decoder.block.11.layer.2.layer_norm.weight', 'decoder.block.21.layer.1.EncDecAttention.o.weight', 'decoder.block.22.layer.1.EncDecAttention.k.weight', 'decoder.block.12.layer.2.layer_norm.weight', 'decoder.block.21.layer.2.DenseReluDense.wo.weight', 'decoder.block.22.layer.0.layer_norm.weight', 'decoder.block.1.layer.1.EncDecAttention.o.weight', 'decoder.block.2.layer.1.EncDecAttention.q.weight', 'decoder.block.10.layer.0.SelfAttention.v.weight', 'decoder.block.5.layer.2.layer_norm.weight', 'decoder.block.22.layer.1.EncDecAttention.q.weight', 'decoder.block.14.layer.1.EncDecAttention.q.weight', 'decoder.block.10.layer.1.EncDecAttention.q.weight', 'decoder.block.6.layer.1.layer_norm.weight', 'decoder.block.1.layer.0.SelfAttention.v.weight', 'decoder.block.17.layer.2.layer_norm.weight', 'decoder.block.11.layer.0.SelfAttention.v.weight', 'decoder.block.14.layer.1.layer_norm.weight', 'decoder.block.20.layer.0.SelfAttention.q.weight', 'decoder.block.1.layer.2.DenseReluDense.wi.weight', 'decoder.block.17.layer.1.EncDecAttention.v.weight', 'decoder.block.12.layer.0.SelfAttention.q.weight', 'decoder.block.11.layer.1.EncDecAttention.k.weight', 'decoder.block.17.layer.0.SelfAttention.k.weight', 'decoder.block.18.layer.0.SelfAttention.v.weight', 'decoder.block.13.layer.1.EncDecAttention.k.weight', 'decoder.block.11.layer.0.SelfAttention.q.weight', 'decoder.block.0.layer.0.SelfAttention.k.weight', 'decoder.block.2.layer.0.SelfAttention.q.weight', 'decoder.block.4.layer.0.SelfAttention.k.weight', 'decoder.block.5.layer.0.layer_norm.weight', 'decoder.block.13.layer.1.layer_norm.weight', 'decoder.block.3.layer.0.SelfAttention.v.weight', 'decoder.block.6.layer.1.EncDecAttention.q.weight', 'decoder.block.19.layer.0.SelfAttention.v.weight', 'decoder.block.18.layer.1.layer_norm.weight', 'decoder.block.18.layer.2.DenseReluDense.wi.weight', 'decoder.block.3.layer.1.EncDecAttention.q.weight', 'decoder.block.8.layer.1.EncDecAttention.v.weight', 'decoder.block.3.layer.1.EncDecAttention.o.weight', 'decoder.block.19.layer.1.EncDecAttention.v.weight', 'decoder.block.8.layer.2.DenseReluDense.wo.weight', 'decoder.block.14.layer.0.SelfAttention.q.weight', 'decoder.block.0.layer.0.SelfAttention.relative_attention_bias.weight', 'decoder.block.4.layer.2.DenseReluDense.wi.weight', 'decoder.block.17.layer.2.DenseReluDense.wi.weight', 'decoder.block.8.layer.2.DenseReluDense.wi.weight', 'decoder.block.16.layer.0.layer_norm.weight', 'decoder.block.15.layer.0.layer_norm.weight', 'decoder.block.23.layer.0.SelfAttention.o.weight', 'decoder.block.17.layer.0.SelfAttention.v.weight', 'decoder.block.3.layer.1.EncDecAttention.v.weight', 'decoder.block.3.layer.0.layer_norm.weight', 'decoder.block.8.layer.2.layer_norm.weight', 'decoder.block.23.layer.0.SelfAttention.v.weight', 'decoder.block.0.layer.0.SelfAttention.q.weight', 'decoder.block.9.layer.0.SelfAttention.q.weight', 'decoder.block.17.layer.0.layer_norm.weight', 'decoder.block.23.layer.1.EncDecAttention.o.weight', 'decoder.block.3.layer.1.layer_norm.weight', 'decoder.block.19.layer.1.EncDecAttention.q.weight', 'decoder.block.3.layer.0.SelfAttention.q.weight', 'decoder.block.15.layer.0.SelfAttention.k.weight', 'decoder.block.14.layer.1.EncDecAttention.k.weight', 'decoder.block.14.layer.2.layer_norm.weight', 'decoder.block.23.layer.1.EncDecAttention.k.weight', 'decoder.block.16.layer.1.EncDecAttention.v.weight', 'decoder.block.10.layer.1.EncDecAttention.k.weight', 'decoder.block.5.layer.2.DenseReluDense.wo.weight', 'decoder.block.21.layer.0.SelfAttention.o.weight', 'decoder.block.12.layer.1.EncDecAttention.o.weight', 'decoder.block.16.layer.1.layer_norm.weight', 'decoder.block.1.layer.2.DenseReluDense.wo.weight', 'decoder.block.21.layer.1.EncDecAttention.q.weight', 'decoder.block.5.layer.1.layer_norm.weight', 'decoder.block.13.layer.0.SelfAttention.q.weight', 'decoder.block.22.layer.2.layer_norm.weight', 'decoder.block.10.layer.0.SelfAttention.o.weight', 'decoder.block.10.layer.0.SelfAttention.k.weight', 'decoder.block.7.layer.2.DenseReluDense.wi.weight', 'decoder.block.5.layer.1.EncDecAttention.o.weight', 'decoder.block.1.layer.0.layer_norm.weight', 'decoder.block.4.layer.1.EncDecAttention.o.weight', 'decoder.block.6.layer.0.SelfAttention.o.weight', 'decoder.block.10.layer.2.layer_norm.weight', 'decoder.block.23.layer.1.EncDecAttention.q.weight', 'decoder.block.12.layer.0.SelfAttention.v.weight', 'decoder.block.0.layer.0.SelfAttention.v.weight', 'decoder.block.16.layer.2.DenseReluDense.wi.weight', 'decoder.block.21.layer.2.layer_norm.weight', 'decoder.block.2.layer.1.EncDecAttention.v.weight', 'decoder.block.3.layer.2.DenseReluDense.wo.weight', 'decoder.block.8.layer.0.SelfAttention.q.weight', 'decoder.block.20.layer.0.SelfAttention.k.weight', 'decoder.block.9.layer.1.EncDecAttention.k.weight', 'decoder.block.17.layer.0.SelfAttention.o.weight', 'decoder.block.7.layer.1.EncDecAttention.k.weight', 'decoder.block.9.layer.2.DenseReluDense.wo.weight', 'decoder.block.2.layer.1.EncDecAttention.o.weight', 'decoder.block.15.layer.0.SelfAttention.q.weight', 'decoder.block.10.layer.1.EncDecAttention.v.weight', 'decoder.block.13.layer.1.EncDecAttention.o.weight', 'decoder.block.20.layer.1.layer_norm.weight', 'decoder.block.23.layer.2.DenseReluDense.wi.weight', 'decoder.block.20.layer.1.EncDecAttention.q.weight', 'decoder.block.6.layer.0.SelfAttention.q.weight', 'decoder.block.5.layer.0.SelfAttention.o.weight', 'decoder.block.14.layer.0.SelfAttention.o.weight', 'decoder.block.15.layer.1.EncDecAttention.o.weight', 'decoder.block.7.layer.2.DenseReluDense.wo.weight', 'decoder.block.4.layer.0.SelfAttention.q.weight', 'decoder.block.2.layer.2.layer_norm.weight', 'decoder.block.11.layer.2.DenseReluDense.wi.weight', 'decoder.block.21.layer.0.SelfAttention.q.weight', 'decoder.block.19.layer.2.layer_norm.weight', 'decoder.block.13.layer.0.layer_norm.weight', 'decoder.block.1.layer.1.EncDecAttention.q.weight', 'decoder.block.5.layer.0.SelfAttention.k.weight', 'decoder.block.19.layer.2.DenseReluDense.wi.weight', 'decoder.block.11.layer.0.layer_norm.weight', 'decoder.block.23.layer.1.layer_norm.weight', 'decoder.block.14.layer.0.SelfAttention.v.weight', 'decoder.block.23.layer.0.SelfAttention.k.weight', 'decoder.block.12.layer.1.EncDecAttention.v.weight', 'decoder.block.20.layer.1.EncDecAttention.k.weight', 'decoder.block.15.layer.2.layer_norm.weight', 'decoder.block.7.layer.0.SelfAttention.v.weight', 'decoder.block.10.layer.1.EncDecAttention.o.weight', 'decoder.block.20.layer.0.SelfAttention.v.weight', 'decoder.block.9.layer.1.layer_norm.weight', 'decoder.block.0.layer.1.EncDecAttention.k.weight', 'decoder.block.14.layer.1.EncDecAttention.v.weight', 'decoder.block.14.layer.0.layer_norm.weight', 'decoder.block.12.layer.1.EncDecAttention.q.weight', 'decoder.block.21.layer.0.SelfAttention.v.weight', 'decoder.block.7.layer.0.layer_norm.weight', 'decoder.block.4.layer.0.SelfAttention.o.weight', 'decoder.block.8.layer.0.layer_norm.weight', 'decoder.block.15.layer.2.DenseReluDense.wi.weight', 'decoder.block.10.layer.2.DenseReluDense.wi.weight', 'decoder.block.11.layer.0.SelfAttention.o.weight', 'decoder.block.16.layer.2.DenseReluDense.wo.weight', 'decoder.block.0.layer.2.layer_norm.weight', 'decoder.block.23.layer.2.DenseReluDense.wo.weight', 'decoder.block.20.layer.0.layer_norm.weight', 'decoder.block.13.layer.1.EncDecAttention.q.weight', 'decoder.block.8.layer.1.EncDecAttention.o.weight', 'decoder.block.13.layer.2.DenseReluDense.wo.weight', 'decoder.block.19.layer.2.DenseReluDense.wo.weight', 'decoder.block.6.layer.0.SelfAttention.k.weight', 'decoder.block.14.layer.2.DenseReluDense.wi.weight', 'decoder.block.21.layer.2.DenseReluDense.wi.weight', 'decoder.block.19.layer.0.layer_norm.weight', 'decoder.block.16.layer.1.EncDecAttention.o.weight', 'decoder.block.18.layer.1.EncDecAttention.o.weight', 'decoder.block.9.layer.0.SelfAttention.v.weight', 'decoder.block.10.layer.0.layer_norm.weight', 'decoder.block.12.layer.2.DenseReluDense.wi.weight', 'decoder.block.18.layer.2.DenseReluDense.wo.weight', 'decoder.block.12.layer.2.DenseReluDense.wo.weight', 'decoder.block.5.layer.1.EncDecAttention.k.weight', 'decoder.block.11.layer.1.EncDecAttention.v.weight', 'decoder.block.21.layer.0.layer_norm.weight', 'decoder.block.6.layer.1.EncDecAttention.k.weight', 'decoder.block.0.layer.2.DenseReluDense.wi.weight', 'decoder.block.12.layer.0.layer_norm.weight', 'decoder.block.17.layer.1.EncDecAttention.o.weight', 'decoder.block.1.layer.1.layer_norm.weight', 'decoder.block.13.layer.2.layer_norm.weight', 'decoder.block.18.layer.0.SelfAttention.k.weight', 'decoder.block.14.layer.1.EncDecAttention.o.weight', 'decoder.block.17.layer.2.DenseReluDense.wo.weight', 'decoder.block.11.layer.1.EncDecAttention.q.weight', 'decoder.block.10.layer.0.SelfAttention.q.weight', 'decoder.block.9.layer.0.SelfAttention.o.weight', 'decoder.block.10.layer.2.DenseReluDense.wo.weight', 'decoder.block.22.layer.2.DenseReluDense.wo.weight', 'decoder.block.16.layer.1.EncDecAttention.k.weight', 'decoder.block.20.layer.0.SelfAttention.o.weight', 'decoder.block.6.layer.2.DenseReluDense.wi.weight', 'decoder.block.17.layer.1.EncDecAttention.q.weight', 'decoder.block.8.layer.1.EncDecAttention.k.weight', 'decoder.block.9.layer.1.EncDecAttention.o.weight', 'decoder.block.17.layer.0.SelfAttention.q.weight', 'decoder.block.21.layer.0.SelfAttention.k.weight', 'decoder.block.4.layer.1.layer_norm.weight', 'decoder.block.16.layer.0.SelfAttention.o.weight', 'decoder.block.3.layer.0.SelfAttention.k.weight', 'decoder.block.22.layer.1.EncDecAttention.v.weight', 'decoder.final_layer_norm.weight', 'decoder.block.21.layer.1.EncDecAttention.v.weight', 'decoder.block.8.layer.1.EncDecAttention.q.weight', 'decoder.block.6.layer.0.SelfAttention.v.weight', 'decoder.block.20.layer.1.EncDecAttention.o.weight', 'decoder.block.16.layer.0.SelfAttention.q.weight', 'decoder.block.15.layer.1.layer_norm.weight', 'decoder.block.13.layer.0.SelfAttention.v.weight', 'decoder.block.19.layer.1.EncDecAttention.o.weight', 'decoder.block.15.layer.0.SelfAttention.o.weight', 'decoder.block.15.layer.1.EncDecAttention.q.weight', 'decoder.block.2.layer.0.layer_norm.weight', 'decoder.block.11.layer.2.DenseReluDense.wo.weight', 'decoder.block.12.layer.1.EncDecAttention.k.weight', 'decoder.block.13.layer.0.SelfAttention.o.weight', 'decoder.block.15.layer.0.SelfAttention.v.weight', 'decoder.block.2.layer.0.SelfAttention.k.weight', 'decoder.block.15.layer.2.DenseReluDense.wo.weight', 'decoder.block.16.layer.0.SelfAttention.k.weight', 'decoder.block.18.layer.0.layer_norm.weight', 'decoder.block.4.layer.2.layer_norm.weight', 'decoder.block.17.layer.1.EncDecAttention.k.weight', 'decoder.block.3.layer.0.SelfAttention.o.weight', 'decoder.block.6.layer.1.EncDecAttention.v.weight', 'decoder.block.22.layer.1.layer_norm.weight']\n", "- This IS expected if you are initializing TFT5EncoderModel from a PyTorch model trained on another task or with another architecture (e.g. initializing a TFBertForSequenceClassification model from a BertForPreTraining model).\n", "- This IS NOT expected if you are initializing TFT5EncoderModel from a PyTorch model that you expect to be exactly identical (e.g. initializing a TFBertForSequenceClassification model from a BertForSequenceClassification model).\n", "All the weights of TFT5EncoderModel were initialized from the PyTorch model.\n", "If your task is similar to the task the model of the checkpoint was trained on, you can already use TFT5EncoderModel for predictions without further training.\n"]}], "source": ["model = TFT5EncoderModel.from_pretrained(\"Rostlab/prot_t5_xl_uniref50\", from_pt=True)             \n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "YN6nqLFuY0k2", "outputId": "d08b4b48-c046-46be-e080-9aeb6381ceb1"}, "outputs": [{"data": {"text/plain": ["9"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["gc.collect()"]}, {"cell_type": "markdown", "metadata": {"id": "ZkqAotTcdZnW"}, "source": ["<b>3. Create or load sequences and map rarely occured amino acids (U,Z,O,B) to (X)<b>"]}, {"cell_type": "code", "execution_count": 81, "metadata": {"id": "a0zwKinIdZnX"}, "outputs": [], "source": ["sequences_Example = \"MGEVVRLTNSSTGGPVFVYVKDGKIIRMTPMDFDDAVDAPSWKIEARGKTFTPPRKTSIAPYTAGFKSMIYSDLRIPYPMKRKSFDPNGERNPQLRGAGLSKQDPWSDYERISWDEATDIVVAEINRIKHAYGPSAILSTPSSHHMWGNVGYRHSTYFRFMNMMGFTYADHNPDSWEGWHWGGMHMWGFSWRLGNPEQYDLLEDGLKHAEMIVFWSSDPETNSGIYAGFESNIRRQWLKDLGVDFVFIDPHMNHTARLVADKWFSPKIGTDHALSFAIAYTWLKEDSYDKEYVAANAHGFEEWADYVLGKTDGTPKTCEWAEEESGVPACEIRALARQWAKKNTYLAAGGLGGWGGACRASHGIEWARGMIALATMQGMGKPGSNMWSTTQGVPLDYEFYFPGYAEGGISGDCENSAAGFKFAWRMFDGKTTFPSPSNLNTSAGQHIPRLKIPECIMGGKFQWSGKGFAGGDISHQLHQYEYPAPGYSKIKMFWKYGGPHLGTMTATNRYAKMYTHDSLEFVVSQSIWFEGEVPFADIILPACTNFERWDISEFANCSGYIPDNYQLCNHRVISLQAKCIEPVGESMSDYEIYRLFAKKLNIEEMFSEGKDELAWCEQYFNATDMPKYMTWDEFFKKGYFVVPDNPNRKKTVALRWFAEGREKDTPDWGPRLNNQVCRKGLQTTTGKVEFIATSLKNFEEQGYIDEHRPSMHTYVPAWESQKHSPLAVKYPLGMLSPHPRFSMHTMGDGKNSYMNYIKDHRVEVDGYKYWIMRVNSIDAEARGIKNGDLIRAYNDRGSVILAAQVTECLQPGTVHSYESCAVYDPLGTAGKSADRGGCINILTPDRYISKYACGMANNTALVEIEKWDGDKYEIY\"\n", "seq_split = []\n", "seq_split.append(str(' '.join([word for word in sequences_Example])))"]}, {"cell_type": "code", "execution_count": 82, "metadata": {"id": "EkINwL9DdZna"}, "outputs": [], "source": ["sequences_Example = [re.sub(r\"[UZOB]\", \"X\", sequence) for sequence in seq_split]"]}, {"cell_type": "markdown", "metadata": {"id": "66BZEB3MdZnf"}, "source": ["<b>4. Tokenize, encode sequences and load it into the GPU if possibile<b>"]}, {"cell_type": "code", "execution_count": 83, "metadata": {"id": "xt5uYuu7dZnf"}, "outputs": [], "source": ["ids = tokenizer.batch_encode_plus(sequences_Example, add_special_tokens=True, padding=True, return_tensors=\"tf\")"]}, {"cell_type": "code", "execution_count": 84, "metadata": {"id": "Grl3ieUhdZnj"}, "outputs": [], "source": ["input_ids = ids['input_ids']\n", "attention_mask = ids['attention_mask']"]}, {"cell_type": "markdown", "metadata": {"id": "Zylf1HyBdZnl"}, "source": ["<b>5. Extracting sequences' features and load it into the CPU if needed<b>"]}, {"cell_type": "code", "execution_count": 85, "metadata": {"id": "i8CVGPRFdZnm"}, "outputs": [], "source": ["embedding = model(input_ids)"]}, {"cell_type": "code", "execution_count": 86, "metadata": {"id": "P8IsEs35T0T4"}, "outputs": [], "source": ["embedding = np.asarray(embedding.last_hidden_state)"]}, {"cell_type": "code", "execution_count": 87, "metadata": {"id": "7Q2AGxlCUrot"}, "outputs": [], "source": ["attention_mask = np.asarray(attention_mask)"]}, {"cell_type": "markdown", "metadata": {"id": "R6oeRZ7xdZns"}, "source": ["<b>7. Remove padding (\\<pad\\>) and special tokens (\\</s\\>) that is added by ProtT5-XL-UniRef50 model<b>"]}, {"cell_type": "code", "execution_count": 88, "metadata": {"id": "1XXoVSPDdZns"}, "outputs": [], "source": ["features = [] \n", "for seq_num in range(len(embedding)):\n", "    seq_len = (attention_mask[seq_num] == 1).sum()\n", "    seq_emd = embedding[seq_num][:seq_len-1]\n", "    features.append(seq_emd)"]}, {"cell_type": "code", "execution_count": 92, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(1, 875, 1024)\n"]}], "source": ["print(np.array(features).shape)\n", "np.save('./ProtT5.npy', np.array(features))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"accelerator": "GPU", "colab": {"authorship_tag": "ABX9TyMgChqZstgL/hnHnxzjUvZ4", "include_colab_link": true, "machine_shape": "hm", "name": "ProtT5-XL-BFD.ipynb", "provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.9"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"01b49e8c145b4a8bae89d8430e3fbdc6": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "039fedc16a9e417c94beea59f5561d1a": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "04585a7bab58488990342e4081dfdd51": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "06c281c8f6b54433beeee404a0cdcc69": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "Downloading: 100%", "description_tooltip": null, "layout": "IPY_MODEL_88ab217893134f6997a317264c676064", "max": 24, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_98ef6cdfca424b74b0f8cbc51630e0a8", "value": 24}}, "19b60494fe2443698785e34808f2027e": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1a4ffa8252bb4b5584ad7bdd581cb6da": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_982e1e391da849d992f28a7c8ea473fb", "IPY_MODEL_5494f8dece5c4a10b5f6af426a760067"], "layout": "IPY_MODEL_ebac9c962e824d7794c40ac4d0332b89"}}, "203fa60c4f4a49a38b9aaab941c863e9": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "255336e889924999b3378102cc007c95": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "32856cc01ae248b98dc4527034ae3398": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_255336e889924999b3378102cc007c95", "placeholder": "​", "style": "IPY_MODEL_203fa60c4f4a49a38b9aaab941c863e9", "value": " 24.0/24.0 [00:00&lt;00:00, 24.8B/s]"}}, "5494f8dece5c4a10b5f6af426a760067": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_b7dfd3ea823c46a9b65bd8cc7bad793d", "placeholder": "​", "style": "IPY_MODEL_04585a7bab58488990342e4081dfdd51", "value": " 7.44G/11.3G [03:23&lt;01:35, 40.2MB/s]"}}, "6039c46157134a998e9e2b1ccec25b63": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_8b06d496d4704beebea534ddb5ad2330", "IPY_MODEL_e699ece0feca440387d082c2b233b58d"], "layout": "IPY_MODEL_71af1636b5f840a2a07324812235db10"}}, "625c498af5e54243a74d0e1442d76448": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_856432cdffae474d8e8c6fafd79a3937", "IPY_MODEL_a8b5fc22711745f99e61bed9e834872e"], "layout": "IPY_MODEL_d5348e015a184d4db05dc48e1581ef44"}}, "650ed09b93e84503a78917000039254a": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_06c281c8f6b54433beeee404a0cdcc69", "IPY_MODEL_32856cc01ae248b98dc4527034ae3398"], "layout": "IPY_MODEL_947c7ac18bae4d72a942fe114df012d9"}}, "662dd8a497d64ee8b310197e72d66977": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "71af1636b5f840a2a07324812235db10": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "763e98c23a574dc2bf451adbbc58a9a4": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "7a63fb9f3a2d4fc3b74c85fb7fcbcf9f": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "7e2c2223c67042f9ba2a1b62ac908f18": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": "initial"}}, "856432cdffae474d8e8c6fafd79a3937": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "Downloading: 100%", "description_tooltip": null, "layout": "IPY_MODEL_e2a70fb106874689be06dacfa4d6d306", "max": 237990, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_ff459b2aa7114caca0d1ef25bf6e1790", "value": 237990}}, "88ab217893134f6997a317264c676064": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8b06d496d4704beebea534ddb5ad2330": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "Downloading: 100%", "description_tooltip": null, "layout": "IPY_MODEL_19b60494fe2443698785e34808f2027e", "max": 546, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_b056a2433f154dbaa41f6840015ecf87", "value": 546}}, "8cde50d61e224d50aaffb55ad8323680": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "947c7ac18bae4d72a942fe114df012d9": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "982e1e391da849d992f28a7c8ea473fb": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "", "description": "Downloading:  38%", "description_tooltip": null, "layout": "IPY_MODEL_8cde50d61e224d50aaffb55ad8323680", "max": 11275562628, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_d885ef1286954116aabbcdca34303124", "value": 7441550336}}, "98ef6cdfca424b74b0f8cbc51630e0a8": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": "initial"}}, "a79c843eaaeb4df8a46f7c077a09899f": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "a8b5fc22711745f99e61bed9e834872e": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_ad3d15cf7b07413588c5f3d80288288a", "placeholder": "​", "style": "IPY_MODEL_763e98c23a574dc2bf451adbbc58a9a4", "value": " 238k/238k [00:00&lt;00:00, 250kB/s]"}}, "a943933f18a64c6d96a0248a5ce9bfd6": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_662dd8a497d64ee8b310197e72d66977", "placeholder": "​", "style": "IPY_MODEL_a79c843eaaeb4df8a46f7c077a09899f", "value": " 1.79k/1.79k [00:01&lt;00:00, 1.27kB/s]"}}, "ad3d15cf7b07413588c5f3d80288288a": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b056a2433f154dbaa41f6840015ecf87": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": "initial"}}, "b21c919fb3944acf815c975826688eca": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_d3e4ec9dec5a471881c66ea6f32a142b", "IPY_MODEL_a943933f18a64c6d96a0248a5ce9bfd6"], "layout": "IPY_MODEL_01b49e8c145b4a8bae89d8430e3fbdc6"}}, "b7dfd3ea823c46a9b65bd8cc7bad793d": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d3e4ec9dec5a471881c66ea6f32a142b": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "Downloading: 100%", "description_tooltip": null, "layout": "IPY_MODEL_e91f71f474b74f39a5fb1155741a8d74", "max": 1786, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_7e2c2223c67042f9ba2a1b62ac908f18", "value": 1786}}, "d5348e015a184d4db05dc48e1581ef44": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d885ef1286954116aabbcdca34303124": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": "initial"}}, "e2a70fb106874689be06dacfa4d6d306": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e699ece0feca440387d082c2b233b58d": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_039fedc16a9e417c94beea59f5561d1a", "placeholder": "​", "style": "IPY_MODEL_7a63fb9f3a2d4fc3b74c85fb7fcbcf9f", "value": " 546/546 [00:00&lt;00:00, 1.71kB/s]"}}, "e91f71f474b74f39a5fb1155741a8d74": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ebac9c962e824d7794c40ac4d0332b89": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ff459b2aa7114caca0d1ef25bf6e1790": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": "initial"}}}}}, "nbformat": 4, "nbformat_minor": 0}